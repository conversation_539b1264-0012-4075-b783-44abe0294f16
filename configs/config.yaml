# 交易系统配置文件

# 监控系统配置（复用现有的）
monitor:
  check_interval: 10s
  telegram:
    enabled: true
    bot_token: "**********************************************"
    chat_id: "-1001768189255"


# 套利协调器配置
arbitrage:
  # 机会通道缓冲区大小
  opportunity_buffer_size: 100
  # 机会去重时间窗口（分钟）
  deduplication_window_minutes: 5
  # 最大同时处理的机会数
  max_active_opportunities: 10
  # 处理超时时间
  process_timeout: 5m
  # 优先级队列最大大小
  priority_queue_size: 1000
  # HandleOpportunity 超时时间
  handle_opportunity_timeout: 5s

# 钱包配置
wallet:
  # 私钥存储路径（加密存储）
  keystore_path: "./keystore"
  # 是否在启动时提示输入密码（适合24/7运行）
  enable_password_prompt: true

# 交易所配置（支持 binance, okx, bybit, coinbase）
exchange:
  # 交易所类型
  type: binance
  # 是否使用测试网
  test_net: false
  # 缓存有效期
  cache_ttl: 5m
  # 请求间隔（限流）
  request_interval: 100ms
  # API密钥从环境变量读取: EXCHANGE_API_KEY, EXCHANGE_API_SECRET
  # 提现白名单
  withdraw_whitelist:
    - "YOUR_WALLET_ADDRESS"

# 资金管理配置
funding:
  # 最小储备金
  min_reserves:
    ethereum: 0.05
    bsc: 0.01
    polygon: 10.0
    arbitrum: 0.01
    optimism: 0.01
    avalanche: 0.5
  # 提现阈值
  withdraw_threshold: 0.8
  # 再平衡间隔
  rebalance_interval: 1h
  # 最大提现金额
  max_withdraw_amount: 10000.0

# 风险管理配置
risk:
  # 单笔交易限额（USD）
  max_single_trade: 10000.0
  # 每日交易总额限制（USD）
  max_daily_volume: 100000.0
  # 最大允许滑点（0-1之间）
  max_slippage: 0.02
  # 代币白名单
  token_whitelist:
  # 链白名单
  chain_whitelist:
  # 最小奖励阈值（USD）
  min_reward_threshold: 10.0

# 数据库配置
database:
  # SQLite 数据库路径
  path: "./data/trades.db"
  # 最大打开连接数
  max_open_conns: 25
  # 最大空闲连接数
  max_idle_conns: 5
  # 连接最大生命周期
  conn_max_lifetime: 1h

# 健康检查配置
health:
  # 是否启用健康检查
  enabled: true
  # 健康检查端口
  port: 8081
  # 健康检查路径
  path: "/health"

chains:
  ethereum:
    chain_id: 1337
    endpoint_id: 30101
    check_interval: 5s
    rpc_urls:
      - "http://127.0.0.1:8545"
      # - "https://rpc.ankr.com/eth/50db7292c9a3b31111416e514a88e6e3f155e36e3060908a34386fe9d92f4832"
    # wss_url: "wss://127.0.0.1:8546"
    wss_url: "wss://mainnet.infura.io/ws/v3/********************************"
    tokens:
      USDC:
        address: "******************************************"
        decimals: 6
      USDT:
        address: "******************************************"
        decimals: 6
    stargate:
      pools:
        # ETH: "******************************************"
        USDC: "******************************************"
        USDT: "******************************************"
        # mETH: "******************************************"
      fee_libs:
        # ETH: "******************************************"
        USDC: "******************************************"
        USDT: "******************************************"
        # mETH: "******************************************"

  # arbitrum:
  #   chain_id: 42161
  #   endpoint_id: 30110
  #   check_interval: 5s
  #   rpc_urls:
  #     - "https://rpc.ankr.com/arbitrum/50db7292c9a3b31111416e514a88e6e3f155e36e3060908a34386fe9d92f4832"
  #   # wss_url: "wss://arb-mainnet.g.alchemy.com/v2/YOUR_API_KEY"
  #   tokens:
  #     USDC:
  #       address: "******************************************"
  #       decimals: 6
  #     USDT:
  #       address: "******************************************"
  #       decimals: 6
  #   stargate:
  #     pools:
  #       # ETH: "******************************************"
  #       USDC: "******************************************"
  #       USDT: "******************************************"
  #     fee_libs:
  #       # ETH: "******************************************"
  #       USDC: "******************************************"
  #       USDT: "******************************************"

  # optimism:
  #   chain_id: 10
  #   endpoint_id: 30111
  #   check_interval: 5s
  #   rpc_urls:
  #     - "https://rpc.ankr.com/optimism/50db7292c9a3b31111416e514a88e6e3f155e36e3060908a34386fe9d92f4832"
  #   tokens:
  #     USDC:
  #       address: "******************************************"
  #       decimals: 6
  #     USDT:
  #       address: "******************************************"
  #       decimals: 6
  #   stargate:
  #     pools:
  #       # ETH: "******************************************"
  #       USDC: "******************************************"
  #       USDT: "******************************************"
  #     fee_libs:
  #       # ETH: "******************************************"
  #       USDC: "******************************************"
  #       USDT: "******************************************"

  base:
    chain_id: 8453
    endpoint_id: 30184
    check_interval: 5s
    rpc_urls:
      - "https://rpc.ankr.com/base/50db7292c9a3b31111416e514a88e6e3f155e36e3060908a34386fe9d92f4832"
    tokens:
      USDC:
        address: "******************************************"
        decimals: 6
    stargate:
      pools:
        # ETH: "******************************************"
        USDC: "******************************************"
      fee_libs:
        # ETH: "******************************************"
        USDC: "******************************************"

  avalanche:
    chain_id: 43114
    endpoint_id: 30106
    check_interval: 10s
    rpc_urls:
      - "https://rpc.ankr.com/avalanche/50db7292c9a3b31111416e514a88e6e3f155e36e3060908a34386fe9d92f4832"
    tokens:
      USDC:
        address: "******************************************"
        decimals: 6
      USDT:
        address: "******************************************"
        decimals: 6
    stargate:
      pools:
        USDC: "******************************************"
        USDT: "******************************************"
      fee_libs:
        USDC: "******************************************"
        USDT: "******************************************"

  # polygon:
  #   chain_id: 137
  #   endpoint_id: 30109
  #   check_interval: 10s
  #   rpc_urls:
  #     - "https://rpc.ankr.com/polygon/50db7292c9a3b31111416e514a88e6e3f155e36e3060908a34386fe9d92f4832"
  #   tokens:
  #     USDC:
  #       address: "0x3c499c542cEF5E3811e1192ce70d8cC03d5c3359"
  #       decimals: 6
  #     USDT:
  #       address: "0xc2132D05D31c914a87C6611C10748AEb04B58e8F"
  #       decimals: 6
  #   stargate:
  #     pools:
  #       USDC: "0x9Aa02D4Fae7F58b8E8f34c66E756cC734DAc7fe4"
  #       USDT: "0xd47b03ee6d86Cf251ee7860FB2ACf9f91B9fD4d7"
  #     fee_libs:
  #       USDC: "0x3Fc69CC4A842838bCDC9499178740226062b14E4"
  #       USDT: "0x4e422B0aCb2Bd7e3aC70B5c0E5eb806e86a94038"

  # bnb:
  #   chain_id: 56
  #   endpoint_id: 30102
  #   check_interval: 30s
  #   rpc_urls:
  #     - "https://rpc.ankr.com/bsc/50db7292c9a3b31111416e514a88e6e3f155e36e3060908a34386fe9d92f4832"
  #   tokens:
  #     USDC:
  #       address: "0x8AC76a51cc950d9822D68b83fE1Ad97B32Cd580d"
  #       decimals: 18
  #     USDT:
  #       address: "0x55d398326f99059fF775485246999027B3197955"
  #       decimals: 18
  #   stargate:
  #     pools:
  #       USDC: "0x962Bd449E630b0d928f308Ce63f1A21F02576057"
  #       USDT: "0x138EB30f73BC423c6455C53df6D89CB01d9eBc63"
  #     fee_libs:
  #       USDC: "******************************************"
  #       USDT: "******************************************"

  # sei:
  #   chain_id: 1329
  #   endpoint_id: 30280
  #   check_interval: 30s
  #   rpc_urls:
  #     - "https://evm-rpc.sei-apis.com"
  #   tokens:
  #     USDC:
  #       address: "******************************************"
  #       decimals: 6
  #     USDT:
  #       address: "******************************************"
  #       decimals: 6
  #   stargate:
  #     pools:
  #       # ETH: "******************************************"
  #       USDC: "******************************************"
  #       USDT: "******************************************"
  #     fee_libs:
  #       # ETH: "******************************************"
  #       USDC: "******************************************"
  #       USDT: "******************************************"

  # metis:
  #   chain_id: 1088
  #   endpoint_id: 30151
  #   check_interval: 30s
  #   rpc_urls:
  #     - "https://metis-mainnet.public.blastapi.io"
  #   tokens:
  #     USDT:
  #       address: "******************************************"
  #       decimals: 6
  #   stargate:
  #     pools:
  #       # ETH: "******************************************"
  #       USDT: "******************************************"
  #     fee_libs:
  #       # ETH: "******************************************"
  #       USDT: "******************************************"

  # kava:
  #   chain_id: 2222
  #   endpoint_id: 30177
  #   check_interval: 30s
  #   rpc_urls:
  #     - "https://evm.kava.io"
  #   tokens:
  #     USDT:
  #       address: "******************************************"
  #       decimals: 6
  #   stargate:
  #     pools:
  #       USDT: "******************************************"
  #     fee_libs:
  #       USDT: "******************************************"

package errors

import (
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestAppError_Basic(t *testing.T) {
	// 测试基本错误创建
	err := New(ErrTypeValidation, ErrCodeInvalidInput, "invalid email format")
	
	assert.NotNil(t, err)
	assert.Equal(t, ErrTypeValidation, err.Type)
	assert.Equal(t, ErrCodeInvalidInput, err.Code)
	assert.Equal(t, "invalid email format", err.Message)
	assert.NotEmpty(t, err.Stack)
	assert.False(t, err.Timestamp.IsZero())
}

func TestAppError_WithDetails(t *testing.T) {
	// 测试添加详细信息
	err := New(ErrTypeValidation, ErrCodeInvalidInput, "invalid input").
		WithDetail("field", "email").
		WithDetail("value", "not-an-email").
		WithRequestID("req-123")
	
	assert.Equal(t, "email", err.Details["field"])
	assert.Equal(t, "not-an-email", err.Details["value"])
	assert.Equal(t, "req-123", err.RequestID)
}

func TestAppError_Wrap(t *testing.T) {
	// 测试错误包装
	originalErr := fmt.Errorf("connection refused")
	wrappedErr := Wrap(originalErr, "failed to connect to database")
	
	assert.NotNil(t, wrappedErr)
	assert.Equal(t, ErrTypeInternal, wrappedErr.Type)
	assert.Contains(t, wrappedErr.Message, "failed to connect to database")
	assert.Equal(t, originalErr, wrappedErr.Cause)
	assert.Equal(t, "connection refused", wrappedErr.CauseMsg)
}

func TestAppError_WrapNil(t *testing.T) {
	// 测试包装nil错误
	err := Wrap(nil, "should be nil")
	assert.Nil(t, err)
	
	err = WrapWithType(nil, ErrTypeNetwork, ErrCodeConnectionFailed, "should be nil")
	assert.Nil(t, err)
}

func TestAppError_WrapAppError(t *testing.T) {
	// 测试包装AppError
	originalErr := New(ErrTypeNetwork, ErrCodeConnectionFailed, "network error").
		WithDetail("host", "example.com")
	
	wrappedErr := Wrap(originalErr, "operation failed")
	
	assert.Contains(t, wrappedErr.Message, "operation failed")
	assert.Contains(t, wrappedErr.Message, "network error")
	assert.Equal(t, "example.com", wrappedErr.Details["host"])
}

func TestAppError_IsRetryable(t *testing.T) {
	tests := []struct {
		name     string
		err      *AppError
		expected bool
	}{
		{
			name:     "network error is retryable",
			err:      New(ErrTypeNetwork, ErrCodeConnectionFailed, "connection failed"),
			expected: true,
		},
		{
			name:     "timeout error is retryable",
			err:      New(ErrTypeTimeout, "TIMEOUT", "operation timed out"),
			expected: true,
		},
		{
			name:     "rate limit error is retryable",
			err:      New(ErrTypeRateLimit, "RATE_LIMITED", "too many requests"),
			expected: true,
		},
		{
			name:     "validation error is not retryable",
			err:      New(ErrTypeValidation, ErrCodeInvalidInput, "invalid input"),
			expected: false,
		},
		{
			name:     "insufficient funds is not retryable",
			err:      New(ErrTypeInsufficientFunds, ErrCodeInsufficientBalance, "not enough balance"),
			expected: false,
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equal(t, tt.expected, tt.err.IsRetryable())
		})
	}
}

func TestAppError_JSON(t *testing.T) {
	// 测试JSON序列化
	err := New(ErrTypeValidation, ErrCodeInvalidInput, "invalid input").
		WithDetail("field", "email").
		WithRequestID("req-123")
	
	data, jsonErr := json.Marshal(err)
	require.NoError(t, jsonErr)
	
	var decoded map[string]interface{}
	require.NoError(t, json.Unmarshal(data, &decoded))
	
	assert.Equal(t, "VALIDATION", decoded["type"])
	assert.Equal(t, "INVALID_INPUT", decoded["code"])
	assert.Equal(t, "invalid input", decoded["message"])
	assert.Equal(t, "req-123", decoded["request_id"])
	assert.Equal(t, false, decoded["is_retryable"])
	
	details := decoded["details"].(map[string]interface{})
	assert.Equal(t, "email", details["field"])
}

func TestErrorBuilder(t *testing.T) {
	// 测试错误构建器
	err := NewBuilder(ErrTypeNetwork, ErrCodeConnectionFailed).
		Message("failed to connect to %s:%d", "localhost", 5432).
		Cause(fmt.Errorf("connection refused")).
		Detail("host", "localhost").
		Detail("port", 5432).
		RequestID("req-456").
		Build()
	
	assert.Equal(t, ErrTypeNetwork, err.Type)
	assert.Equal(t, ErrCodeConnectionFailed, err.Code)
	assert.Equal(t, "failed to connect to localhost:5432", err.Message)
	assert.Equal(t, "localhost", err.Details["host"])
	assert.Equal(t, 5432, err.Details["port"])
	assert.Equal(t, "req-456", err.RequestID)
	assert.NotNil(t, err.Cause)
	assert.NotEmpty(t, err.Stack)
}

func TestConvenienceFunctions(t *testing.T) {
	t.Run("ValidationError", func(t *testing.T) {
		err := ValidationError("email", "not-an-email", "invalid format")
		assert.Equal(t, ErrTypeValidation, err.Type)
		assert.Equal(t, ErrCodeInvalidInput, err.Code)
		assert.Contains(t, err.Message, "email")
		assert.Contains(t, err.Message, "invalid format")
		assert.Equal(t, "email", err.Details["field"])
	})
	
	t.Run("NetworkError", func(t *testing.T) {
		cause := fmt.Errorf("connection refused")
		err := NetworkError("GET", "http://example.com", cause)
		assert.Equal(t, ErrTypeNetwork, err.Type)
		assert.Equal(t, ErrCodeConnectionFailed, err.Code)
		assert.Contains(t, err.Message, "GET")
		assert.Contains(t, err.Message, "http://example.com")
		assert.Equal(t, cause, err.Cause)
	})
	
	t.Run("InsufficientFundsError", func(t *testing.T) {
		err := InsufficientFundsError("ethereum", "USDT", "1000", "500")
		assert.Equal(t, ErrTypeInsufficientFunds, err.Type)
		assert.Equal(t, ErrCodeInsufficientBalance, err.Code)
		assert.Contains(t, err.Message, "USDT")
		assert.Contains(t, err.Message, "ethereum")
		assert.Equal(t, "1000", err.Details["required"])
		assert.Equal(t, "500", err.Details["available"])
	})
	
	t.Run("TimeoutError", func(t *testing.T) {
		err := TimeoutError("database query", 30*time.Second)
		assert.Equal(t, ErrTypeTimeout, err.Type)
		assert.Contains(t, err.Message, "database query")
		assert.Contains(t, err.Message, "30s")
	})
	
	t.Run("ConfigurationError", func(t *testing.T) {
		err := ConfigurationError("database", "missing connection string")
		assert.Equal(t, ErrTypeConfiguration, err.Type)
		assert.Contains(t, err.Message, "database")
		assert.Contains(t, err.Message, "missing connection string")
	})
	
	t.Run("NotFoundError", func(t *testing.T) {
		err := NotFoundError("user", "user-123")
		assert.Equal(t, ErrTypeNotFound, err.Type)
		assert.Contains(t, err.Message, "user")
		assert.Contains(t, err.Message, "user-123")
	})
}

func TestErrorChecking(t *testing.T) {
	validationErr := ValidationError("field", "value", "reason")
	networkErr := NetworkError("GET", "url", fmt.Errorf("failed"))
	
	t.Run("IsType", func(t *testing.T) {
		assert.True(t, IsType(validationErr, ErrTypeValidation))
		assert.False(t, IsType(validationErr, ErrTypeNetwork))
		assert.True(t, IsType(networkErr, ErrTypeNetwork))
		assert.False(t, IsType(nil, ErrTypeValidation))
	})
	
	t.Run("IsCode", func(t *testing.T) {
		assert.True(t, IsCode(validationErr, ErrCodeInvalidInput))
		assert.False(t, IsCode(validationErr, ErrCodeConnectionFailed))
		assert.True(t, IsCode(networkErr, ErrCodeConnectionFailed))
		assert.False(t, IsCode(nil, ErrCodeInvalidInput))
	})
	
	t.Run("AsAppError", func(t *testing.T) {
		appErr, ok := AsAppError(validationErr)
		assert.True(t, ok)
		assert.NotNil(t, appErr)
		
		normalErr := fmt.Errorf("normal error")
		appErr, ok = AsAppError(normalErr)
		assert.False(t, ok)
		assert.Nil(t, appErr)
		
		appErr, ok = AsAppError(nil)
		assert.False(t, ok)
		assert.Nil(t, appErr)
	})
	
	t.Run("IsRetryable", func(t *testing.T) {
		assert.False(t, IsRetryable(validationErr))
		assert.True(t, IsRetryable(networkErr))
		assert.False(t, IsRetryable(fmt.Errorf("normal error")))
		assert.False(t, IsRetryable(nil))
	})
	
	t.Run("GetType", func(t *testing.T) {
		assert.Equal(t, ErrTypeValidation, GetType(validationErr))
		assert.Equal(t, ErrTypeNetwork, GetType(networkErr))
		assert.Equal(t, ErrTypeInternal, GetType(fmt.Errorf("normal error")))
		assert.Equal(t, ErrorType(""), GetType(nil))
	})
	
	t.Run("GetCode", func(t *testing.T) {
		assert.Equal(t, ErrCodeInvalidInput, GetCode(validationErr))
		assert.Equal(t, ErrCodeConnectionFailed, GetCode(networkErr))
		assert.Equal(t, ErrorCode("UNKNOWN"), GetCode(fmt.Errorf("normal error")))
		assert.Equal(t, ErrorCode(""), GetCode(nil))
	})
}

func TestAppError_Error(t *testing.T) {
	// 测试Error()方法
	err1 := New(ErrTypeValidation, ErrCodeInvalidInput, "invalid input")
	assert.Equal(t, "INVALID_INPUT: invalid input", err1.Error())
	
	cause := fmt.Errorf("original error")
	err2 := Wrap(cause, "wrapped error")
	assert.Contains(t, err2.Error(), "wrapped error")
	assert.Contains(t, err2.Error(), "original error")
}

func TestAppError_Unwrap(t *testing.T) {
	// 测试Unwrap方法
	cause := fmt.Errorf("original error")
	wrapped := Wrap(cause, "wrapped error")
	
	assert.Equal(t, cause, wrapped.Unwrap())
}

func TestCaptureStack(t *testing.T) {
	// 测试堆栈捕获
	err := New(ErrTypeInternal, "TEST", "test error")
	
	assert.NotEmpty(t, err.Stack)
	assert.True(t, len(err.Stack) > 0)
	
	// 检查堆栈包含当前测试函数
	foundTestFunc := false
	for _, frame := range err.Stack {
		if contains(frame, "TestCaptureStack") {
			foundTestFunc = true
			break
		}
	}
	assert.True(t, foundTestFunc, "Stack should contain test function")
}

// 辅助函数
func contains(s, substr string) bool {
	return len(s) >= len(substr) && s[len(s)-len(substr):] == substr || len(s) > len(substr) && s[:len(s)-len(substr)] != s[1:len(s)-len(substr)+1]
}
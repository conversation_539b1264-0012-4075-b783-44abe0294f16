package errors

import (
	"encoding/json"
	"fmt"
	"runtime"
	"time"
)

// ErrorType 定义错误类型
type ErrorType string

const (
	// ErrTypeValidation 验证错误
	ErrTypeValidation ErrorType = "VALIDATION"
	// ErrTypeNetwork 网络错误
	ErrTypeNetwork ErrorType = "NETWORK"
	// ErrTypeInsufficientFunds 资金不足
	ErrTypeInsufficientFunds ErrorType = "INSUFFICIENT_FUNDS"
	// ErrTypeRateLimit 限流错误
	ErrTypeRateLimit ErrorType = "RATE_LIMIT"
	// ErrTypeTimeout 超时错误
	ErrTypeTimeout ErrorType = "TIMEOUT"
	// ErrTypeNotFound 资源未找到
	ErrTypeNotFound ErrorType = "NOT_FOUND"
	// ErrTypeConflict 冲突错误
	ErrTypeConflict ErrorType = "CONFLICT"
	// ErrTypeInternal 内部错误
	ErrTypeInternal ErrorType = "INTERNAL"
	// ErrTypeExternal 外部服务错误
	ErrTypeExternal ErrorType = "EXTERNAL"
	// ErrTypeConfiguration 配置错误
	ErrTypeConfiguration ErrorType = "CONFIGURATION"
)

// ErrorCode 定义具体错误码
type ErrorCode string

const (
	// 验证错误码
	ErrCodeInvalidInput      ErrorCode = "INVALID_INPUT"
	ErrCodeMissingField      ErrorCode = "MISSING_FIELD"
	ErrCodeInvalidFormat     ErrorCode = "INVALID_FORMAT"
	ErrCodeOutOfRange        ErrorCode = "OUT_OF_RANGE"
	
	// 资金错误码
	ErrCodeInsufficientBalance ErrorCode = "INSUFFICIENT_BALANCE"
	ErrCodeInsufficientGas     ErrorCode = "INSUFFICIENT_GAS"
	ErrCodeInsufficientLiquidity ErrorCode = "INSUFFICIENT_LIQUIDITY"
	
	// 网络错误码
	ErrCodeConnectionFailed ErrorCode = "CONNECTION_FAILED"
	ErrCodeRequestFailed    ErrorCode = "REQUEST_FAILED"
	ErrCodeResponseInvalid  ErrorCode = "RESPONSE_INVALID"
	
	// 交易错误码
	ErrCodeTransactionFailed   ErrorCode = "TRANSACTION_FAILED"
	ErrCodeTransactionReverted ErrorCode = "TRANSACTION_REVERTED"
	ErrCodeNonceTooLow        ErrorCode = "NONCE_TOO_LOW"
	ErrCodeGasPriceTooLow     ErrorCode = "GAS_PRICE_TOO_LOW"
)

// AppError 应用错误结构
type AppError struct {
	Type      ErrorType              `json:"type"`
	Code      ErrorCode              `json:"code"`
	Message   string                 `json:"message"`
	Details   map[string]interface{} `json:"details,omitempty"`
	Cause     error                  `json:"-"`
	CauseMsg  string                 `json:"cause,omitempty"`
	Stack     []string               `json:"stack,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
	RequestID string                 `json:"request_id,omitempty"`
}

// Error 实现 error 接口
func (e *AppError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("%s: %s (caused by: %v)", e.Code, e.Message, e.Cause)
	}
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

// Unwrap 支持 errors.Is 和 errors.As
func (e *AppError) Unwrap() error {
	return e.Cause
}

// WithDetail 添加详细信息
func (e *AppError) WithDetail(key string, value interface{}) *AppError {
	if e.Details == nil {
		e.Details = make(map[string]interface{})
	}
	e.Details[key] = value
	return e
}

// WithRequestID 添加请求ID
func (e *AppError) WithRequestID(requestID string) *AppError {
	e.RequestID = requestID
	return e
}

// IsRetryable 判断错误是否可重试
func (e *AppError) IsRetryable() bool {
	switch e.Type {
	case ErrTypeNetwork, ErrTypeTimeout, ErrTypeRateLimit:
		return true
	case ErrTypeInternal:
		// 某些内部错误可能可以重试
		switch e.Code {
		case ErrCodeConnectionFailed:
			return true
		}
	}
	return false
}

// MarshalJSON 自定义JSON序列化
func (e *AppError) MarshalJSON() ([]byte, error) {
	type Alias AppError
	return json.Marshal(&struct {
		*Alias
		IsRetryable bool `json:"is_retryable"`
	}{
		Alias:       (*Alias)(e),
		IsRetryable: e.IsRetryable(),
	})
}

// New 创建新的应用错误
func New(errType ErrorType, code ErrorCode, message string) *AppError {
	return &AppError{
		Type:      errType,
		Code:      code,
		Message:   message,
		Timestamp: time.Now(),
		Stack:     captureStack(2), // Skip New and captureStack
	}
}

// Wrap 包装现有错误
func Wrap(err error, message string) *AppError {
	if err == nil {
		return nil
	}
	
	// 如果已经是 AppError，保留原始信息
	if appErr, ok := err.(*AppError); ok {
		newErr := *appErr
		newErr.Message = message + ": " + appErr.Message
		newErr.Stack = captureStack(2)
		return &newErr
	}
	
	return &AppError{
		Type:      ErrTypeInternal,
		Code:      "WRAPPED_ERROR",
		Message:   message,
		Cause:     err,
		CauseMsg:  err.Error(),
		Timestamp: time.Now(),
		Stack:     captureStack(2),
	}
}

// WrapWithType 使用特定类型包装错误
func WrapWithType(err error, errType ErrorType, code ErrorCode, message string) *AppError {
	if err == nil {
		return nil
	}
	
	appErr := &AppError{
		Type:      errType,
		Code:      code,
		Message:   message,
		Cause:     err,
		CauseMsg:  err.Error(),
		Timestamp: time.Now(),
		Stack:     captureStack(2),
	}
	
	// 如果原始错误是 AppError，继承其详情
	if origErr, ok := err.(*AppError); ok {
		if origErr.Details != nil {
			appErr.Details = make(map[string]interface{})
			for k, v := range origErr.Details {
				appErr.Details[k] = v
			}
		}
		appErr.RequestID = origErr.RequestID
	}
	
	return appErr
}

// captureStack 捕获调用栈
func captureStack(skip int) []string {
	stack := make([]string, 0, 10)
	for i := skip; i < skip+10; i++ {
		pc, file, line, ok := runtime.Caller(i)
		if !ok {
			break
		}
		
		fn := runtime.FuncForPC(pc)
		if fn == nil {
			continue
		}
		
		stack = append(stack, fmt.Sprintf("%s:%d %s", file, line, fn.Name()))
	}
	return stack
}

// ErrorBuilder 错误构建器
type ErrorBuilder struct {
	err *AppError
}

// NewBuilder 创建错误构建器
func NewBuilder(errType ErrorType, code ErrorCode) *ErrorBuilder {
	return &ErrorBuilder{
		err: &AppError{
			Type:      errType,
			Code:      code,
			Timestamp: time.Now(),
			Details:   make(map[string]interface{}),
		},
	}
}

// Message 设置错误消息
func (b *ErrorBuilder) Message(format string, args ...interface{}) *ErrorBuilder {
	b.err.Message = fmt.Sprintf(format, args...)
	return b
}

// Cause 设置原因错误
func (b *ErrorBuilder) Cause(err error) *ErrorBuilder {
	b.err.Cause = err
	if err != nil {
		b.err.CauseMsg = err.Error()
	}
	return b
}

// Detail 添加详情
func (b *ErrorBuilder) Detail(key string, value interface{}) *ErrorBuilder {
	b.err.Details[key] = value
	return b
}

// RequestID 设置请求ID
func (b *ErrorBuilder) RequestID(id string) *ErrorBuilder {
	b.err.RequestID = id
	return b
}

// Build 构建错误
func (b *ErrorBuilder) Build() *AppError {
	b.err.Stack = captureStack(2)
	return b.err
}

// 便捷函数

// ValidationError 创建验证错误
func ValidationError(field string, value interface{}, reason string) *AppError {
	return NewBuilder(ErrTypeValidation, ErrCodeInvalidInput).
		Message("validation failed for field %s: %s", field, reason).
		Detail("field", field).
		Detail("value", value).
		Detail("reason", reason).
		Build()
}

// NetworkError 创建网络错误
func NetworkError(operation string, target string, err error) *AppError {
	return NewBuilder(ErrTypeNetwork, ErrCodeConnectionFailed).
		Message("network operation failed: %s to %s", operation, target).
		Cause(err).
		Detail("operation", operation).
		Detail("target", target).
		Build()
}

// InsufficientFundsError 创建资金不足错误
func InsufficientFundsError(chain, token string, required, available string) *AppError {
	return NewBuilder(ErrTypeInsufficientFunds, ErrCodeInsufficientBalance).
		Message("insufficient %s on %s: required %s, available %s", token, chain, required, available).
		Detail("chain", chain).
		Detail("token", token).
		Detail("required", required).
		Detail("available", available).
		Build()
}

// TimeoutError 创建超时错误
func TimeoutError(operation string, duration time.Duration) *AppError {
	return NewBuilder(ErrTypeTimeout, "OPERATION_TIMEOUT").
		Message("operation %s timed out after %v", operation, duration).
		Detail("operation", operation).
		Detail("timeout", duration.String()).
		Build()
}

// ConfigurationError 创建配置错误
func ConfigurationError(component string, reason string) *AppError {
	return NewBuilder(ErrTypeConfiguration, "INVALID_CONFIGURATION").
		Message("configuration error in %s: %s", component, reason).
		Detail("component", component).
		Detail("reason", reason).
		Build()
}

// NotFoundError 创建资源未找到错误
func NotFoundError(resource string, identifier string) *AppError {
	return NewBuilder(ErrTypeNotFound, "RESOURCE_NOT_FOUND").
		Message("%s not found: %s", resource, identifier).
		Detail("resource", resource).
		Detail("identifier", identifier).
		Build()
}

// 错误检查辅助函数

// IsType 检查错误是否为特定类型
func IsType(err error, errType ErrorType) bool {
	if err == nil {
		return false
	}
	
	appErr, ok := AsAppError(err)
	if !ok {
		return false
	}
	
	return appErr.Type == errType
}

// IsCode 检查错误是否为特定代码
func IsCode(err error, code ErrorCode) bool {
	if err == nil {
		return false
	}
	
	appErr, ok := AsAppError(err)
	if !ok {
		return false
	}
	
	return appErr.Code == code
}

// AsAppError 尝试将错误转换为 AppError
func AsAppError(err error) (*AppError, bool) {
	if err == nil {
		return nil, false
	}
	
	appErr, ok := err.(*AppError)
	return appErr, ok
}

// IsRetryable 检查错误是否可重试
func IsRetryable(err error) bool {
	if err == nil {
		return false
	}
	
	appErr, ok := AsAppError(err)
	if !ok {
		return false
	}
	
	return appErr.IsRetryable()
}

// GetType 获取错误类型
func GetType(err error) ErrorType {
	if err == nil {
		return ""
	}
	
	appErr, ok := AsAppError(err)
	if !ok {
		return ErrTypeInternal
	}
	
	return appErr.Type
}

// GetCode 获取错误代码
func GetCode(err error) ErrorCode {
	if err == nil {
		return ""
	}
	
	appErr, ok := AsAppError(err)
	if !ok {
		return "UNKNOWN"
	}
	
	return appErr.Code
}
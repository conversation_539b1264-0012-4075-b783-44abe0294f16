package middleware

import (
	"fmt"
	"net/http"
	"runtime"
	"time"

	"stargate/internal/errors"
	"stargate/pkg/logger"
)

// RecoveryMiddleware 创建panic恢复中间件
func RecoveryMiddleware() func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			defer func() {
				if rvr := recover(); rvr != nil {
					// 获取堆栈信息
					buf := make([]byte, 4096)
					n := runtime.Stack(buf, false)
					stack := string(buf[:n])
					
					// 创建错误
					err := errors.NewBuilder(errors.ErrTypeInternal, "PANIC_RECOVERED").
						Message("panic recovered: %v", rvr).
						Detail("url", r.URL.String()).
						Detail("method", r.Method).
						Detail("remote_addr", r.RemoteAddr).
						Detail("stack", stack).
						Build()
					
					// 记录错误
					if logger.Enhanced != nil {
						logger.Enhanced.WithError(err).
							Str("request_path", r.URL.Path).
							Str("request_method", r.Method).
							Msg("Panic recovered in HTTP handler")
					} else {
						logger.Error().
							Err(err).
							Str("request_path", r.URL.Path).
							Str("request_method", r.Method).
							Str("stack", stack).
							Msg("Panic recovered in HTTP handler")
					}
					
					// 返回错误响应
					http.Error(w, "Internal Server Error", http.StatusInternalServerError)
				}
			}()
			
			next.ServeHTTP(w, r)
		})
	}
}

// SafeHandler 包装HTTP处理器，提供panic恢复
func SafeHandler(handler http.HandlerFunc, name string) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		defer func() {
			if rvr := recover(); rvr != nil {
				// 获取堆栈信息
				buf := make([]byte, 4096)
				n := runtime.Stack(buf, false)
				stack := string(buf[:n])
				
				// 记录错误
				logger.Error().
					Str("handler", name).
					Interface("panic", rvr).
					Str("stack", stack).
					Str("path", r.URL.Path).
					Str("method", r.Method).
					Msg("Panic in handler")
				
				// 返回错误响应
				http.Error(w, "Internal Server Error", http.StatusInternalServerError)
			}
		}()
		
		handler(w, r)
	}
}

// SafeGo 安全地启动goroutine
func SafeGo(fn func(), name string) {
	go func() {
		defer func() {
			if rvr := recover(); rvr != nil {
				// 获取堆栈信息
				buf := make([]byte, 4096)
				n := runtime.Stack(buf, false)
				stack := string(buf[:n])
				
				// 创建错误
				err := errors.NewBuilder(errors.ErrTypeInternal, "GOROUTINE_PANIC").
					Message("panic in goroutine %s: %v", name, rvr).
					Detail("goroutine", name).
					Detail("panic_value", fmt.Sprintf("%v", rvr)).
					Detail("stack", stack).
					Build()
				
				// 记录错误
				if logger.Enhanced != nil {
					logger.Enhanced.WithError(err).
						Str("goroutine_name", name).
						Msg("Panic recovered in goroutine")
				} else {
					logger.Error().
						Err(err).
						Str("goroutine_name", name).
						Str("stack", stack).
						Msg("Panic recovered in goroutine")
				}
			}
		}()
		
		fn()
	}()
}

// SafeGoWithRestart 安全地启动goroutine，支持自动重启
func SafeGoWithRestart(fn func(), name string, maxRestarts int, restartDelay time.Duration) {
	restartCount := 0
	
	var worker func()
	worker = func() {
		defer func() {
			if rvr := recover(); rvr != nil {
				// 获取堆栈信息
				buf := make([]byte, 4096)
				n := runtime.Stack(buf, false)
				stack := string(buf[:n])
				
				// 记录错误
				logger.Error().
					Str("goroutine", name).
					Interface("panic", rvr).
					Str("stack", stack).
					Int("restart_count", restartCount).
					Msg("Panic recovered in goroutine")
				
				// 检查是否可以重启
				if restartCount < maxRestarts {
					restartCount++
					logger.Info().
						Str("goroutine", name).
						Int("restart_count", restartCount).
						Dur("restart_delay", restartDelay).
						Msg("Restarting goroutine after panic")
					
					// 延迟后重启
					time.Sleep(restartDelay)
					go worker()
				} else {
					logger.Error().
						Str("goroutine", name).
						Int("max_restarts", maxRestarts).
						Msg("Goroutine crashed too many times, not restarting")
				}
			}
		}()
		
		fn()
	}
	
	go worker()
}

// RecoveryFunc 通用恢复函数
type RecoveryFunc func(recovered interface{}, stack []byte)

// WithRecovery 使用自定义恢复函数执行代码
func WithRecovery(fn func(), recovery RecoveryFunc) {
	defer func() {
		if rvr := recover(); rvr != nil {
			// 获取堆栈信息
			buf := make([]byte, 4096)
			n := runtime.Stack(buf, false)
			
			// 调用恢复函数
			if recovery != nil {
				recovery(rvr, buf[:n])
			} else {
				// 默认恢复行为
				logger.Error().
					Interface("panic", rvr).
					Str("stack", string(buf[:n])).
					Msg("Panic recovered")
			}
		}
	}()
	
	fn()
}

// AsyncTask 异步任务包装器
type AsyncTask struct {
	Name         string
	Fn           func() error
	OnSuccess    func()
	OnError      func(error)
	OnPanic      func(interface{}, []byte)
	MaxRetries   int
	RetryDelay   time.Duration
}

// Run 运行异步任务
func (t *AsyncTask) Run() {
	go t.runWithRetry(0)
}

// runWithRetry 带重试的运行
func (t *AsyncTask) runWithRetry(attempt int) {
	defer func() {
		if rvr := recover(); rvr != nil {
			// 获取堆栈信息
			buf := make([]byte, 4096)
			n := runtime.Stack(buf, false)
			
			// 调用panic处理器
			if t.OnPanic != nil {
				t.OnPanic(rvr, buf[:n])
			}
			
			// 记录错误
			logger.Error().
				Str("task", t.Name).
				Interface("panic", rvr).
				Str("stack", string(buf[:n])).
				Int("attempt", attempt).
				Msg("Panic in async task")
			
			// 检查是否重试
			if attempt < t.MaxRetries {
				logger.Info().
					Str("task", t.Name).
					Int("attempt", attempt+1).
					Dur("retry_delay", t.RetryDelay).
					Msg("Retrying task after panic")
				
				time.Sleep(t.RetryDelay)
				t.runWithRetry(attempt + 1)
			}
		}
	}()
	
	// 执行任务
	err := t.Fn()
	if err != nil {
		// 处理错误
		if t.OnError != nil {
			t.OnError(err)
		}
		
		// 记录错误
		appErr := errors.Wrap(err, fmt.Sprintf("async task %s failed", t.Name))
		logger.Enhanced.WithError(appErr).
			Str("task", t.Name).
			Int("attempt", attempt).
			Msg("Async task failed")
		
		// 检查是否重试
		if attempt < t.MaxRetries && errors.IsRetryable(err) {
			logger.Info().
				Str("task", t.Name).
				Int("attempt", attempt+1).
				Dur("retry_delay", t.RetryDelay).
				Msg("Retrying task after error")
			
			time.Sleep(t.RetryDelay)
			t.runWithRetry(attempt + 1)
		}
	} else {
		// 成功处理
		if t.OnSuccess != nil {
			t.OnSuccess()
		}
		
		logger.Info().
			Str("task", t.Name).
			Int("attempt", attempt).
			Msg("Async task completed successfully")
	}
}

// PanicHandler panic处理器接口
type PanicHandler interface {
	HandlePanic(recovered interface{}, stack []byte)
}

// DefaultPanicHandler 默认panic处理器
type DefaultPanicHandler struct {
	OnPanic func(interface{}, []byte)
}

// HandlePanic 处理panic
func (h *DefaultPanicHandler) HandlePanic(recovered interface{}, stack []byte) {
	// 创建错误
	err := errors.NewBuilder(errors.ErrTypeInternal, "PANIC").
		Message("panic: %v", recovered).
		Detail("panic_value", fmt.Sprintf("%v", recovered)).
		Detail("stack", string(stack)).
		Build()
	
	// 记录错误
	logger.Enhanced.WithError(err).Msg("Panic handled")
	
	// 调用自定义处理器
	if h.OnPanic != nil {
		h.OnPanic(recovered, stack)
	}
}
package config

import (
	"fmt"
	"strings"
	"sync"
	"time"

	"stargate/pkg/logger"

	"github.com/fsnotify/fsnotify"
	"github.com/spf13/viper"
)

// Config 主配置结构体
type Config struct {
	// Monitor 监控系统配置
	Monitor MonitorConfig `mapstructure:"monitor"`
	
	// Arbitrage 套利协调器配置
	Arbitrage ArbitrageConfig `mapstructure:"arbitrage"`
	
	// Wallet 钱包配置
	Wallet WalletConfig `mapstructure:"wallet"`
	
	// Exchange 交易所配置
	Exchange ExchangeConfig `mapstructure:"exchange"`
	
	// Funding 资金管理配置
	Funding FundingConfig `mapstructure:"funding"`
	
	// Risk 风险管理配置
	Risk RiskConfig `mapstructure:"risk"`
	
	// Chains 链配置
	Chains map[string]ChainConfig `mapstructure:"chains"`
	
	// Database 数据库配置
	Database DatabaseConfig `mapstructure:"database"`
	
	// Health 健康检查配置
	Health HealthConfig `mapstructure:"health"`

}

// MonitorConfig 监控系统配置
type MonitorConfig struct {
	// CheckInterval 检查间隔
	CheckInterval time.Duration `mapstructure:"check_interval"`
	
	// Telegram 电报通知配置
	Telegram TelegramConfig `mapstructure:"telegram"`
	
	// EnableMetrics 是否启用指标收集
	EnableMetrics bool `mapstructure:"enable_metrics"`
	
	// MetricsPort 指标服务端口
	MetricsPort int `mapstructure:"metrics_port"`
}

// TelegramConfig Telegram通知配置
type TelegramConfig struct {
	Enabled  bool   `mapstructure:"enabled"`
	BotToken string `mapstructure:"bot_token"`
	ChatID   string `mapstructure:"chat_id"`
}

// BinanceConfig 币安配置
type BinanceConfig struct {
	APIKey    string `mapstructure:"api_key"`
	APISecret string `mapstructure:"api_secret"`
	TestNet   bool   `mapstructure:"test_net"`
}

// ConfigManager 配置管理器
type ConfigManager struct {
	config        *Config
	viper         *viper.Viper
	mu            sync.RWMutex
	updateCallbacks []func(*Config)
}

var (
	// 全局配置管理器实例
	manager *ConfigManager
	once    sync.Once
)

// GetManager 获取配置管理器单例
func GetManager() *ConfigManager {
	once.Do(func() {
		manager = &ConfigManager{
			viper:           viper.New(),
			updateCallbacks: make([]func(*Config), 0),
		}
	})
	return manager
}

// GetConfig 获取当前配置
func GetConfig() *Config {
	manager := GetManager()
	manager.mu.RLock()
	defer manager.mu.RUnlock()
	return manager.config
}

// Load 加载配置文件
func (cm *ConfigManager) Load(configFile string) error {
	cm.viper.SetConfigFile(configFile)
	cm.viper.SetConfigType("yaml")
	
	// 设置默认值
	cm.setDefaults()
	
	// 设置环境变量
	cm.setupEnvBindings()
	
	// 读取配置文件
	if err := cm.viper.ReadInConfig(); err != nil {
		return fmt.Errorf("failed to read config file: %w", err)
	}
	
	// 解析配置
	var cfg Config
	if err := cm.viper.Unmarshal(&cfg); err != nil {
		return fmt.Errorf("failed to unmarshal config: %w", err)
	}
	
	// 处理配置兼容性
	cm.processCompatibility(&cfg)
	
	// 验证配置
	if err := cm.validateConfig(&cfg); err != nil {
		return fmt.Errorf("invalid config: %w", err)
	}
	
	// 更新配置
	cm.mu.Lock()
	cm.config = &cfg
	cm.mu.Unlock()
	
	// 通知更新
	cm.notifyUpdate(&cfg)
	
	logger.Info().
		Str("config_file", configFile).
		Msg("Configuration loaded successfully")
	
	return nil
}

// setDefaults 设置默认配置值
func (cm *ConfigManager) setDefaults() {
	// 监控系统默认值
	cm.viper.SetDefault("monitor.check_interval", "12s")
	cm.viper.SetDefault("monitor.enable_metrics", true)
	cm.viper.SetDefault("monitor.metrics_port", 9090)
	cm.viper.SetDefault("monitor.telegram.enabled", true)
	
	// 套利协调器默认值
	cm.viper.SetDefault("arbitrage.opportunity_buffer_size", 100)
	cm.viper.SetDefault("arbitrage.deduplication_window_minutes", 5)
	cm.viper.SetDefault("arbitrage.max_active_opportunities", 10)
	cm.viper.SetDefault("arbitrage.process_timeout", "5m")
	cm.viper.SetDefault("arbitrage.priority_queue_size", 1000)
	cm.viper.SetDefault("arbitrage.handle_opportunity_timeout", "5s")
	
	// 钱包默认值
	cm.viper.SetDefault("wallet.keystore_path", "./keystore")
	
	// 交易所默认值
	cm.viper.SetDefault("exchange.type", "binance")
	cm.viper.SetDefault("exchange.test_net", false)
	cm.viper.SetDefault("exchange.cache_ttl", "5m")
	cm.viper.SetDefault("exchange.request_interval", "100ms")
	cm.viper.SetDefault("exchange.enabled", false)
	
	// 资金管理默认值
	cm.viper.SetDefault("funding.withdraw_threshold", 0.8)
	cm.viper.SetDefault("funding.rebalance_interval", "1h")
	cm.viper.SetDefault("funding.max_withdraw_amount", 10000.0)
	
	// 风险管理默认值
	cm.viper.SetDefault("risk.max_single_trade", 1000.0)
	cm.viper.SetDefault("risk.max_daily_volume", 10000.0)
	cm.viper.SetDefault("risk.max_slippage", 0.02)
	cm.viper.SetDefault("risk.min_reward_threshold", 10.0)
	
	// 数据库默认值
	cm.viper.SetDefault("database.path", "./data/stargate.db")
	cm.viper.SetDefault("database.max_open_conns", 25)
	cm.viper.SetDefault("database.max_idle_conns", 5)
	cm.viper.SetDefault("database.conn_max_lifetime", "1h")
	
	// 健康检查默认值
	cm.viper.SetDefault("health.enabled", true)
	cm.viper.SetDefault("health.port", 8081)
	cm.viper.SetDefault("health.path", "/health")
}

// setupEnvBindings 设置环境变量绑定
func (cm *ConfigManager) setupEnvBindings() {
	// 允许从环境变量覆盖配置
	cm.viper.AutomaticEnv()
	cm.viper.SetEnvPrefix("STARGATE")
	cm.viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	
	// 特殊处理敏感信息的环境变量
	cm.viper.BindEnv("exchange.api_key", "EXCHANGE_API_KEY")
	cm.viper.BindEnv("exchange.api_secret", "EXCHANGE_API_SECRET")
	
	cm.viper.BindEnv("wallet.password", "WALLET_PASSWORD")
	
	cm.viper.BindEnv("monitor.telegram.bot_token", "TELEGRAM_BOT_TOKEN")
	cm.viper.BindEnv("monitor.telegram.chat_id", "TELEGRAM_CHAT_ID")
}

// processCompatibility 处理配置兼容性
func (cm *ConfigManager) processCompatibility(cfg *Config) {
	// 处理链配置兼容性
	for name, chain := range cfg.Chains {
		// 如果只有 RPCURL，将其添加到 RPCURLs
		if chain.RPCURL != "" && len(chain.RPCURLs) == 0 {
			chain.RPCURLs = []string{chain.RPCURL}
			cfg.Chains[name] = chain
		}
		// 如果只有 RPCURLs，取第一个作为 RPCURL
		if chain.RPCURL == "" && len(chain.RPCURLs) > 0 {
			chain.RPCURL = chain.RPCURLs[0]
			cfg.Chains[name] = chain
		}
	}
	
	// 确保 Exchange 有 enabled 字段
	if cfg.Exchange.APIKey != "" && cfg.Exchange.APISecret != "" {
		cfg.Exchange.Enabled = true
	}
}

// validateConfig 验证配置的有效性
func (cm *ConfigManager) validateConfig(cfg *Config) error {
	// 验证监控配置
	if cfg.Monitor.CheckInterval <= 0 {
		return fmt.Errorf("monitor.check_interval must be positive")
	}
	if cfg.Monitor.EnableMetrics && cfg.Monitor.MetricsPort <= 0 {
		return fmt.Errorf("monitor.metrics_port must be positive when metrics are enabled")
	}
	
	// 验证套利配置
	if cfg.Arbitrage.OpportunityBufferSize <= 0 {
		return fmt.Errorf("arbitrage.opportunity_buffer_size must be positive")
	}
	if cfg.Arbitrage.DeduplicationWindowMinutes <= 0 {
		return fmt.Errorf("arbitrage.deduplication_window_minutes must be positive")
	}
	if cfg.Arbitrage.MaxActiveOpportunities <= 0 {
		return fmt.Errorf("arbitrage.max_active_opportunities must be positive")
	}
	
	// 验证风险配置
	if cfg.Risk.MaxSingleTrade <= 0 {
		return fmt.Errorf("risk.max_single_trade must be positive")
	}
	if cfg.Risk.MaxDailyVolume <= 0 {
		return fmt.Errorf("risk.max_daily_volume must be positive")
	}
	if cfg.Risk.MaxSlippage < 0 || cfg.Risk.MaxSlippage > 1 {
		return fmt.Errorf("risk.max_slippage must be between 0 and 1")
	}
	if cfg.Risk.MinRewardThreshold < 0 {
		return fmt.Errorf("risk.min_reward_threshold must be non-negative")
	}
	
	// 验证数据库配置
	if cfg.Database.Path == "" {
		return fmt.Errorf("database.path cannot be empty")
	}
	if cfg.Database.MaxOpenConns <= 0 {
		return fmt.Errorf("database.max_open_conns must be positive")
	}
	if cfg.Database.MaxIdleConns < 0 {
		return fmt.Errorf("database.max_idle_conns must be non-negative")
	}
	
	// 验证健康检查配置
	if cfg.Health.Enabled && cfg.Health.Port <= 0 {
		return fmt.Errorf("health.port must be positive when health check is enabled")
	}
	
	// 验证链配置
	for name, chain := range cfg.Chains {
		// 检查是否有 RPC URL（支持旧的 RPCURL 和新的 RPCURLs）
		if chain.RPCURL == "" && len(chain.RPCURLs) == 0 {
			return fmt.Errorf("chain %s: rpc_url or rpc_urls must be provided", name)
		}
		if chain.ChainID <= 0 {
			return fmt.Errorf("chain %s: chain_id must be positive", name)
		}
	}
	
	return nil
}

// WatchConfig 启用配置文件热更新
func (cm *ConfigManager) WatchConfig() error {
	cm.viper.WatchConfig()
	cm.viper.OnConfigChange(func(e fsnotify.Event) {
		logger.Info().
			Str("file", e.Name).
			Str("operation", e.Op.String()).
			Msg("Configuration file changed")
		
		// 重新加载配置
		var cfg Config
		if err := cm.viper.Unmarshal(&cfg); err != nil {
			logger.Error().
				Err(err).
				Msg("Failed to reload configuration")
			return
		}
		
		// 验证新配置
		if err := cm.validateConfig(&cfg); err != nil {
			logger.Error().
				Err(err).
				Msg("Invalid configuration after reload")
			return
		}
		
		// 更新配置
		cm.mu.Lock()
		oldConfig := cm.config
		cm.config = &cfg
		cm.mu.Unlock()
		
		// 通知更新
		cm.notifyUpdate(&cfg)
		
		logger.Info().
			Interface("old_config", oldConfig).
			Interface("new_config", cfg).
			Msg("Configuration reloaded successfully")
	})
	
	return nil
}

// RegisterUpdateCallback 注册配置更新回调
func (cm *ConfigManager) RegisterUpdateCallback(callback func(*Config)) {
	cm.mu.Lock()
	cm.updateCallbacks = append(cm.updateCallbacks, callback)
	cm.mu.Unlock()
}

// notifyUpdate 通知配置更新
func (cm *ConfigManager) notifyUpdate(cfg *Config) {
	for _, callback := range cm.updateCallbacks {
		go callback(cfg)
	}
}


// GetString 获取字符串配置项
func (cm *ConfigManager) GetString(key string) string {
	return cm.viper.GetString(key)
}

// GetInt 获取整数配置项
func (cm *ConfigManager) GetInt(key string) int {
	return cm.viper.GetInt(key)
}

// GetBool 获取布尔配置项
func (cm *ConfigManager) GetBool(key string) bool {
	return cm.viper.GetBool(key)
}

// GetFloat64 获取浮点数配置项
func (cm *ConfigManager) GetFloat64(key string) float64 {
	return cm.viper.GetFloat64(key)
}

// GetDuration 获取时间间隔配置项
func (cm *ConfigManager) GetDuration(key string) time.Duration {
	return cm.viper.GetDuration(key)
}

// IsSet 检查配置项是否存在
func (cm *ConfigManager) IsSet(key string) bool {
	return cm.viper.IsSet(key)
}


// ArbitrageConfig 套利协调器配置
type ArbitrageConfig struct {
	OpportunityBufferSize      int           `mapstructure:"opportunity_buffer_size"`
	DeduplicationWindowMinutes int           `mapstructure:"deduplication_window_minutes"`
	MaxActiveOpportunities     int           `mapstructure:"max_active_opportunities"`
	ProcessTimeout             time.Duration `mapstructure:"process_timeout"`
	PriorityQueueSize          int           `mapstructure:"priority_queue_size"`
	HandleOpportunityTimeout   time.Duration `mapstructure:"handle_opportunity_timeout"`
}

// WalletConfig 钱包配置
type WalletConfig struct {
	KeystorePath string `mapstructure:"keystore_path"`
	// EnablePasswordPrompt 是否在启动时提示输入密码
	EnablePasswordPrompt bool `mapstructure:"enable_password_prompt"`
	// 注意：密码应该从环境变量或安全存储中读取
	// Chains 配置从主配置的 Chains 字段读取
}

// ExchangeConfig 交易所配置
type ExchangeConfig struct {
	Enabled         bool   `mapstructure:"enabled"`
	Type            string `mapstructure:"type"`
	APIKey          string `mapstructure:"api_key"`
	APISecret       string `mapstructure:"api_secret"`
	TestNet         bool   `mapstructure:"test_net"`
	CacheTTL        string `mapstructure:"cache_ttl"`
	RequestInterval string `mapstructure:"request_interval"`
}

// FundingConfig 资金管理配置
type FundingConfig struct {
	MinReserves         map[string]float64 `mapstructure:"min_reserves"`
	WithdrawThreshold   float64            `mapstructure:"withdraw_threshold"`
	RebalanceInterval   time.Duration      `mapstructure:"rebalance_interval"`
	MaxWithdrawAmount   float64            `mapstructure:"max_withdraw_amount"`
}

// RiskConfig 风险管理配置（从 trader.go 迁移）
type RiskConfig struct {
	MaxSingleTrade     float64            `mapstructure:"max_single_trade"`
	MaxDailyVolume     float64            `mapstructure:"max_daily_volume"`
	MaxSlippage        float64            `mapstructure:"max_slippage"`
	TokenWhitelist     []string           `mapstructure:"token_whitelist"`
	ChainWhitelist     []string           `mapstructure:"chain_whitelist"`
	MinRewardThreshold float64            `mapstructure:"min_reward_threshold"`
	AutoWithdraw       bool               `mapstructure:"auto_withdraw"`
}

// DatabaseConfig 数据库配置（从 trader.go 迁移）
type DatabaseConfig struct {
	Path            string `mapstructure:"path"`
	MaxOpenConns    int    `mapstructure:"max_open_conns"`
	MaxIdleConns    int    `mapstructure:"max_idle_conns"`
	ConnMaxLifetime string `mapstructure:"conn_max_lifetime"`
}

// HealthConfig 健康检查配置（从 trader.go 迁移）
type HealthConfig struct {
	Enabled bool   `mapstructure:"enabled"`
	Port    int    `mapstructure:"port"`
	Path    string `mapstructure:"path"`
}

// ChainConfig 链配置（完整版本，从 pkg/config/chain.go 迁移）
type ChainConfig struct {
	ChainName     string               `mapstructure:"chain_name"`
	ChainID       int64                `mapstructure:"chain_id"`
	EndpointID    uint32               `mapstructure:"endpoint_id"`
	RPCURLs       []string             `mapstructure:"rpc_urls"`
	RPCURL        string               `mapstructure:"rpc_url"`     // 兼容旧配置
	WSSURL        string               `mapstructure:"wss_url"`     // WebSocket URL for real-time updates
	CheckInterval time.Duration        `mapstructure:"check_interval"` // 链级别的检查间隔
	Tokens        map[string]TokenInfo `mapstructure:"tokens"`      // 代币信息
	Stargate      StargateConfig       `mapstructure:"stargate"`    // Stargate 合约配置
}

// TokenInfo 代币信息
type TokenInfo struct {
	Address  string `mapstructure:"address"`  // 代币合约地址
	Decimals int    `mapstructure:"decimals"` // 代币精度
}

// StargateConfig Stargate 相关合约配置
type StargateConfig struct {
	Pools   map[string]string `mapstructure:"pools"`    // Pool 合约地址
	FeeLibs map[string]string `mapstructure:"fee_libs"` // FeeLib 合约地址
}
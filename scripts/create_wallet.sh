#!/bin/bash

# 创建钱包的脚本

echo "🔐 Stargate Wallet Generator"
echo "=========================="
echo ""

# 构建 keygen 工具
echo "Building keygen tool..."
go build -o bin/keygen cmd/keygen/main.go

if [ $? -ne 0 ]; then
    echo "❌ Failed to build keygen tool"
    exit 1
fi

# 显示选项
echo "Select an option:"
echo "1. Create new wallet"
echo "2. Import existing private key"
echo "3. Show existing wallets"
echo -n "Enter your choice (1-3): "
read choice

case $choice in
    1)
        echo ""
        echo "Creating new wallet..."
        ./bin/keygen -keystore ./keystore
        ;;
    2)
        echo ""
        echo "⚠️  WARNING: Only import private keys from secure sources!"
        echo -n "Enter private key (without 0x prefix): "
        read -s private_key
        echo ""
        ./bin/keygen -keystore ./keystore -import "$private_key"
        ;;
    3)
        echo ""
        ./bin/keygen -keystore ./keystore -show
        ;;
    *)
        echo "Invalid choice!"
        exit 1
        ;;
esac

echo ""
echo "✅ Done!"
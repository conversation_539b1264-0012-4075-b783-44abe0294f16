package exchange

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"github.com/ccxt/ccxt/go/v4"
	"stargate/pkg/logger"
)

// DepositStatus 充值状态
type DepositStatus string

const (
	DepositStatusPending    DepositStatus = "pending"    // 等待中
	DepositStatusCredited   DepositStatus = "credited"   // 已到账
	DepositStatusProcessing DepositStatus = "processing" // 处理中
	DepositStatusSuccess    DepositStatus = "success"    // 成功
	DepositStatusFailed     DepositStatus = "failed"     // 失败
)

// DepositRecord 充值记录
type DepositRecord struct {
	ID              string        `json:"id"`                // 充值ID
	TxID            string        `json:"tx_id"`             // 区块链交易ID
	Currency        string        `json:"currency"`          // 币种
	Network         string        `json:"network"`           // 网络
	Address         string        `json:"address"`           // 充值地址
	Tag             string        `json:"tag"`               // Tag/Memo
	Amount          float64       `json:"amount"`            // 充值金额
	Status          DepositStatus `json:"status"`            // 状态
	Confirmations   int           `json:"confirmations"`     // 确认数
	UnlockConfirms  int           `json:"unlock_confirms"`   // 解锁所需确认数
	SubmitTime      time.Time     `json:"submit_time"`       // 提交时间
	SuccessTime     time.Time     `json:"success_time"`      // 成功时间
	InsertTime      time.Time     `json:"insert_time"`       // 记录插入时间
}

// DepositHistory 充值历史
type DepositHistory struct {
	Total   int              `json:"total"`   // 总记录数
	Records []*DepositRecord `json:"records"` // 记录列表
}

// NetworkConfirmations 网络确认要求
type NetworkConfirmations struct {
	Network           string `json:"network"`            // 网络名称
	RequiredConfirms  int    `json:"required_confirms"`  // 所需确认数
	UnlockConfirms    int    `json:"unlock_confirms"`    // 解锁确认数
}

// DepositMonitor 充值监控器
type DepositMonitor struct {
	client            *ExchangeClient
	pollingInterval   time.Duration
	notifyFunc        func(*DepositRecord)
	watchedDeposits   map[string]*DepositRecord // depositID -> record
	networkConfirms   map[string]int            // network -> required confirmations
	mu                sync.RWMutex
	ctx               context.Context
	cancel            context.CancelFunc
	running           bool
}

// GetDepositHistory 获取充值历史
func (c *ExchangeClient) GetDepositHistory(ctx context.Context, coin string, startTime, endTime *time.Time, limit int) (*DepositHistory, error) {
	// 限流
	c.enforceRateLimit()
	
	// 构建参数
	params := make(map[string]interface{})
	if startTime != nil {
		params["startTime"] = startTime.Unix() * 1000
	}
	if endTime != nil {
		params["endTime"] = endTime.Unix() * 1000
	}
	if limit > 0 {
		params["limit"] = limit
	}
	
	// 只有Binance支持查询充值历史
	switch ex := c.exchange.(type) {
	case *ccxt.Binance:
		// 构建选项
		var options []ccxt.FetchDepositsOptions
		if coin != "" {
			options = append(options, ccxt.WithFetchDepositsCode(coin))
		}
		if startTime != nil {
			options = append(options, ccxt.WithFetchDepositsSince(startTime.Unix()*1000))
		}
		if limit > 0 {
			options = append(options, ccxt.WithFetchDepositsLimit(int64(limit)))
		}
		if len(params) > 0 {
			options = append(options, ccxt.WithFetchDepositsParams(params))
		}
		
		deposits, err := ex.FetchDeposits(options...)
		if err != nil {
			return nil, fmt.Errorf("failed to fetch deposit history: %w", err)
		}
		
		// 转换为我们的格式
		history := &DepositHistory{
			Total:   len(deposits),
			Records: make([]*DepositRecord, 0, len(deposits)),
		}
		
		for _, d := range deposits {
			record := &DepositRecord{
				Status:   parseDepositStatus(d.Status),
				Currency: coin,
			}
			
			// 解析字段
			if d.Id != nil {
				record.ID = *d.Id
			}
			if d.TxId != nil {
				record.TxID = *d.TxId
			}
			if d.Amount != nil {
				record.Amount = *d.Amount
			}
			if d.Address != nil {
				record.Address = *d.Address
			}
			if d.Tag != nil {
				record.Tag = *d.Tag
			}
			if d.Timestamp != nil {
				record.SubmitTime = time.Unix(int64(*d.Timestamp)/1000, 0)
			}
			if d.Datetime != nil {
				// 解析时间字符串
				if t, err := time.Parse(time.RFC3339, *d.Datetime); err == nil {
					record.InsertTime = t
				}
			}
			
			// CCXT Go版本的Transaction结构可能不包含Info字段
			// 网络和确认数信息需要从其他地方获取
			// TODO: 检查CCXT Go如何获取这些信息
			
			history.Records = append(history.Records, record)
		}
		
		return history, nil
		
	default:
		return nil, fmt.Errorf("get deposit history not supported for exchange type: %s", c.exchangeType)
	}
}

// GetNetworkConfirmations 获取网络确认要求
func (c *ExchangeClient) GetNetworkConfirmations(ctx context.Context, coin, network string) (*NetworkConfirmations, error) {
	// 限流
	c.enforceRateLimit()
	
	// 默认确认数（实际应该从交易所API获取）
	defaultConfirms := map[string]int{
		"ETH":      12,
		"BSC":      15,
		"POLYGON":  128,
		"ARBITRUM": 12,
		"OPTIMISM": 12,
		"AVAX":     12,
		"BTC":      2,
		"TRX":      20,
	}
	
	confirms := &NetworkConfirmations{
		Network:          network,
		RequiredConfirms: 12, // 默认值
		UnlockConfirms:   12, // 默认值
	}
	
	// 使用默认值
	if conf, exists := defaultConfirms[network]; exists {
		confirms.RequiredConfirms = conf
		confirms.UnlockConfirms = conf
	}
	
	logger.Info().
		Str("coin", coin).
		Str("network", network).
		Int("confirmations", confirms.RequiredConfirms).
		Msg("Network confirmations retrieved")
	
	return confirms, nil
}

// NewDepositMonitor 创建充值监控器
func NewDepositMonitor(client *ExchangeClient, pollingInterval time.Duration) *DepositMonitor {
	ctx, cancel := context.WithCancel(context.Background())
	
	return &DepositMonitor{
		client:          client,
		pollingInterval: pollingInterval,
		watchedDeposits: make(map[string]*DepositRecord),
		networkConfirms: make(map[string]int),
		ctx:            ctx,
		cancel:         cancel,
		running:        false,
	}
}

// SetNotificationFunc 设置通知函数
func (m *DepositMonitor) SetNotificationFunc(fn func(*DepositRecord)) {
	m.notifyFunc = fn
}

// Start 启动监控
func (m *DepositMonitor) Start() error {
	m.mu.Lock()
	if m.running {
		m.mu.Unlock()
		return errors.New("monitor already running")
	}
	m.running = true
	m.mu.Unlock()
	
	go m.monitorLoop()
	
	logger.Info().
		Dur("interval", m.pollingInterval).
		Msg("Deposit monitor started")
	
	return nil
}

// Stop 停止监控
func (m *DepositMonitor) Stop() {
	m.mu.Lock()
	if !m.running {
		m.mu.Unlock()
		return
	}
	m.running = false
	m.mu.Unlock()
	
	m.cancel()
	
	logger.Info().Msg("Deposit monitor stopped")
}

// AddWatchedDeposit 添加监控的充值
func (m *DepositMonitor) AddWatchedDeposit(deposit *DepositRecord) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.watchedDeposits[deposit.ID] = deposit
	
	logger.Info().
		Str("deposit_id", deposit.ID).
		Str("currency", deposit.Currency).
		Float64("amount", deposit.Amount).
		Msg("Added deposit to watch list")
}

// monitorLoop 监控循环
func (m *DepositMonitor) monitorLoop() {
	ticker := time.NewTicker(m.pollingInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-m.ctx.Done():
			return
		case <-ticker.C:
			m.checkDeposits()
		}
	}
}

// checkDeposits 检查充值状态
func (m *DepositMonitor) checkDeposits() {
	m.mu.RLock()
	watchList := make(map[string]*DepositRecord)
	for k, v := range m.watchedDeposits {
		watchList[k] = v
	}
	m.mu.RUnlock()
	
	if len(watchList) == 0 {
		return
	}
	
	// 获取最近的充值历史
	endTime := time.Now()
	startTime := endTime.Add(-24 * time.Hour) // 查询最近24小时
	
	history, err := m.client.GetDepositHistory(m.ctx, "", &startTime, &endTime, 100)
	if err != nil {
		logger.Error().
			Err(err).
			Msg("Failed to fetch deposit history for monitoring")
		return
	}
	
	// 检查每个充值记录
	for _, record := range history.Records {
		if watched, exists := watchList[record.ID]; exists {
			// 检查状态是否变化
			if record.Status != watched.Status {
				logger.Info().
					Str("deposit_id", record.ID).
					Str("old_status", string(watched.Status)).
					Str("new_status", string(record.Status)).
					Int("confirmations", record.Confirmations).
					Msg("Deposit status changed")
				
				// 更新监控记录
				m.mu.Lock()
				m.watchedDeposits[record.ID] = record
				m.mu.Unlock()
				
				// 发送通知
				if m.notifyFunc != nil {
					m.notifyFunc(record)
				}
				
				// 如果充值已完成，从监控列表中移除
				if record.Status == DepositStatusSuccess || record.Status == DepositStatusFailed {
					m.mu.Lock()
					delete(m.watchedDeposits, record.ID)
					m.mu.Unlock()
					
					logger.Info().
						Str("deposit_id", record.ID).
						Str("final_status", string(record.Status)).
						Msg("Deposit monitoring completed")
				}
			}
		}
	}
}

// GetWatchedDeposits 获取正在监控的充值列表
func (m *DepositMonitor) GetWatchedDeposits() []*DepositRecord {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	deposits := make([]*DepositRecord, 0, len(m.watchedDeposits))
	for _, deposit := range m.watchedDeposits {
		deposits = append(deposits, deposit)
	}
	
	return deposits
}

// parseDepositStatus 解析充值状态
func parseDepositStatus(status *string) DepositStatus {
	if status == nil {
		return DepositStatusPending
	}
	
	switch *status {
	case "pending", "processing":
		return DepositStatusProcessing
	case "ok", "success", "completed", "credited":
		return DepositStatusSuccess
	case "failed", "rejected":
		return DepositStatusFailed
	default:
		return DepositStatusPending
	}
}
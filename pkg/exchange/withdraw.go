package exchange

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/ccxt/ccxt/go/v4"
	"stargate/pkg/logger"
)

// WithdrawStatus 提现状态
type WithdrawStatus string

const (
	WithdrawStatusPending    WithdrawStatus = "pending"
	WithdrawStatusProcessing WithdrawStatus = "processing"
	WithdrawStatusSuccess    WithdrawStatus = "success"
	WithdrawStatusFailed     WithdrawStatus = "failed"
	WithdrawStatusCancelled  WithdrawStatus = "cancelled"
)

// WithdrawRequest 提现请求
type WithdrawRequest struct {
	Coin      string  `json:"coin"`       // 币种
	Network   string  `json:"network"`    // 网络
	Address   string  `json:"address"`    // 提现地址
	Tag       string  `json:"tag"`        // memo/tag (可选)
	Amount    float64 `json:"amount"`     // 提现数量
	TransID   string  `json:"trans_id"`   // 客户端事务ID (可选)
}

// WithdrawResponse 提现响应
type WithdrawResponse struct {
	ID         string         `json:"id"`          // 提现ID
	TransID    string         `json:"trans_id"`    // 客户端事务ID
	Amount     float64        `json:"amount"`      // 提现金额
	Fee        float64        `json:"fee"`         // 手续费
	Status     WithdrawStatus `json:"status"`      // 状态
	TxID       string         `json:"tx_id"`       // 区块链交易ID
	Info       string         `json:"info"`        // 额外信息
	SubmitTime time.Time      `json:"submit_time"` // 提交时间
	UpdateTime time.Time      `json:"update_time"` // 更新时间
}

// WithdrawLimit 提现限额
type WithdrawLimit struct {
	Coin         string  `json:"coin"`          // 币种
	Network      string  `json:"network"`       // 网络
	MinAmount    float64 `json:"min_amount"`    // 最小提现数量
	MaxAmount    float64 `json:"max_amount"`    // 最大提现数量
	MinFee       float64 `json:"min_fee"`       // 最小手续费
	MaxFee       float64 `json:"max_fee"`       // 最大手续费
	IsEnabled    bool    `json:"is_enabled"`    // 是否启用
	NeedTag      bool    `json:"need_tag"`      // 是否需要tag
}

// WithdrawHistory 提现历史记录
type WithdrawHistory struct {
	Total   int                 `json:"total"`   // 总记录数
	Records []*WithdrawResponse `json:"records"` // 记录列表
}

// Withdraw 执行提现
func (c *ExchangeClient) Withdraw(ctx context.Context, req *WithdrawRequest) (*WithdrawResponse, error) {
	if req == nil {
		return nil, errors.New("withdraw request is nil")
	}
	
	// 参数验证
	if req.Coin == "" || req.Network == "" || req.Address == "" {
		return nil, errors.New("coin, network and address are required")
	}
	if req.Amount <= 0 {
		return nil, errors.New("amount must be positive")
	}
	
	// 限流
	c.enforceRateLimit()
	
	// 构建参数
	params := map[string]interface{}{
		"coin":    req.Coin,
		"network": req.Network,
		"address": req.Address,
		"amount":  req.Amount,
	}
	
	if req.Tag != "" {
		params["addressTag"] = req.Tag
	}
	if req.TransID != "" {
		params["transactionFeeFlag"] = req.TransID
	}
	
	// 只有Binance支持提现
	var result ccxt.Transaction
	
	switch ex := c.exchange.(type) {
	case *ccxt.Binance:
		// CCXT的withdraw方法
		options := []ccxt.WithdrawOptions{
			ccxt.WithWithdrawTag(req.Tag),
			ccxt.WithWithdrawParams(params),
		}
		transaction, err := ex.Withdraw(req.Coin, req.Amount, req.Address, options...)
		if err != nil {
			return nil, fmt.Errorf("failed to withdraw: %w", err)
		}
		result = transaction
	default:
		return nil, fmt.Errorf("withdraw not supported for exchange type: %s", c.exchangeType)
	}
	
	// 解析响应
	response := &WithdrawResponse{
		Amount:     req.Amount,
		Status:     WithdrawStatusPending,
		SubmitTime: time.Now(),
		UpdateTime: time.Now(),
	}
	
	// 解析transaction信息
	if result.Id != nil {
		response.ID = *result.Id
	}
	if result.TxId != nil {
		response.TxID = *result.TxId
	}
	if result.Status != nil {
		response.Status = parseWithdrawStatus(result.Status)
	}
	
	logger.Info().
		Str("exchange", string(c.exchangeType)).
		Str("coin", req.Coin).
		Str("network", req.Network).
		Str("address", req.Address).
		Float64("amount", req.Amount).
		Str("withdraw_id", response.ID).
		Msg("Withdraw submitted")
	
	return response, nil
}

// GetWithdrawStatus 查询提现状态
func (c *ExchangeClient) GetWithdrawStatus(ctx context.Context, withdrawID string) (*WithdrawResponse, error) {
	if withdrawID == "" {
		return nil, errors.New("withdraw ID is required")
	}
	
	// 限流
	c.enforceRateLimit()
	
	// 只有Binance支持查询提现状态
	switch ex := c.exchange.(type) {
	case *ccxt.Binance:
		// 使用fetchWithdrawals查询
		options := []ccxt.FetchWithdrawalsOptions{
			ccxt.WithFetchWithdrawalsParams(map[string]interface{}{
				"id": withdrawID,
			}),
		}
		
		withdrawals, err := ex.FetchWithdrawals(options...)
		if err != nil {
			return nil, fmt.Errorf("failed to fetch withdraw status: %w", err)
		}
		
		if len(withdrawals) == 0 {
			return nil, fmt.Errorf("withdraw ID not found: %s", withdrawID)
		}
		
		// 解析第一条记录
		withdrawal := withdrawals[0]
		response := &WithdrawResponse{
			ID:     withdrawID,
			Status: parseWithdrawStatus(withdrawal.Status),
		}
		
		// 解析其他字段
		if withdrawal.Amount != nil {
			response.Amount = *withdrawal.Amount
		}
		// Transaction 结构没有 Fee 字段，需要从 Info 中解析
		if withdrawal.TxId != nil {
			response.TxID = *withdrawal.TxId
		}
		if withdrawal.Timestamp != nil {
			response.SubmitTime = time.Unix(int64(*withdrawal.Timestamp)/1000, 0)
		}
		if withdrawal.Datetime != nil {
			// 解析时间字符串
			if t, err := time.Parse(time.RFC3339, *withdrawal.Datetime); err == nil {
				response.UpdateTime = t
			}
		}
		
		return response, nil
		
	default:
		return nil, fmt.Errorf("get withdraw status not supported for exchange type: %s", c.exchangeType)
	}
}

// GetWithdrawLimits 获取提现限额
func (c *ExchangeClient) GetWithdrawLimits(ctx context.Context, coin, network string) (*WithdrawLimit, error) {
	if coin == "" || network == "" {
		return nil, errors.New("coin and network are required")
	}
	
	// 限流
	c.enforceRateLimit()
	
	// 只有Binance支持查询提现限额
	switch ex := c.exchange.(type) {
	case *ccxt.Binance:
		// 获取币种信息
		currencies, err := ex.FetchCurrencies(nil)
		if err != nil {
			return nil, fmt.Errorf("failed to fetch currencies: %w", err)
		}
		
		// 查找指定币种
		currency, err := currencies.Get(coin)
		if err != nil {
			return nil, fmt.Errorf("coin not found: %s", coin)
		}
		
		// 查找指定网络的限额信息
		limit := &WithdrawLimit{
			Coin:      coin,
			Network:   network,
			IsEnabled: false,
		}
		
		// 解析网络信息
		if currency.Networks != nil {
			for netName, netInfo := range currency.Networks {
				if netName == network {
					if netInfo.Active != nil {
						limit.IsEnabled = *netInfo.Active
					}
					if netInfo.Fee != nil {
						limit.MinFee = *netInfo.Fee
						limit.MaxFee = *netInfo.Fee
					}
					// CCXT Go版本的限额信息结构可能不同
					// 暂时跳过限额信息的解析
					// TODO: 需要检查实际的 netInfo 结构
					break
				}
			}
		}
		
		return limit, nil
		
	default:
		return nil, fmt.Errorf("get withdraw limits not supported for exchange type: %s", c.exchangeType)
	}
}

// GetWithdrawHistory 获取提现历史
func (c *ExchangeClient) GetWithdrawHistory(ctx context.Context, coin string, startTime, endTime *time.Time, limit int) (*WithdrawHistory, error) {
	// 限流
	c.enforceRateLimit()
	
	// 构建参数
	params := make(map[string]interface{})
	if startTime != nil {
		params["since"] = startTime.Unix() * 1000
	}
	if endTime != nil {
		params["until"] = endTime.Unix() * 1000
	}
	if limit > 0 {
		params["limit"] = limit
	}
	
	// 只有Binance支持查询提现历史
	switch ex := c.exchange.(type) {
	case *ccxt.Binance:
		// 构建选项
		var options []ccxt.FetchWithdrawalsOptions
		if coin != "" {
			options = append(options, ccxt.WithFetchWithdrawalsCode(coin))
		}
		if startTime != nil {
			options = append(options, ccxt.WithFetchWithdrawalsSince(startTime.Unix()*1000))
		}
		if limit > 0 {
			options = append(options, ccxt.WithFetchWithdrawalsLimit(int64(limit)))
		}
		if len(params) > 0 {
			options = append(options, ccxt.WithFetchWithdrawalsParams(params))
		}
		
		withdrawals, err := ex.FetchWithdrawals(options...)
		if err != nil {
			return nil, fmt.Errorf("failed to fetch withdraw history: %w", err)
		}
		
		// 转换为我们的格式
		history := &WithdrawHistory{
			Total:   len(withdrawals),
			Records: make([]*WithdrawResponse, 0, len(withdrawals)),
		}
		
		for _, w := range withdrawals {
			record := &WithdrawResponse{
				Status: parseWithdrawStatus(w.Status),
			}
			
			// 解析字段
			if w.Id != nil {
				record.ID = *w.Id
			}
			if w.Amount != nil {
				record.Amount = *w.Amount
			}
			// Transaction 结构没有 Fee 字段
			if w.TxId != nil {
				record.TxID = *w.TxId
			}
			if w.Timestamp != nil {
				record.SubmitTime = time.Unix(int64(*w.Timestamp)/1000, 0)
			}
			if w.Updated != nil {
				record.UpdateTime = time.Unix(int64(*w.Updated)/1000, 0)
			}
			
			history.Records = append(history.Records, record)
		}
		
		return history, nil
		
	default:
		return nil, fmt.Errorf("get withdraw history not supported for exchange type: %s", c.exchangeType)
	}
}

// CheckWithdrawWhitelist 检查提现白名单
func (c *ExchangeClient) CheckWithdrawWhitelist(ctx context.Context, coin, address, network string) (bool, error) {
	if coin == "" || address == "" || network == "" {
		return false, errors.New("coin, address and network are required")
	}
	
	// 限流
	c.enforceRateLimit()
	
	// 目前CCXT不直接支持查询白名单，需要通过其他方式实现
	// 这里返回一个基本实现，实际应用中可能需要调用交易所特定API
	logger.Warn().
		Str("exchange", string(c.exchangeType)).
		Str("coin", coin).
		Str("address", address).
		Str("network", network).
		Msg("Whitelist check not fully implemented, returning true by default")
	
	return true, nil
}

// parseWithdrawStatus 解析提现状态
func parseWithdrawStatus(status *string) WithdrawStatus {
	if status == nil {
		return WithdrawStatusPending
	}
	
	switch *status {
	case "pending", "processing":
		return WithdrawStatusProcessing
	case "ok", "success", "completed":
		return WithdrawStatusSuccess
	case "failed", "rejected":
		return WithdrawStatusFailed
	case "canceled", "cancelled":
		return WithdrawStatusCancelled
	default:
		return WithdrawStatusPending
	}
}
package exchange

import (
	"context"
	"errors"
	"fmt"
	"math/big"
	"strings"
	"sync"
	"time"

	"github.com/ccxt/ccxt/go/v4"
	"stargate/pkg/events"
	"stargate/pkg/logger"
)

// ExchangeClient 交易所客户端
type ExchangeClient struct {
	exchange     interface{} // 使用interface{}来避免类型问题
	exchangeType ExchangeType
	eventBus     events.EventBus
	
	// 缓存
	balanceCache    *LRUCache   // LRU cache for balances
	addressCache    *LRUCache   // LRU cache for deposit addresses
	cacheTTL        time.Duration
	mu              sync.RWMutex
	
	// 限流管理
	lastRequestTime time.Time
	requestInterval time.Duration
	
	// Cache cleanup stop channel
	cacheCleanupStop chan struct{}
	
	// 活跃的提现追踪
	activeWithdrawals map[string]*withdrawalTracker
	withdrawalsMu     sync.RWMutex
}

// NewExchangeClient 创建交易所客户端
func NewExchangeClient(config *ExchangeConfig) (*ExchangeClient, error) {
	if config == nil {
		return nil, errors.New("config is nil")
	}
	
	if config.APIKey == "" || config.APISecret == "" {
		return nil, errors.New("API key and secret are required")
	}
	
	// 创建交易所实例
	var exchange interface{}
	userConfig := map[string]interface{}{
		"apiKey": config.APIKey,
		"secret": config.APISecret,
	}
	
	// 如果是测试网，添加测试网配置（Coinbase不支持沙盒模式）
	if config.TestNet && config.Type != ExchangeCoinbase {
		userConfig["testnet"] = true
		userConfig["sandbox"] = true
	}
	
	// 根据交易所类型创建实例
	switch config.Type {
	case ExchangeBinance:
		exchange = ccxt.NewBinance(userConfig)
	case ExchangeOKX:
		exchange = ccxt.NewOkx(userConfig)
	case ExchangeBybit:
		exchange = ccxt.NewBybit(userConfig)
	case ExchangeCoinbase:
		exchange = ccxt.NewCoinbase(userConfig)
	default:
		return nil, fmt.Errorf("unsupported exchange type: %s", config.Type)
	}
	
	// 默认配置
	if config.CacheTTL == 0 {
		config.CacheTTL = 5 * time.Minute
	}
	if config.RequestInterval == 0 {
		config.RequestInterval = 100 * time.Millisecond
	}
	
	client := &ExchangeClient{
		exchange:          exchange,
		exchangeType:      config.Type,
		eventBus:          config.EventBus,
		balanceCache:      NewLRUCache(100, config.CacheTTL), // 100 entries max
		addressCache:      NewLRUCache(50, config.CacheTTL),  // 50 entries max
		cacheTTL:          config.CacheTTL,
		requestInterval:   config.RequestInterval,
		activeWithdrawals: make(map[string]*withdrawalTracker),
	}
	
	// Start cache cleanup routine
	client.cacheCleanupStop = client.balanceCache.StartCleanupRoutine(1 * time.Minute)
	client.addressCache.StartCleanupRoutine(1 * time.Minute)
	
	logger.Info().
		Str("exchange", string(config.Type)).
		Bool("testnet", config.TestNet).
		Msg("Exchange client created")
	
	return client, nil
}

// GetSpotBalance 获取现货余额
func (c *ExchangeClient) GetSpotBalance(ctx context.Context) (map[string]*Balance, error) {
	// Try to get from cache first
	cacheKey := "spot_balances"
	if cached, found := c.balanceCache.Get(cacheKey); found {
		if balances, ok := cached.(map[string]*Balance); ok {
			logger.Debug().Msg("Returning cached spot balances")
			return balances, nil
		}
	}
	
	// 限流
	c.enforceRateLimit()
	
	// 根据交易所类型调用对应的方法
	var balances ccxt.Balances
	var err error
	
	switch ex := c.exchange.(type) {
	case *ccxt.Binance:
		balances, err = ex.FetchBalance(nil)
	case *ccxt.Okx:
		balances, err = ex.FetchBalance(nil)
	case *ccxt.Bybit:
		balances, err = ex.FetchBalance(nil)
	case *ccxt.Coinbase:
		balances, err = ex.FetchBalance(nil)
	default:
		return nil, fmt.Errorf("unsupported exchange type for balance fetch")
	}
	
	if err != nil {
		return nil, fmt.Errorf("failed to fetch balance: %w", err)
	}
	
	// 转换格式
	result := make(map[string]*Balance)
	now := time.Now()
	
	// 解析余额
	if balances.Total != nil {
		for currency, value := range balances.Total {
			if value == nil || *value == 0 {
				continue
			}
			
			balance := &Balance{
				Currency:  currency,
				Total:     parseBigInt(*value),
				UpdatedAt: now,
			}
			
			// 获取可用余额
			if balances.Free != nil {
				if freeValue, exists := balances.Free[currency]; exists && freeValue != nil {
					balance.Free = parseBigInt(*freeValue)
				}
			}
			
			// 获取已使用余额
			if balances.Used != nil {
				if usedValue, exists := balances.Used[currency]; exists && usedValue != nil {
					balance.Used = parseBigInt(*usedValue)
				}
			}
			
			result[currency] = balance
		}
	}
	
	// 更新缓存
	c.balanceCache.Set("spot_balances", result)
	
	logger.Info().
		Str("exchange", string(c.exchangeType)).
		Int("currencies", len(result)).
		Msg("Spot balance fetched")
	
	return result, nil
}

// GetDepositAddress 获取充值地址
func (c *ExchangeClient) GetDepositAddress(ctx context.Context, currency string, network string) (*DepositAddress, error) {
	// 检查缓存
	cacheKey := fmt.Sprintf("%s:%s", currency, network)
	if cached, found := c.addressCache.Get(cacheKey); found {
		if addr, ok := cached.(*DepositAddress); ok {
			logger.Debug().
				Str("currency", currency).
				Str("network", network).
				Msg("Returning cached deposit address")
			return addr, nil
		}
	}
	
	// 限流
	c.enforceRateLimit()
	
	// 构建参数
	params := make(map[string]interface{})
	if network != "" {
		params["network"] = network
	}
	
	// 根据交易所类型调用对应的方法
	var addressInfo ccxt.DepositAddress
	var err error
	
	switch ex := c.exchange.(type) {
	case *ccxt.Binance:
		addressInfo, err = ex.FetchDepositAddress(currency, nil)
	case *ccxt.Okx:
		addressInfo, err = ex.FetchDepositAddress(currency, nil)
	case *ccxt.Bybit:
		addressInfo, err = ex.FetchDepositAddress(currency, nil)
	case *ccxt.Coinbase:
		addressInfo, err = ex.FetchDepositAddress(currency, nil)
	default:
		return nil, fmt.Errorf("unsupported exchange type for deposit address")
	}
	
	if err != nil {
		return nil, fmt.Errorf("failed to fetch deposit address: %w", err)
	}
	
	// 解析地址信息
	addr := &DepositAddress{
		Currency:  currency,
		Network:   network,
		UpdatedAt: time.Now(),
	}
	
	// 处理指针类型
	if addressInfo.Address != nil {
		addr.Address = *addressInfo.Address
	}
	if addressInfo.Tag != nil {
		addr.Tag = *addressInfo.Tag
	}
	
	// 更新缓存
	c.addressCache.Set(cacheKey, addr)
	
	logger.Info().
		Str("exchange", string(c.exchangeType)).
		Str("currency", currency).
		Str("network", network).
		Str("address", addr.Address).
		Msg("Deposit address fetched")
	
	return addr, nil
}

// CheckAPIPermissions 检查API权限
func (c *ExchangeClient) CheckAPIPermissions(ctx context.Context) (map[string]bool, error) {
	c.enforceRateLimit()
	
	permissions := make(map[string]bool)
	
	// 尝试获取余额来检查读取权限
	switch ex := c.exchange.(type) {
	case *ccxt.Binance:
		_, err := ex.FetchBalance(nil)
		permissions["read"] = err == nil
	case *ccxt.Okx:
		_, err := ex.FetchBalance(nil)
		permissions["read"] = err == nil
	case *ccxt.Bybit:
		_, err := ex.FetchBalance(nil)
		permissions["read"] = err == nil
	case *ccxt.Coinbase:
		_, err := ex.FetchBalance(nil)
		permissions["read"] = err == nil
	}
	
	// 其他权限暂时设置为false
	permissions["trade"] = false
	permissions["withdraw"] = false
	
	logger.Info().
		Str("exchange", string(c.exchangeType)).
		Interface("permissions", permissions).
		Msg("API permissions checked")
	
	return permissions, nil
}

// enforceRateLimit 限流控制
func (c *ExchangeClient) enforceRateLimit() {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	if !c.lastRequestTime.IsZero() {
		elapsed := time.Since(c.lastRequestTime)
		if elapsed < c.requestInterval {
			time.Sleep(c.requestInterval - elapsed)
		}
	}
	
	c.lastRequestTime = time.Now()
}

// Close 关闭客户端
func (c *ExchangeClient) Close() error {
	// Stop cache cleanup routine
	if c.cacheCleanupStop != nil {
		close(c.cacheCleanupStop)
	}
	
	// 清理缓存
	c.balanceCache.Clear()
	c.addressCache.Clear()
	
	logger.Info().
		Str("exchange", string(c.exchangeType)).
		Msg("Exchange client closed")
	
	return nil
}

// InvalidateBalanceCache invalidates the balance cache
func (c *ExchangeClient) InvalidateBalanceCache() {
	c.balanceCache.Delete("spot_balances")
	logger.Debug().Msg("Balance cache invalidated")
}

// InvalidateAddressCache invalidates specific or all address cache entries
func (c *ExchangeClient) InvalidateAddressCache(currency, network string) {
	if currency == "" && network == "" {
		// Clear all
		c.addressCache.Clear()
		logger.Debug().Msg("All address cache invalidated")
	} else {
		// Clear specific
		cacheKey := fmt.Sprintf("%s:%s", currency, network)
		c.addressCache.Delete(cacheKey)
		logger.Debug().
			Str("currency", currency).
			Str("network", network).
			Msg("Address cache invalidated")
	}
}

// GetCacheMetrics returns cache performance metrics
func (c *ExchangeClient) GetCacheMetrics() map[string]CacheMetrics {
	return map[string]CacheMetrics{
		"balance_cache": c.balanceCache.GetMetrics(),
		"address_cache": c.addressCache.GetMetrics(),
	}
}

// withdrawalTracker tracks an active withdrawal
type withdrawalTracker struct {
	event     *events.CexWithdrawEvent
	startTime time.Time
	status    string
}

// HandleCexWithdraw handles CEX withdrawal events
func (c *ExchangeClient) HandleCexWithdraw(ctx context.Context, event events.Event) error {
	withdrawEvent, ok := event.(*events.CexWithdrawEvent)
	if !ok {
		return fmt.Errorf("invalid event type: expected CexWithdrawEvent")
	}
	
	logger.Info().
		Str("request_id", withdrawEvent.RequestID).
		Str("chain", withdrawEvent.Chain).
		Str("token", withdrawEvent.TokenSymbol).
		Str("amount", withdrawEvent.Amount.String()).
		Str("to_address", withdrawEvent.ToAddress).
		Msg("Processing CEX withdrawal request")
	
	// Track the withdrawal
	c.withdrawalsMu.Lock()
	c.activeWithdrawals[withdrawEvent.RequestID] = &withdrawalTracker{
		event:     withdrawEvent,
		startTime: time.Now(),
		status:    "pending",
	}
	c.withdrawalsMu.Unlock()
	
	// Execute withdrawal asynchronously
	go c.executeWithdrawal(withdrawEvent)
	
	return nil
}

// executeWithdrawal executes the actual withdrawal
func (c *ExchangeClient) executeWithdrawal(event *events.CexWithdrawEvent) {
	// Update status
	c.updateWithdrawalStatus(event.RequestID, "processing")
	
	// Map chain name to exchange network
	network := c.mapChainToNetwork(event.Chain)
	
	// Convert big.Int to float64
	amountFloat := new(big.Float).SetInt(event.Amount)
	decimals := c.getTokenDecimals(event.TokenSymbol)
	divisor := new(big.Float).SetInt(new(big.Int).Exp(big.NewInt(10), big.NewInt(int64(decimals)), nil))
	amountFloat.Quo(amountFloat, divisor)
	amount, _ := amountFloat.Float64()
	
	// Create withdrawal request
	withdrawReq := &WithdrawRequest{
		Coin:    strings.ToUpper(event.TokenSymbol),
		Network: network,
		Address: event.ToAddress,
		Amount:  amount,
		TransID: event.RequestID,
	}
	
	// Execute withdrawal via exchange
	withdrawResp, err := c.Withdraw(context.Background(), withdrawReq)
	
	if err != nil {
		logger.Error().
			Err(err).
			Str("request_id", event.RequestID).
			Str("chain", event.Chain).
			Str("token", event.TokenSymbol).
			Msg("Failed to execute withdrawal")
		
		c.updateWithdrawalStatus(event.RequestID, "failed")
		
		// Publish failure event
		if c.eventBus != nil {
			completedEvent := events.NewWithdrawCompletedEvent(event.RequestID)
			completedEvent.Chain = event.Chain
			completedEvent.Token = event.TokenSymbol
			completedEvent.Amount = event.Amount
			completedEvent.Success = false
			completedEvent.Error = err.Error()
			
			if err := c.eventBus.Publish(context.Background(), completedEvent); err != nil {
				logger.Error().Err(err).Msg("Failed to publish withdraw completed event")
			}
		}
		
		return
	}
	
	logger.Info().
		Str("request_id", event.RequestID).
		Str("exchange_withdrawal_id", withdrawResp.ID).
		Str("chain", event.Chain).
		Str("token", event.TokenSymbol).
		Str("amount", event.Amount.String()).
		Msg("Withdrawal initiated successfully")
	
	// Update status with exchange withdrawal ID
	c.updateWithdrawalStatus(event.RequestID, fmt.Sprintf("submitted:%s", withdrawResp.ID))
	
	// Start monitoring the withdrawal status
	go c.monitorWithdrawal(event, withdrawResp.ID)
}

// monitorWithdrawal monitors the status of a withdrawal until completion
func (c *ExchangeClient) monitorWithdrawal(event *events.CexWithdrawEvent, withdrawalID string) {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			// Check withdrawal status
			withdrawResp, err := c.GetWithdrawStatus(context.Background(), withdrawalID)
			if err != nil {
				logger.Error().
					Err(err).
					Str("request_id", event.RequestID).
					Str("withdrawal_id", withdrawalID).
					Msg("Failed to check withdrawal status")
				continue
			}
			
			// Handle status updates
			switch withdrawResp.Status {
			case WithdrawStatusSuccess:
				logger.Info().
					Str("request_id", event.RequestID).
					Str("chain", event.Chain).
					Str("token", event.TokenSymbol).
					Str("amount", event.Amount.String()).
					Msg("Withdrawal completed successfully")
				
				// Publish success event
				if c.eventBus != nil {
					completedEvent := events.NewWithdrawCompletedEvent(event.RequestID)
					completedEvent.Chain = event.Chain
					completedEvent.Token = event.TokenSymbol
					completedEvent.Amount = event.Amount
					completedEvent.Success = true
					
					if err := c.eventBus.Publish(context.Background(), completedEvent); err != nil {
						logger.Error().Err(err).Msg("Failed to publish withdraw completed event")
					}
				}
				
				// Remove from active withdrawals
				c.removeWithdrawal(event.RequestID)
				return
				
			case WithdrawStatusFailed, WithdrawStatusCancelled:
				logger.Error().
					Str("request_id", event.RequestID).
					Str("status", string(withdrawResp.Status)).
					Msg("Withdrawal failed")
				
				// Publish failure event
				if c.eventBus != nil {
					completedEvent := events.NewWithdrawCompletedEvent(event.RequestID)
					completedEvent.Chain = event.Chain
					completedEvent.Token = event.TokenSymbol
					completedEvent.Amount = event.Amount
					completedEvent.Success = false
					completedEvent.Error = fmt.Sprintf("withdrawal %s", status)
					
					if err := c.eventBus.Publish(context.Background(), completedEvent); err != nil {
						logger.Error().Err(err).Msg("Failed to publish withdraw completed event")
					}
				}
				
				// Remove from active withdrawals
				c.removeWithdrawal(event.RequestID)
				return
				
			default:
				// Still processing, check timeout
				c.withdrawalsMu.RLock()
				tracker, exists := c.activeWithdrawals[event.RequestID]
				c.withdrawalsMu.RUnlock()
				
				if exists && time.Since(tracker.startTime) > 30*time.Minute {
					logger.Warn().
						Str("request_id", event.RequestID).
						Str("status", string(withdrawResp.Status)).
						Msg("Withdrawal timeout, considering failed")
					
					// Publish timeout event
					if c.eventBus != nil {
						completedEvent := events.NewWithdrawCompletedEvent(event.RequestID)
						completedEvent.Chain = event.Chain
						completedEvent.Token = event.TokenSymbol
						completedEvent.Amount = event.Amount
						completedEvent.Success = false
						completedEvent.Error = "withdrawal timeout"
						
						if err := c.eventBus.Publish(context.Background(), completedEvent); err != nil {
							logger.Error().Err(err).Msg("Failed to publish withdraw completed event")
						}
					}
					
					// Remove from active withdrawals
					c.removeWithdrawal(event.RequestID)
					return
				}
			}
		}
	}
}

// updateWithdrawalStatus updates the status of a withdrawal
func (c *ExchangeClient) updateWithdrawalStatus(requestID, status string) {
	c.withdrawalsMu.Lock()
	defer c.withdrawalsMu.Unlock()
	
	if tracker, exists := c.activeWithdrawals[requestID]; exists {
		tracker.status = status
	}
}

// removeWithdrawal removes a withdrawal from tracking
func (c *ExchangeClient) removeWithdrawal(requestID string) {
	c.withdrawalsMu.Lock()
	defer c.withdrawalsMu.Unlock()
	
	delete(c.activeWithdrawals, requestID)
}

// mapChainToNetwork maps chain names to exchange network names
func (c *ExchangeClient) mapChainToNetwork(chain string) string {
	// Map chain names to exchange network names
	// This should be configurable
	networkMap := map[string]string{
		"ethereum":  "ETH",
		"bsc":       "BSC",
		"polygon":   "MATIC",
		"arbitrum":  "ARBITRUM",
		"optimism":  "OPTIMISM",
		"avalanche": "AVAX",
		"fantom":    "FTM",
		"metis":     "METIS",
	}
	
	if network, ok := networkMap[strings.ToLower(chain)]; ok {
		return network
	}
	
	// Default to uppercase chain name
	return strings.ToUpper(chain)
}

// getTokenDecimals returns the decimals for a token
func (c *ExchangeClient) getTokenDecimals(tokenSymbol string) int {
	// Common token decimals - this should be configurable
	decimals := map[string]int{
		"USDT": 6,
		"USDC": 6,
		"DAI":  18,
		"WETH": 18,
		"ETH":  18,
	}
	
	if d, ok := decimals[strings.ToUpper(tokenSymbol)]; ok {
		return d
	}
	
	// Default to 18 decimals
	return 18
}
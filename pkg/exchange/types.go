package exchange

import (
	"math/big"
	"time"
	
	"stargate/pkg/events"
)

// ExchangeType 交易所类型
type ExchangeType string

const (
	ExchangeBinance  ExchangeType = "binance"
	ExchangeOKX      ExchangeType = "okx"
	ExchangeBybit    ExchangeType = "bybit"
	ExchangeCoinbase ExchangeType = "coinbase"
)

// Balance 余额信息
type Balance struct {
	Currency  string    `json:"currency"`
	Free      *big.Int  `json:"free"`      // 可用余额
	Used      *big.Int  `json:"used"`      // 已使用余额
	Total     *big.Int  `json:"total"`     // 总余额
	UpdatedAt time.Time `json:"updated_at"`
}

// DepositAddress 充值地址信息
type DepositAddress struct {
	Currency  string    `json:"currency"`
	Address   string    `json:"address"`
	Tag       string    `json:"tag"`      // memo/tag for some chains
	Network   string    `json:"network"`  // 网络类型
	Chain     string    `json:"chain"`    // 链名称
	UpdatedAt time.Time `json:"updated_at"`
}

// ExchangeConfig 交易所配置
type ExchangeConfig struct {
	Type            ExchangeType
	APIKey          string
	APISecret       string
	TestNet         bool
	CacheTTL        time.Duration
	RequestInterval time.Duration
	EventBus        events.EventBus       // 事件总线
	Options         map[string]interface{} // 额外配置选项
}

// parseBigInt 解析大整数
func parseBigInt(value interface{}) *big.Int {
	switch v := value.(type) {
	case float64:
		// CCXT通常返回float64，需要转换
		// 注意：这可能会有精度损失，对于大数值需要特别处理
		return big.NewInt(int64(v * 1e8)) // 转换为最小单位（聪）
	case string:
		result, ok := new(big.Int).SetString(v, 10)
		if !ok {
			return big.NewInt(0)
		}
		return result
	case int64:
		return big.NewInt(v)
	case *big.Int:
		return v
	default:
		return big.NewInt(0)
	}
}
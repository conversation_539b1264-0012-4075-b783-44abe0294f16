# Exchange Package

The exchange package provides a unified interface for interacting with cryptocurrency exchanges using the CCXT library.

## Features

### Basic Operations
- **Spot Balance**: Get current spot wallet balances
- **Deposit Address**: Generate deposit addresses for different cryptocurrencies
- **API Permissions**: Check API key permissions

### Withdrawal Operations (Binance Only)
- **Execute Withdrawal**: Withdraw funds to external addresses
- **Query Status**: Track withdrawal status by ID
- **Check Limits**: Get withdrawal limits and fees for specific networks
- **Whitelist Verification**: Check if address is whitelisted
- **History Query**: Get withdrawal history with filtering

### Deposit Operations
- **Get Deposit Address**: Generate deposit addresses for cryptocurrencies
- **Query History**: Get deposit history with filtering
- **Monitor Deposits**: Real-time monitoring of deposit status
- **Network Confirmations**: Get required confirmations for different networks
- **Status Notifications**: Callback system for deposit status changes

## Usage

### Creating a Client

```go
config := &ExchangeConfig{
    Type:            ExchangeBinance,
    APIKey:          "your-api-key",
    APISecret:       "your-api-secret",
    TestNet:         false, // Use true for testnet
    CacheTTL:        5 * time.Minute,
    RequestInterval: 100 * time.Millisecond,
}

client, err := NewExchangeClient(config)
if err != nil {
    log.Fatal(err)
}
defer client.Close()
```

### Withdrawing Funds

```go
// Prepare withdrawal request
req := &WithdrawRequest{
    Coin:    "USDT",
    Network: "BSC",     // BEP20 network
    Address: "0x...",   // Destination address
    Amount:  100.0,     // Amount to withdraw
    Tag:     "",        // Optional: memo/tag for some chains
}

// Execute withdrawal
response, err := client.Withdraw(ctx, req)
if err != nil {
    log.Fatal(err)
}

fmt.Printf("Withdrawal ID: %s\n", response.ID)
fmt.Printf("Status: %s\n", response.Status)
```

### Checking Withdrawal Status

```go
// Query withdrawal status
status, err := client.GetWithdrawStatus(ctx, withdrawalID)
if err != nil {
    log.Fatal(err)
}

switch status.Status {
case WithdrawStatusSuccess:
    fmt.Printf("Withdrawal completed! TxID: %s\n", status.TxID)
case WithdrawStatusFailed:
    fmt.Printf("Withdrawal failed: %s\n", status.Info)
case WithdrawStatusProcessing:
    fmt.Println("Withdrawal is still processing...")
}
```

### Checking Withdrawal Limits

```go
// Get withdrawal limits for USDT on BSC network
limits, err := client.GetWithdrawLimits(ctx, "USDT", "BSC")
if err != nil {
    log.Fatal(err)
}

fmt.Printf("Min Amount: %f\n", limits.MinAmount)
fmt.Printf("Max Amount: %f\n", limits.MaxAmount)
fmt.Printf("Fee: %f - %f\n", limits.MinFee, limits.MaxFee)
fmt.Printf("Enabled: %v\n", limits.IsEnabled)
fmt.Printf("Requires Tag: %v\n", limits.NeedTag)
```

### Getting Withdrawal History

```go
// Get withdrawal history for the last 30 days
endTime := time.Now()
startTime := endTime.AddDate(0, 0, -30)

history, err := client.GetWithdrawHistory(ctx, "USDT", &startTime, &endTime, 50)
if err != nil {
    log.Fatal(err)
}

fmt.Printf("Total withdrawals: %d\n", history.Total)
for _, record := range history.Records {
    fmt.Printf("ID: %s, Amount: %f, Status: %s, Time: %s\n",
        record.ID, record.Amount, record.Status, record.SubmitTime)
}
```

### Deposit Monitoring

```go
// Create deposit monitor
monitor := NewDepositMonitor(client, 30*time.Second)

// Set notification callback
monitor.SetNotificationFunc(func(deposit *DepositRecord) {
    fmt.Printf("Deposit %s status: %s (confirmations: %d)\n",
        deposit.ID, deposit.Status, deposit.Confirmations)
    
    if deposit.Status == DepositStatusSuccess {
        // Deposit completed, trigger next actions
    }
})

// Add deposits to watch
deposit := &DepositRecord{
    ID:       "12345",
    Currency: "USDT",
    Amount:   1000.0,
    Status:   DepositStatusPending,
}
monitor.AddWatchedDeposit(deposit)

// Start monitoring
err := monitor.Start()
if err != nil {
    log.Fatal(err)
}
defer monitor.Stop()
```

### Getting Deposit Address

```go
// Get deposit address for USDT on BSC network
addr, err := client.GetDepositAddress(ctx, "USDT", "BSC")
if err != nil {
    log.Fatal(err)
}

fmt.Printf("Deposit Address: %s\n", addr.Address)
if addr.Tag != "" {
    fmt.Printf("Tag/Memo: %s\n", addr.Tag)
}
```

### Checking Deposit History

```go
// Get deposit history for last 7 days
endTime := time.Now()
startTime := endTime.AddDate(0, 0, -7)

history, err := client.GetDepositHistory(ctx, "USDT", &startTime, &endTime, 50)
if err != nil {
    log.Fatal(err)
}

for _, record := range history.Records {
    fmt.Printf("Deposit: %s, Amount: %f, Status: %s, Confirms: %d\n",
        record.ID, record.Amount, record.Status, record.Confirmations)
}
```

## Supported Exchanges

Currently, the package supports the following exchanges through CCXT:
- **Binance** (Full withdrawal support)
- **OKX** (Balance and deposit only)
- **Bybit** (Balance and deposit only)
- **Coinbase** (Balance and deposit only)

Note: Withdrawal functionality is currently only implemented for Binance.

## Security Considerations

1. **API Keys**: Store API keys securely, preferably in environment variables
2. **Permissions**: Use API keys with minimum required permissions
3. **Whitelist**: Enable withdrawal address whitelist on the exchange
4. **2FA**: Enable 2-factor authentication for withdrawal operations
5. **Limits**: Set appropriate daily withdrawal limits on the exchange

## Error Handling

The package provides detailed error messages for common scenarios:
- Invalid parameters
- Insufficient balance
- Network not supported
- Address not whitelisted
- API permission errors
- Rate limiting

## Testing

The package includes comprehensive unit tests and integration tests:

```bash
# Run unit tests
go test ./pkg/exchange

# Run integration tests (requires API keys)
BINANCE_API_KEY=xxx BINANCE_API_SECRET=yyy go test ./pkg/exchange -run Integration
```

## Limitations

1. CCXT Go version may have different APIs than the JavaScript version
2. Some advanced features may not be available in all exchanges
3. Withdrawal limits parsing is simplified due to CCXT Go structure differences
4. Fee information may not be available in withdrawal status queries
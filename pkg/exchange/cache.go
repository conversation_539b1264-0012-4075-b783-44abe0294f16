package exchange

import (
	"container/list"
	"fmt"
	"sync"
	"time"
)

// CacheEntry represents a single cache entry with value and expiration
type CacheEntry struct {
	Key       string
	Value     interface{}
	ExpiresAt time.Time
	element   *list.Element // For LRU tracking
}

// IsExpired checks if the cache entry has expired
func (e *CacheEntry) IsExpired() bool {
	return time.Now().After(e.ExpiresAt)
}

// LRUCache implements a thread-safe LRU cache with TTL support
type LRUCache struct {
	maxSize  int
	ttl      time.Duration
	cache    map[string]*CacheEntry
	lruList  *list.List
	mu       sync.RWMutex
	
	// Metrics
	hits       int64
	misses     int64
	evictions  int64
}

// NewLRUCache creates a new LRU cache with specified size and TTL
func NewLRUCache(maxSize int, ttl time.Duration) *LRUCache {
	if maxSize <= 0 {
		maxSize = 100 // Default size
	}
	if ttl <= 0 {
		ttl = 5 * time.Minute // Default TTL
	}
	
	return &LRUCache{
		maxSize:  maxSize,
		ttl:      ttl,
		cache:    make(map[string]*CacheEntry),
		lruList:  list.New(),
	}
}

// Get retrieves a value from the cache
func (c *LRUCache) Get(key string) (interface{}, bool) {
	c.mu.RLock()
	entry, exists := c.cache[key]
	c.mu.RUnlock()
	
	if !exists {
		c.incrementMisses()
		return nil, false
	}
	
	// Check if expired
	if entry.IsExpired() {
		c.Delete(key)
		c.incrementMisses()
		return nil, false
	}
	
	// Move to front (most recently used)
	c.mu.Lock()
	c.lruList.MoveToFront(entry.element)
	c.mu.Unlock()
	
	c.incrementHits()
	return entry.Value, true
}

// Set adds or updates a value in the cache
func (c *LRUCache) Set(key string, value interface{}) {
	c.SetWithTTL(key, value, c.ttl)
}

// SetWithTTL adds or updates a value with custom TTL
func (c *LRUCache) SetWithTTL(key string, value interface{}, ttl time.Duration) {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	// Check if key already exists
	if entry, exists := c.cache[key]; exists {
		// Update existing entry
		entry.Value = value
		entry.ExpiresAt = time.Now().Add(ttl)
		c.lruList.MoveToFront(entry.element)
		return
	}
	
	// Create new entry
	entry := &CacheEntry{
		Key:       key,
		Value:     value,
		ExpiresAt: time.Now().Add(ttl),
	}
	
	// Add to front of LRU list
	element := c.lruList.PushFront(entry)
	entry.element = element
	c.cache[key] = entry
	
	// Check if we need to evict
	if c.lruList.Len() > c.maxSize {
		c.evictOldest()
	}
}

// Delete removes a key from the cache
func (c *LRUCache) Delete(key string) {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	if entry, exists := c.cache[key]; exists {
		c.lruList.Remove(entry.element)
		delete(c.cache, key)
	}
}

// Clear removes all entries from the cache
func (c *LRUCache) Clear() {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	c.cache = make(map[string]*CacheEntry)
	c.lruList.Init()
}

// Size returns the current number of entries in the cache
func (c *LRUCache) Size() int {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.lruList.Len()
}

// evictOldest removes the least recently used entry
func (c *LRUCache) evictOldest() {
	element := c.lruList.Back()
	if element != nil {
		entry := element.Value.(*CacheEntry)
		c.lruList.Remove(element)
		delete(c.cache, entry.Key)
		c.evictions++
	}
}

// CleanExpired removes all expired entries
func (c *LRUCache) CleanExpired() int {
	c.mu.Lock()
	defer c.mu.Unlock()
	
	var toRemove []*CacheEntry
	now := time.Now()
	
	// Find expired entries
	for _, entry := range c.cache {
		if now.After(entry.ExpiresAt) {
			toRemove = append(toRemove, entry)
		}
	}
	
	// Remove expired entries
	for _, entry := range toRemove {
		c.lruList.Remove(entry.element)
		delete(c.cache, entry.Key)
	}
	
	return len(toRemove)
}

// StartCleanupRoutine starts a background goroutine to clean expired entries
func (c *LRUCache) StartCleanupRoutine(interval time.Duration) chan struct{} {
	if interval <= 0 {
		interval = 1 * time.Minute
	}
	
	stop := make(chan struct{})
	
	go func() {
		ticker := time.NewTicker(interval)
		defer ticker.Stop()
		
		for {
			select {
			case <-ticker.C:
				c.CleanExpired()
			case <-stop:
				return
			}
		}
	}()
	
	return stop
}

// Metrics methods
func (c *LRUCache) incrementHits() {
	c.mu.Lock()
	c.hits++
	c.mu.Unlock()
}

func (c *LRUCache) incrementMisses() {
	c.mu.Lock()
	c.misses++
	c.mu.Unlock()
}

// GetMetrics returns cache performance metrics
func (c *LRUCache) GetMetrics() CacheMetrics {
	c.mu.RLock()
	defer c.mu.RUnlock()
	
	total := c.hits + c.misses
	hitRate := float64(0)
	if total > 0 {
		hitRate = float64(c.hits) / float64(total)
	}
	
	return CacheMetrics{
		Hits:      c.hits,
		Misses:    c.misses,
		Evictions: c.evictions,
		HitRate:   hitRate,
		Size:      int64(c.lruList.Len()),
		MaxSize:   int64(c.maxSize),
	}
}

// CacheMetrics contains cache performance statistics
type CacheMetrics struct {
	Hits      int64   `json:"hits"`
	Misses    int64   `json:"misses"`
	Evictions int64   `json:"evictions"`
	HitRate   float64 `json:"hit_rate"`
	Size      int64   `json:"size"`
	MaxSize   int64   `json:"max_size"`
}

// String returns a string representation of the metrics
func (m CacheMetrics) String() string {
	return fmt.Sprintf("Cache Metrics: Hits=%d, Misses=%d, HitRate=%.2f%%, Size=%d/%d, Evictions=%d",
		m.Hits, m.Misses, m.HitRate*100, m.Size, m.MaxSize, m.Evictions)
}
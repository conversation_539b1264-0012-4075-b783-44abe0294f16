package logger

import (
	"fmt"
	"io"
	"os"
	"path/filepath"
	"sync"

	"github.com/rs/zerolog"
	"time"
	
	"gopkg.in/natefinch/lumberjack.v2"
)

// RotationConfig 日志轮转配置
type RotationConfig struct {
	// Filename 日志文件名
	Filename string
	// MaxSize 单个文件最大大小（MB）
	MaxSize int
	// MaxBackups 保留的旧文件最大数量
	MaxBackups int
	// MaxAge 保留的旧文件最大天数
	MaxAge int
	// Compress 是否压缩旧文件
	Compress bool
	// LocalTime 是否使用本地时间
	LocalTime bool
}

// DefaultRotationConfig 默认轮转配置
func DefaultRotationConfig() RotationConfig {
	return RotationConfig{
		Filename:   "logs/stargate.log",
		MaxSize:    100,
		MaxBackups: 30,
		MaxAge:     7,
		Compress:   true,
		LocalTime:  true,
	}
}

// RotatingWriter 轮转写入器
type RotatingWriter struct {
	writers []io.Writer
	mu      sync.RWMutex
}

// NewRotatingWriter 创建轮转写入器
func NewRotatingWriter(configs ...RotationConfig) *RotatingWriter {
	rw := &RotatingWriter{
		writers: make([]io.Writer, 0),
	}
	
	for _, cfg := range configs {
		writer := &lumberjack.Logger{
			Filename:   cfg.Filename,
			MaxSize:    cfg.MaxSize,
			MaxBackups: cfg.MaxBackups,
			MaxAge:     cfg.MaxAge,
			Compress:   cfg.Compress,
			LocalTime:  cfg.LocalTime,
		}
		rw.writers = append(rw.writers, writer)
	}
	
	return rw
}

// Write 实现 io.Writer 接口
func (rw *RotatingWriter) Write(p []byte) (n int, err error) {
	rw.mu.RLock()
	defer rw.mu.RUnlock()
	
	for _, w := range rw.writers {
		n, err = w.Write(p)
		if err != nil {
			return n, err
		}
	}
	
	return n, nil
}

// AddWriter 添加写入器
func (rw *RotatingWriter) AddWriter(w io.Writer) {
	rw.mu.Lock()
	defer rw.mu.Unlock()
	rw.writers = append(rw.writers, w)
}

// MultiWriter 多目标写入器
type MultiWriter struct {
	writers []io.Writer
	mu      sync.RWMutex
}

// NewMultiWriter 创建多目标写入器
func NewMultiWriter(writers ...io.Writer) *MultiWriter {
	return &MultiWriter{
		writers: writers,
	}
}

// Write 实现 io.Writer 接口
func (mw *MultiWriter) Write(p []byte) (n int, err error) {
	mw.mu.RLock()
	defer mw.mu.RUnlock()
	
	for _, w := range mw.writers {
		n, err = w.Write(p)
		if err != nil {
			return n, err
		}
	}
	
	return n, nil
}

// LoggerWithRotation 创建带轮转的日志记录器
func LoggerWithRotation(cfg Config, rotationCfg RotationConfig) zerolog.Logger {
	// 确保日志目录存在
	logDir := filepath.Dir(rotationCfg.Filename)
	if err := os.MkdirAll(logDir, 0755); err != nil {
		fmt.Fprintf(os.Stderr, "Failed to create log directory: %v\n", err)
	}
	
	// 创建轮转写入器
	fileWriter := &lumberjack.Logger{
		Filename:   rotationCfg.Filename,
		MaxSize:    rotationCfg.MaxSize,
		MaxBackups: rotationCfg.MaxBackups,
		MaxAge:     rotationCfg.MaxAge,
		Compress:   rotationCfg.Compress,
		LocalTime:  rotationCfg.LocalTime,
	}
	
	// 根据格式创建写入器
	var writers []io.Writer
	
	// 总是添加文件写入器
	writers = append(writers, fileWriter)
	
	// 根据配置添加控制台写入器
	if cfg.Format == "console" {
		consoleWriter := zerolog.ConsoleWriter{
			Out:        os.Stdout,
			TimeFormat: "2006-01-02 15:04:05",
			FormatLevel: func(i interface{}) string {
				switch i.(string) {
				case "debug":
					return "DBG"
				case "info":
					return "INF"
				case "warn":
					return "WRN"
				case "error":
					return "ERR"
				default:
					return "???"
				}
			},
		}
		writers = append(writers, consoleWriter)
	} else {
		// JSON格式也输出到控制台
		writers = append(writers, os.Stdout)
	}
	
	// 创建多目标写入器
	multi := io.MultiWriter(writers...)
	
	// 创建日志记录器
	logger := zerolog.New(multi).With().Timestamp().Logger()
	
	// 添加调用者信息
	if cfg.AddCaller {
		logger = logger.With().Caller().Logger()
	}
	
	// 设置日志级别
	level := parseLevel(cfg.Level)
	logger = logger.Level(level)
	
	return logger
}

// InitWithRotation 初始化带轮转的日志系统
func InitWithRotation(cfg Config, rotationCfg RotationConfig) {
	Logger = LoggerWithRotation(cfg, rotationCfg)
	
	// 同时初始化增强日志
	Enhanced = &EnhancedLogger{
		logger: Logger,
		filter: NewSensitiveDataFilter(true),
		sampling: &SamplingConfig{
			Enabled:        false,
			SampleRate:     1.0,
			BurstLimit:     100,
			WindowDuration: time.Minute,
			counters:       make(map[string]*samplingCounter),
		},
		metrics: &LogMetrics{},
	}
	
	// 设置全局日志钩子
	Logger = Enhanced.WithHooks()
}

// FileOnlyLogger 创建仅写入文件的日志记录器
func FileOnlyLogger(filename string, cfg Config) zerolog.Logger {
	// 确保日志目录存在
	logDir := filepath.Dir(filename)
	if err := os.MkdirAll(logDir, 0755); err != nil {
		fmt.Fprintf(os.Stderr, "Failed to create log directory: %v\n", err)
	}
	
	// 创建文件写入器
	file, err := os.OpenFile(filename, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to open log file: %v\n", err)
		return Logger // 返回默认日志记录器
	}
	
	// 创建日志记录器
	logger := zerolog.New(file).With().Timestamp().Logger()
	
	// 添加调用者信息
	if cfg.AddCaller {
		logger = logger.With().Caller().Logger()
	}
	
	// 设置日志级别
	level := parseLevel(cfg.Level)
	logger = logger.Level(level)
	
	return logger
}

// SpecializedLoggers 专用日志记录器
type SpecializedLoggers struct {
	Audit       zerolog.Logger // 审计日志
	Security    zerolog.Logger // 安全日志
	Performance zerolog.Logger // 性能日志
	Error       zerolog.Logger // 错误日志
}

// NewSpecializedLoggers 创建专用日志记录器
func NewSpecializedLoggers(baseDir string, cfg Config) *SpecializedLoggers {
	return &SpecializedLoggers{
		Audit: LoggerWithRotation(cfg, RotationConfig{
			Filename:   filepath.Join(baseDir, "audit.log"),
			MaxSize:    50,
			MaxBackups: 90, // 审计日志保留更长时间
			MaxAge:     90,
			Compress:   true,
			LocalTime:  true,
		}),
		Security: LoggerWithRotation(cfg, RotationConfig{
			Filename:   filepath.Join(baseDir, "security.log"),
			MaxSize:    50,
			MaxBackups: 60,
			MaxAge:     60,
			Compress:   true,
			LocalTime:  true,
		}),
		Performance: LoggerWithRotation(cfg, RotationConfig{
			Filename:   filepath.Join(baseDir, "performance.log"),
			MaxSize:    100,
			MaxBackups: 7,
			MaxAge:     7,
			Compress:   true,
			LocalTime:  true,
		}),
		Error: LoggerWithRotation(cfg, RotationConfig{
			Filename:   filepath.Join(baseDir, "error.log"),
			MaxSize:    100,
			MaxBackups: 30,
			MaxAge:     30,
			Compress:   true,
			LocalTime:  true,
		}),
	}
}
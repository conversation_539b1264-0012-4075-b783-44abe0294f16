package logger

import (
	"os"
	"path/filepath"
	"strconv"

	"github.com/rs/zerolog"
)

var (
	// Logger is the global logger instance
	Logger zerolog.Logger
)

// Config represents logger configuration
type Config struct {
	// Level is the minimum log level (debug, info, warn, error)
	Level string
	// Format is the output format (json, console)
	Format string
	// AddCaller adds file:line to logs
	AddCaller bool
}

// Init initializes the global logger with the given configuration
func Init(cfg Config) {
	// Set log level
	level := parseLevel(cfg.Level)
	zerolog.SetGlobalLevel(level)

	// Configure caller marshaling to show only filename:line
	if cfg.AddCaller {
		zerolog.CallerMarshalFunc = func(pc uintptr, file string, line int) string {
			return filepath.Base(file) + ":" + strconv.Itoa(line)
		}
	}

	// Create logger based on format
	if cfg.Format == "console" {
		// Console writer for development
		consoleWriter := zerolog.ConsoleWriter{
			Out:        os.Stdout,
			TimeFormat: "2006-01-02 15:04:05",
			FormatLevel: func(i interface{}) string {
				switch i.(string) {
				case "debug":
					return "DBG"
				case "info":
					return "INF"
				case "warn":
					return "WRN"
				case "error":
					return "ERR"
				default:
					return "???"
				}
			},
		}
		Logger = zerolog.New(consoleWriter).With().Timestamp().Logger()
	} else {
		// JSON format for production
		Logger = zerolog.New(os.Stdout).With().Timestamp().Logger()
	}

	// Add caller if requested
	if cfg.AddCaller {
		Logger = Logger.With().Caller().Logger()
	}
}

// InitDefault initializes logger with default settings
func InitDefault() {
	Init(Config{
		Level:     "info",
		Format:    "console",
		AddCaller: true,
	})
}

// parseLevel converts string level to zerolog.Level
func parseLevel(level string) zerolog.Level {
	switch level {
	case "debug":
		return zerolog.DebugLevel
	case "info":
		return zerolog.InfoLevel
	case "warn":
		return zerolog.WarnLevel
	case "error":
		return zerolog.ErrorLevel
	default:
		return zerolog.InfoLevel
	}
}

// Helper functions for structured logging

// With creates a child logger with additional context fields
func With() zerolog.Context {
	return Logger.With()
}

// Debug logs a debug message
func Debug() *zerolog.Event {
	return Logger.Debug()
}

// Info logs an info message
func Info() *zerolog.Event {
	return Logger.Info()
}

// Warn logs a warning message
func Warn() *zerolog.Event {
	return Logger.Warn()
}

// Error logs an error message
func Error() *zerolog.Event {
	return Logger.Error()
}

// Fatal logs a fatal message and exits
func Fatal() *zerolog.Event {
	return Logger.Fatal()
}

// Common field helpers for consistency

// Chain adds chain name to log entry
func Chain(event *zerolog.Event, chain string) *zerolog.Event {
	return event.Str("chain", chain)
}

// Token adds token name to log entry
func Token(event *zerolog.Event, token string) *zerolog.Event {
	return event.Str("token", token)
}

// Path adds source and destination chains to log entry
func Path(event *zerolog.Event, src, dst string) *zerolog.Event {
	return event.Str("src", src).Str("dst", dst)
}

// Deficit adds deficit value to log entry
func Deficit(event *zerolog.Event, deficit uint64) *zerolog.Event {
	return event.Uint64("deficit", deficit)
}

// Credit adds credit value to log entry
func Credit(event *zerolog.Event, credit uint64) *zerolog.Event {
	return event.Uint64("credit", credit)
}

// Reward adds reward information to log entry
func Reward(event *zerolog.Event, reward float64, rewardMillionth uint64) *zerolog.Event {
	return event.Float64("reward", reward).Uint64("reward_millionth", rewardMillionth)
}
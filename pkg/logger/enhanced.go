package logger

import (
	"context"
	"fmt"
	"runtime"
	"strings"
	"sync"
	"time"

	"github.com/rs/zerolog"
	"stargate/internal/errors"
)

// EnhancedLogger 增强的日志记录器
type EnhancedLogger struct {
	logger   zerolog.Logger
	filter   *SensitiveDataFilter
	sampling *SamplingConfig
	metrics  *LogMetrics
	mu       sync.RWMutex
}

// SamplingConfig 日志采样配置
type SamplingConfig struct {
	Enabled        bool
	SampleRate     float64           // 0.0 - 1.0
	BurstLimit     int               // 突发限制
	WindowDuration time.Duration     // 时间窗口
	counters       map[string]*samplingCounter
	mu             sync.RWMutex
}

// samplingCounter 采样计数器
type samplingCounter struct {
	count      int
	windowStart time.Time
}

// LogMetrics 日志指标
type LogMetrics struct {
	TotalLogs    int64
	ErrorLogs    int64
	WarnLogs     int64
	FilteredLogs int64
	SampledLogs  int64
	mu           sync.RWMutex
}

var (
	// Enhanced 全局增强日志实例
	Enhanced *EnhancedLogger
	once     sync.Once
)

// InitEnhanced 初始化增强日志系统
func InitEnhanced(cfg Config) {
	once.Do(func() {
		// 初始化基础日志
		Init(cfg)
		
		// 创建增强日志
		Enhanced = &EnhancedLogger{
			logger: Logger,
			filter: NewSensitiveDataFilter(true),
			sampling: &SamplingConfig{
				Enabled:        false,
				SampleRate:     1.0,
				BurstLimit:     100,
				WindowDuration: time.Minute,
				counters:       make(map[string]*samplingCounter),
			},
			metrics: &LogMetrics{},
		}
		
		// 设置全局日志钩子
		Logger = Enhanced.WithHooks()
	})
}

// WithHooks 添加日志钩子
func (el *EnhancedLogger) WithHooks() zerolog.Logger {
	return el.logger.Hook(zerolog.HookFunc(func(e *zerolog.Event, level zerolog.Level, msg string) {
		// 更新指标
		el.updateMetrics(level)
		
		// 敏感信息过滤
		if el.filter.enabled {
			// 过滤消息
			filteredMsg := el.filter.FilterString(msg)
			if filteredMsg != msg {
				el.metrics.mu.Lock()
				el.metrics.FilteredLogs++
				el.metrics.mu.Unlock()
			}
		}
	}))
}

// updateMetrics 更新日志指标
func (el *EnhancedLogger) updateMetrics(level zerolog.Level) {
	el.metrics.mu.Lock()
	defer el.metrics.mu.Unlock()
	
	el.metrics.TotalLogs++
	
	switch level {
	case zerolog.ErrorLevel:
		el.metrics.ErrorLogs++
	case zerolog.WarnLevel:
		el.metrics.WarnLogs++
	}
}

// WithError 记录错误日志
func (el *EnhancedLogger) WithError(err error) *zerolog.Event {
	if err == nil {
		return el.logger.Error()
	}
	
	event := el.logger.Error()
	
	// 检查是否是 AppError
	if appErr, ok := errors.AsAppError(err); ok {
		event = event.
			Str("error_type", string(appErr.Type)).
			Str("error_code", string(appErr.Code)).
			Bool("retryable", appErr.IsRetryable()).
			Time("error_time", appErr.Timestamp)
		
		// 添加错误详情
		if appErr.Details != nil {
			// 过滤敏感信息
			filteredDetails := el.filter.FilterMap(appErr.Details)
			event = event.Interface("error_details", filteredDetails)
		}
		
		// 添加请求ID
		if appErr.RequestID != "" {
			event = event.Str("request_id", appErr.RequestID)
		}
		
		// 添加堆栈（仅在调试模式）
		if Logger.GetLevel() <= zerolog.DebugLevel && len(appErr.Stack) > 0 {
			event = event.Strs("stack", appErr.Stack[:min(5, len(appErr.Stack))])
		}
	}
	
	// 过滤错误消息
	filteredMsg := el.filter.FilterError(err)
	return event.Err(err).Str("error_msg", filteredMsg)
}

// WithRequestID 添加请求ID到日志上下文
func (el *EnhancedLogger) WithRequestID(requestID string) zerolog.Logger {
	return el.logger.With().Str("request_id", requestID).Logger()
}

// WithContext 从上下文提取信息
func (el *EnhancedLogger) WithContext(ctx context.Context) zerolog.Logger {
	logger := el.logger
	
	// 提取请求ID
	if requestID, ok := ctx.Value("request_id").(string); ok {
		logger = logger.With().Str("request_id", requestID).Logger()
	}
	
	// 提取用户ID
	if userID, ok := ctx.Value("user_id").(string); ok {
		logger = logger.With().Str("user_id", userID).Logger()
	}
	
	// 提取追踪ID
	if traceID, ok := ctx.Value("trace_id").(string); ok {
		logger = logger.With().Str("trace_id", traceID).Logger()
	}
	
	return logger
}

// SampleLog 采样日志
func (el *EnhancedLogger) SampleLog(key string, level zerolog.Level) bool {
	if !el.sampling.Enabled {
		return true
	}
	
	el.sampling.mu.Lock()
	defer el.sampling.mu.Unlock()
	
	now := time.Now()
	counter, exists := el.sampling.counters[key]
	
	// 初始化或重置计数器
	if !exists || now.Sub(counter.windowStart) > el.sampling.WindowDuration {
		el.sampling.counters[key] = &samplingCounter{
			count:       0,
			windowStart: now,
		}
		counter = el.sampling.counters[key]
	}
	
	// 检查突发限制
	if counter.count >= el.sampling.BurstLimit {
		el.metrics.mu.Lock()
		el.metrics.SampledLogs++
		el.metrics.mu.Unlock()
		return false
	}
	
	// 采样决策
	if counter.count > 0 && el.sampling.SampleRate < 1.0 {
		// 简单的采样逻辑
		if float64(counter.count)*el.sampling.SampleRate > float64(counter.count+1)*el.sampling.SampleRate {
			el.metrics.mu.Lock()
			el.metrics.SampledLogs++
			el.metrics.mu.Unlock()
			return false
		}
	}
	
	counter.count++
	return true
}

// Performance 记录性能日志
func (el *EnhancedLogger) Performance(operation string, duration time.Duration) *zerolog.Event {
	return el.logger.Info().
		Str("operation", operation).
		Dur("duration", duration).
		Float64("duration_ms", float64(duration.Nanoseconds())/1e6)
}

// Audit 记录审计日志
func (el *EnhancedLogger) Audit(action string, resource string, result string) *zerolog.Event {
	return el.logger.Info().
		Str("audit_action", action).
		Str("audit_resource", resource).
		Str("audit_result", result).
		Time("audit_time", time.Now())
}

// GetMetrics 获取日志指标
func (el *EnhancedLogger) GetMetrics() LogMetrics {
	el.metrics.mu.RLock()
	defer el.metrics.mu.RUnlock()
	return LogMetrics{
		TotalLogs:    el.metrics.TotalLogs,
		ErrorLogs:    el.metrics.ErrorLogs,
		WarnLogs:     el.metrics.WarnLogs,
		FilteredLogs: el.metrics.FilteredLogs,
		SampledLogs:  el.metrics.SampledLogs,
	}
}

// ConfigureSampling 配置日志采样
func (el *EnhancedLogger) ConfigureSampling(enabled bool, sampleRate float64, burstLimit int) {
	el.sampling.mu.Lock()
	defer el.sampling.mu.Unlock()
	
	el.sampling.Enabled = enabled
	el.sampling.SampleRate = sampleRate
	el.sampling.BurstLimit = burstLimit
}

// min 返回较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// SafeGo 安全的协程启动
func SafeGo(fn func(), name string) {
	go func() {
		defer func() {
			if r := recover(); r != nil {
				// 获取堆栈信息
				buf := make([]byte, 4096)
				n := runtime.Stack(buf, false)
				stack := string(buf[:n])
				
				// 记录panic
				Enhanced.logger.Error().
					Str("goroutine", name).
					Interface("panic", r).
					Str("stack", stack).
					Msg("Goroutine panic recovered")
			}
		}()
		fn()
	}()
}

// LogRequest 记录请求日志（用于HTTP中间件）
type RequestLogger struct {
	RequestID  string
	Method     string
	Path       string
	RemoteAddr string
	UserAgent  string
	StartTime  time.Time
}

// Start 开始请求记录
func (rl *RequestLogger) Start() {
	Enhanced.logger.Info().
		Str("request_id", rl.RequestID).
		Str("method", rl.Method).
		Str("path", rl.Path).
		Str("remote_addr", rl.RemoteAddr).
		Str("user_agent", rl.UserAgent).
		Msg("Request started")
}

// End 结束请求记录
func (rl *RequestLogger) End(status int, size int) {
	duration := time.Since(rl.StartTime)
	
	event := Enhanced.logger.Info()
	if status >= 400 {
		event = Enhanced.logger.Warn()
	}
	if status >= 500 {
		event = Enhanced.logger.Error()
	}
	
	event.
		Str("request_id", rl.RequestID).
		Str("method", rl.Method).
		Str("path", rl.Path).
		Int("status", status).
		Int("size", size).
		Dur("duration", duration).
		Float64("duration_ms", float64(duration.Nanoseconds())/1e6).
		Msg("Request completed")
}

// FilterSensitiveFields 过滤结构体中的敏感字段
func FilterSensitiveFields(v interface{}) interface{} {
	if Enhanced == nil || Enhanced.filter == nil {
		return v
	}
	
	// 转换为map进行过滤
	if m, ok := v.(map[string]interface{}); ok {
		return Enhanced.filter.FilterMap(m)
	}
	
	// 对于字符串直接过滤
	if s, ok := v.(string); ok {
		return Enhanced.filter.FilterString(s)
	}
	
	return v
}

// FormatDuration 格式化持续时间
func FormatDuration(d time.Duration) string {
	if d < time.Millisecond {
		return fmt.Sprintf("%dµs", d.Microseconds())
	}
	if d < time.Second {
		return fmt.Sprintf("%dms", d.Milliseconds())
	}
	if d < time.Minute {
		return fmt.Sprintf("%.2fs", d.Seconds())
	}
	return fmt.Sprintf("%.2fm", d.Minutes())
}

// GetCallerInfo 获取调用者信息
func GetCallerInfo(skip int) (string, int, string) {
	pc, file, line, ok := runtime.Caller(skip + 1)
	if !ok {
		return "unknown", 0, "unknown"
	}
	
	fn := runtime.FuncForPC(pc)
	if fn == nil {
		return file, line, "unknown"
	}
	
	// 简化文件路径
	parts := strings.Split(file, "/")
	if len(parts) > 2 {
		file = strings.Join(parts[len(parts)-2:], "/")
	}
	
	// 简化函数名
	funcName := fn.Name()
	lastSlash := strings.LastIndex(funcName, "/")
	if lastSlash >= 0 {
		funcName = funcName[lastSlash+1:]
	}
	
	return file, line, funcName
}
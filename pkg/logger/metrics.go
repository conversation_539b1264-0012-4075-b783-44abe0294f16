package logger

import (
	"sync"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/rs/zerolog"
	"stargate/internal/errors"
)

var (
	// 日志级别计数器
	logLevelCounter = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "stargate_log_messages_total",
			Help: "Total number of log messages by level",
		},
		[]string{"level"},
	)
	
	// 错误类型计数器
	errorTypeCounter = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "stargate_errors_total",
			Help: "Total number of errors by type",
		},
		[]string{"type", "code"},
	)
	
	// 恢复计数器
	recoveryCounter = promauto.NewCounter(
		prometheus.CounterOpts{
			Name: "stargate_panics_recovered_total",
			Help: "Total number of panics recovered",
		},
	)
	
	// 敏感信息过滤计数器
	sensitiveDataFilteredCounter = promauto.NewCounter(
		prometheus.CounterOpts{
			Name: "stargate_sensitive_data_filtered_total",
			Help: "Total number of sensitive data items filtered",
		},
	)
	
	// 日志采样计数器
	logSampledCounter = promauto.NewCounter(
		prometheus.CounterOpts{
			Name: "stargate_logs_sampled_total",
			Help: "Total number of logs dropped due to sampling",
		},
	)
	
	// 日志文件大小
	logFileSizeGauge = promauto.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: "stargate_log_file_size_bytes",
			Help: "Current size of log files in bytes",
		},
		[]string{"filename"},
	)
	
	// 日志延迟直方图
	logLatencyHistogram = promauto.NewHistogram(
		prometheus.HistogramOpts{
			Name:    "stargate_log_write_duration_seconds",
			Help:    "Time taken to write log messages",
			Buckets: prometheus.ExponentialBuckets(0.0001, 2, 10), // 0.1ms to 102.4ms
		},
	)
)

// MetricsLogger 带指标的日志记录器
type MetricsLogger struct {
	logger zerolog.Logger
	mu     sync.RWMutex
}

// NewMetricsLogger 创建带指标的日志记录器
func NewMetricsLogger(logger zerolog.Logger) *MetricsLogger {
	return &MetricsLogger{
		logger: logger,
	}
}

// RecordLogLevel 记录日志级别指标
func RecordLogLevel(level string) {
	logLevelCounter.WithLabelValues(level).Inc()
}

// RecordError 记录错误指标
func RecordError(errorType, errorCode string) {
	errorTypeCounter.WithLabelValues(errorType, errorCode).Inc()
}

// RecordRecovery 记录恢复指标
func RecordRecovery() {
	recoveryCounter.Inc()
}

// RecordSensitiveDataFiltered 记录敏感数据过滤
func RecordSensitiveDataFiltered() {
	sensitiveDataFilteredCounter.Inc()
}

// RecordLogSampled 记录日志采样
func RecordLogSampled() {
	logSampledCounter.Inc()
}

// UpdateLogFileSize 更新日志文件大小
func UpdateLogFileSize(filename string, size int64) {
	logFileSizeGauge.WithLabelValues(filename).Set(float64(size))
}

// RecordLogLatency 记录日志写入延迟
func RecordLogLatency(seconds float64) {
	logLatencyHistogram.Observe(seconds)
}

// MetricsHook Prometheus指标钩子
type MetricsHook struct{}

// Run 实现zerolog.Hook接口
func (h MetricsHook) Run(e *zerolog.Event, level zerolog.Level, msg string) {
	// 记录日志级别
	switch level {
	case zerolog.DebugLevel:
		RecordLogLevel("debug")
	case zerolog.InfoLevel:
		RecordLogLevel("info")
	case zerolog.WarnLevel:
		RecordLogLevel("warn")
	case zerolog.ErrorLevel:
		RecordLogLevel("error")
	case zerolog.FatalLevel:
		RecordLogLevel("fatal")
	case zerolog.PanicLevel:
		RecordLogLevel("panic")
	}
}

// ErrorMetricsMiddleware 错误指标中间件
func ErrorMetricsMiddleware(next func(error) error) func(error) error {
	return func(err error) error {
		if err != nil {
			// 尝试提取错误类型和代码
			if appErr, ok := err.(*errors.AppError); ok {
				RecordError(string(appErr.Type), string(appErr.Code))
			} else {
				RecordError("UNKNOWN", "UNKNOWN")
			}
		}
		return next(err)
	}
}

// LoggerWithMetrics 创建带指标的日志记录器
func LoggerWithMetrics(logger zerolog.Logger) zerolog.Logger {
	return logger.Hook(MetricsHook{})
}

// InitMetrics 初始化日志指标
func InitMetrics() {
	// 注册自定义指标
	prometheus.MustRegister(
		logLevelCounter,
		errorTypeCounter,
		recoveryCounter,
		sensitiveDataFilteredCounter,
		logSampledCounter,
		logFileSizeGauge,
		logLatencyHistogram,
	)
}

// LogStats 日志统计信息
type LogStats struct {
	TotalLogs      int64            `json:"total_logs"`
	LogsByLevel    map[string]int64 `json:"logs_by_level"`
	ErrorsByType   map[string]int64 `json:"errors_by_type"`
	RecoveryCount  int64            `json:"recovery_count"`
	FilteredCount  int64            `json:"filtered_count"`
	SampledCount   int64            `json:"sampled_count"`
	mu             sync.RWMutex
}

// GlobalStats 全局日志统计
var GlobalStats = &LogStats{
	LogsByLevel:  make(map[string]int64),
	ErrorsByType: make(map[string]int64),
}

// RecordLog 记录日志统计
func (s *LogStats) RecordLog(level string) {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	s.TotalLogs++
	s.LogsByLevel[level]++
}

// RecordErrorType 记录错误类型
func (s *LogStats) RecordErrorType(errorType string) {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	s.ErrorsByType[errorType]++
}

// RecordRecovered 记录恢复
func (s *LogStats) RecordRecovered() {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	s.RecoveryCount++
}

// RecordFiltered 记录过滤
func (s *LogStats) RecordFiltered() {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	s.FilteredCount++
}

// RecordSampled 记录采样
func (s *LogStats) RecordSampled() {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	s.SampledCount++
}

// GetStats 获取统计信息的副本
func (s *LogStats) GetStats() LogStats {
	s.mu.RLock()
	defer s.mu.RUnlock()
	
	// 创建副本（不包含mutex）
	stats := LogStats{
		TotalLogs:     s.TotalLogs,
		LogsByLevel:   make(map[string]int64),
		ErrorsByType:  make(map[string]int64),
		RecoveryCount: s.RecoveryCount,
		FilteredCount: s.FilteredCount,
		SampledCount:  s.SampledCount,
	}
	
	// 复制map
	for k, v := range s.LogsByLevel {
		stats.LogsByLevel[k] = v
	}
	for k, v := range s.ErrorsByType {
		stats.ErrorsByType[k] = v
	}
	
	return stats
}

// Reset 重置统计信息
func (s *LogStats) Reset() {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	s.TotalLogs = 0
	s.RecoveryCount = 0
	s.FilteredCount = 0
	s.SampledCount = 0
	s.LogsByLevel = make(map[string]int64)
	s.ErrorsByType = make(map[string]int64)
}
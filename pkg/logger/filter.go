package logger

import (
	"regexp"
	"strings"
)

// SensitivePatterns 定义敏感信息的正则表达式模式
var SensitivePatterns = []struct {
	Name    string
	Pattern *regexp.Regexp
	Mask    string
}{
	{
		Name:    "api_key",
		Pattern: regexp.MustCompile(`(?i)(api[_-]?key|apikey)\s*[:=]\s*["']?([a-zA-Z0-9_\-]{20,})["']?`),
		Mask:    "$1=***REDACTED***",
	},
	{
		Name:    "api_secret",
		Pattern: regexp.MustCompile(`(?i)(api[_-]?secret|apisecret|secret[_-]?key)\s*[:=]\s*["']?([a-zA-Z0-9_\-]{20,})["']?`),
		Mask:    "$1=***REDACTED***",
	},
	{
		Name:    "private_key",
		Pattern: regexp.MustCompile(`(?i)(private[_-]?key|priv[_-]?key)\s*[:=]\s*["']?(0x)?([a-fA-F0-9]{64})["']?`),
		Mask:    "$1=***REDACTED***",
	},
	{
		Name:    "mnemonic",
		Pattern: regexp.MustCompile(`(?i)(mnemonic|seed[_-]?phrase)\s*[:=]\s*["']?(\b\w+(?:\s+\w+){11,23}\b)["']?`),
		Mask:    "$1=***REDACTED***",
	},
	{
		Name:    "password",
		Pattern: regexp.MustCompile(`(?i)(password|passwd|pwd)\s*[:=]\s*["']?([^"'\s]+)["']?`),
		Mask:    "$1=***REDACTED***",
	},
	{
		Name:    "jwt_token",
		Pattern: regexp.MustCompile(`(?i)(bearer|token|jwt)\s*[:=]?\s*["']?(eyJ[a-zA-Z0-9_\-]+\.eyJ[a-zA-Z0-9_\-]+\.[a-zA-Z0-9_\-]+)["']?`),
		Mask:    "$1=***REDACTED***",
	},
	{
		Name:    "ethereum_address",
		Pattern: regexp.MustCompile(`\b(0x[a-fA-F0-9]{40})\b`),
		Mask:    "0x***REDACTED***",
	},
	{
		Name:    "credit_card",
		Pattern: regexp.MustCompile(`\b(\d{4}[\s\-]?)(\d{4}[\s\-]?)(\d{4}[\s\-]?)(\d{4})\b`),
		Mask:    "****-****-****-$4",
	},
	{
		Name:    "email",
		Pattern: regexp.MustCompile(`\b([a-zA-Z0-9._%+-]+)@([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})\b`),
		Mask:    "***@$2",
	},
}

// SensitiveDataFilter 敏感数据过滤器
type SensitiveDataFilter struct {
	enabled  bool
	patterns []struct {
		Name    string
		Pattern *regexp.Regexp
		Mask    string
	}
}

// NewSensitiveDataFilter 创建新的敏感数据过滤器
func NewSensitiveDataFilter(enabled bool) *SensitiveDataFilter {
	return &SensitiveDataFilter{
		enabled:  enabled,
		patterns: SensitivePatterns,
	}
}

// FilterString 过滤字符串中的敏感信息
func (f *SensitiveDataFilter) FilterString(input string) string {
	if !f.enabled || input == "" {
		return input
	}
	
	filtered := input
	for _, pattern := range f.patterns {
		filtered = pattern.Pattern.ReplaceAllString(filtered, pattern.Mask)
	}
	
	return filtered
}

// FilterMap 过滤map中的敏感信息
func (f *SensitiveDataFilter) FilterMap(data map[string]interface{}) map[string]interface{} {
	if !f.enabled || data == nil {
		return data
	}
	
	filtered := make(map[string]interface{})
	for key, value := range data {
		// 检查key是否包含敏感词
		lowerKey := strings.ToLower(key)
		if f.isKeysSensitive(lowerKey) {
			filtered[key] = "***REDACTED***"
			continue
		}
		
		// 递归处理值
		switch v := value.(type) {
		case string:
			filtered[key] = f.FilterString(v)
		case map[string]interface{}:
			filtered[key] = f.FilterMap(v)
		case []interface{}:
			filtered[key] = f.FilterSlice(v)
		default:
			filtered[key] = value
		}
	}
	
	return filtered
}

// FilterSlice 过滤切片中的敏感信息
func (f *SensitiveDataFilter) FilterSlice(data []interface{}) []interface{} {
	if !f.enabled || data == nil {
		return data
	}
	
	filtered := make([]interface{}, len(data))
	for i, item := range data {
		switch v := item.(type) {
		case string:
			filtered[i] = f.FilterString(v)
		case map[string]interface{}:
			filtered[i] = f.FilterMap(v)
		case []interface{}:
			filtered[i] = f.FilterSlice(v)
		default:
			filtered[i] = item
		}
	}
	
	return filtered
}

// isKeysSensitive 检查键名是否包含敏感词
func (f *SensitiveDataFilter) isKeysSensitive(key string) bool {
	sensitiveKeywords := []string{
		"password", "passwd", "pwd",
		"secret", "api_key", "apikey",
		"private_key", "privkey", "priv_key",
		"token", "bearer", "jwt",
		"mnemonic", "seed", "phrase",
		"credential", "auth",
	}
	
	for _, keyword := range sensitiveKeywords {
		if strings.Contains(key, keyword) {
			return true
		}
	}
	
	return false
}

// AddPattern 添加自定义敏感信息模式
func (f *SensitiveDataFilter) AddPattern(name string, pattern string, mask string) error {
	re, err := regexp.Compile(pattern)
	if err != nil {
		return err
	}
	
	f.patterns = append(f.patterns, struct {
		Name    string
		Pattern *regexp.Regexp
		Mask    string
	}{
		Name:    name,
		Pattern: re,
		Mask:    mask,
	})
	
	return nil
}

// FilterError 过滤错误消息中的敏感信息
func (f *SensitiveDataFilter) FilterError(err error) string {
	if err == nil {
		return ""
	}
	return f.FilterString(err.Error())
}
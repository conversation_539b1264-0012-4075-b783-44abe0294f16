package types

import (
	"math/big"
	"time"
)

// TradeOpportunity represents a cross-chain arbitrage opportunity
type TradeOpportunity struct {
	ID              string    `json:"id"`
	Source<PERSON>hain     string    `json:"source_chain"`
	DestChain       string    `json:"dest_chain"`
	Token           string    `json:"token"`
	Amount          *big.Int  `json:"amount"`
	EstimatedProfit *big.Int  `json:"estimated_profit"`
	ProfitUSD       float64   `json:"profit_usd"`
	CreatedAt       time.Time `json:"created_at"`
	ExpiresAt       time.Time `json:"expires_at"`
	Metadata        map[string]interface{} `json:"metadata"`
}

// TradeRequest represents a request to execute a trade
type TradeRequest struct {
	ID           string                 `json:"id"`
	Opportunity  *TradeOpportunity      `json:"opportunity"`
	SourceChain  string                 `json:"source_chain"`
	Dest<PERSON>hain    string                 `json:"dest_chain"`
	TokenSymbol  string                 `json:"token_symbol"`
	TokenAmount  *big.Int               `json:"token_amount"`
	MinAmountOut *big.Int               `json:"min_amount_out"`
	MaxSlippage  float64                `json:"max_slippage"`
	Deadline     time.Time              `json:"deadline"`
	Priority     int                    `json:"priority"`
	Metadata     map[string]interface{} `json:"metadata"`
}
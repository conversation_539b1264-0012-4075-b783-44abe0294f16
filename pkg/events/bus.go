package events

import (
	"context"
	"time"
)

// EventType 定义事件类型
type EventType string

const (
	// Stargate 相关事件类型
	// EventTypeStargateArbitrage Stargate套利机会事件
	EventTypeStargateArbitrage EventType = "stargate.arbitrage"
	// EventTypeStargateExecution Stargate执行请求事件
	EventTypeStargateExecution EventType = "stargate.execution"
	// EventTypeStargateCompleted Stargate交易完成事件
	EventTypeStargateCompleted EventType = "stargate.completed"
	
	// 系统事件类型
	// EventTypeRiskAlert 风险警告事件
	EventTypeRiskAlert EventType = "risk.alert"
	// EventTypeFundingStatus 资金状态事件
	EventTypeFundingStatus EventType = "funding.status"
	
	// 业务决策事件
	// EventTypeValidatedOpportunity 验证通过的套利机会
	EventTypeValidatedOpportunity EventType = "opportunity.validated"
	// EventTypeStargateExecute 执行Stargate交易
	EventTypeStargateExecute EventType = "stargate.execute"
	// EventTypeCexWithdraw CEX提现
	EventTypeCexWithdraw EventType = "cex.withdraw"
	// EventTypeOpportunityRejected 机会被拒绝
	EventTypeOpportunityRejected EventType = "opportunity.rejected"
	
	// 执行结果事件
	// EventTypeExecutionCompleted 执行完成
	EventTypeExecutionCompleted EventType = "execution.completed"
	// EventTypeWithdrawCompleted 提现完成
	EventTypeWithdrawCompleted EventType = "withdraw.completed"
	
	// EventTypeBalanceUpdated 余额更新
	EventTypeBalanceUpdated EventType = "balance.updated"
)

// Event 事件接口
type Event interface {
	// Type 返回事件类型
	Type() EventType
	// Timestamp 返回事件时间戳
	Timestamp() time.Time
	// ID 返回事件唯一标识符
	ID() string
}


// EventHandler 事件处理函数
type EventHandler func(ctx context.Context, event Event) error

// Subscription 订阅接口
type Subscription interface {
	// ID 返回订阅ID
	ID() string
	// Unsubscribe 取消订阅
	Unsubscribe() error
}

// EventBus 事件总线接口
type EventBus interface {
	// Publish 发布事件
	Publish(ctx context.Context, event Event) error
	// PublishBatch 批量发布事件
	PublishBatch(ctx context.Context, events []Event) error
	// Subscribe 订阅事件
	Subscribe(eventType EventType, handler EventHandler) (Subscription, error)
	// SubscribeWithOptions 使用选项订阅事件
	SubscribeWithOptions(eventType EventType, handler EventHandler, opts SubscribeOptions) (Subscription, error)
	// Unsubscribe 取消订阅
	Unsubscribe(sub Subscription) error
	// Shutdown 关闭事件总线
	Shutdown(ctx context.Context) error
}

// SubscribeOptions 订阅选项
type SubscribeOptions struct {
	// BufferSize 事件缓冲区大小
	BufferSize int
	// Async 是否异步处理事件
	Async bool
	// MaxRetries 最大重试次数
	MaxRetries int
	// RetryDelay 重试延迟
	RetryDelay time.Duration
}

// DefaultSubscribeOptions 默认订阅选项
func DefaultSubscribeOptions() SubscribeOptions {
	return SubscribeOptions{
		BufferSize: 100,
		Async:      true,
		MaxRetries: 3,
		RetryDelay: time.Second,
	}
}
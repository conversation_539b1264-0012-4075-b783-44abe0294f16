package events

import (
	"math/big"
)

// ValidatedOpportunityEvent - ArbitrageCoordinator验证通过的机会
// 直接嵌入StargateArbitrageEvent，复用其结构
type ValidatedOpportunityEvent struct {
	*StargateArbitrageEvent
	OpportunityID string // 机会ID（原事件ID）
}

// NewValidatedOpportunityEvent 创建验证通过的机会事件
func NewValidatedOpportunityEvent(opportunityID string) *ValidatedOpportunityEvent {
	return &ValidatedOpportunityEvent{
		StargateArbitrageEvent: &StargateArbitrageEvent{
			BaseEvent: NewBaseEvent(EventTypeValidatedOpportunity),
			ArbPaths:  make([]StgArbPath, 0),
			Metadata:  make(map[string]interface{}),
		},
		OpportunityID: opportunityID,
	}
}

// NewValidatedOpportunityEventFromArbitrage 从StargateArbitrageEvent创建ValidatedOpportunityEvent
func NewValidatedOpportunityEventFromArbitrage(arbEvent *StargateArbitrageEvent) *ValidatedOpportunityEvent {
	// 创建新的BaseEvent以更新事件类型
	newBase := NewBaseEvent(EventTypeValidatedOpportunity)
	
	// 复制原事件但更新BaseEvent
	validatedEvent := &ValidatedOpportunityEvent{
		StargateArbitrageEvent: arbEvent,
		OpportunityID:          arbEvent.ID(),
	}
	
	// 更新事件类型
	validatedEvent.BaseEvent = newBase
	
	return validatedEvent
}

// StargateExecuteEvent - RiskManager决定执行交易
type StargateExecuteEvent struct {
	BaseEvent
	OpportunityID  string
	ExecutionID    string
	SourceChain    string
	DestChain      string
	TokenSymbol    string
	Amount         *big.Int
	MinAmountOut   *big.Int
	WalletAddress  string
}

// NewStargateExecuteEvent 创建执行交易事件
func NewStargateExecuteEvent(opportunityID, executionID string) *StargateExecuteEvent {
	return &StargateExecuteEvent{
		BaseEvent:     NewBaseEvent(EventTypeStargateExecute),
		OpportunityID: opportunityID,
		ExecutionID:   executionID,
	}
}

// CexWithdrawEvent - RiskManager决定从CEX提现
type CexWithdrawEvent struct {
	BaseEvent
	RequestID      string
	Exchange       string
	Chain          string
	TokenSymbol    string
	Amount         *big.Int
	FromAddress    string
	ToAddress      string
	Reason         string
}

// NewCexWithdrawEvent 创建CEX提现事件
func NewCexWithdrawEvent(requestID string) *CexWithdrawEvent {
	return &CexWithdrawEvent{
		BaseEvent: NewBaseEvent(EventTypeCexWithdraw),
		RequestID: requestID,
	}
}

// OpportunityRejectedEvent - RiskManager拒绝执行
type OpportunityRejectedEvent struct {
	BaseEvent
	OpportunityID  string
	Reason         string
	RiskScore      float64
}

// NewOpportunityRejectedEvent 创建机会被拒绝事件
func NewOpportunityRejectedEvent(opportunityID string) *OpportunityRejectedEvent {
	return &OpportunityRejectedEvent{
		BaseEvent:     NewBaseEvent(EventTypeOpportunityRejected),
		OpportunityID: opportunityID,
	}
}

// ExecutionCompletedEvent - 执行完成
type ExecutionCompletedEvent struct {
	BaseEvent
	ExecutionID    string
	OpportunityID  string
	Success        bool
	TxHash         string
	ActualProfit   *big.Int
	GasUsed        *big.Int
	Error          string
}

// NewExecutionCompletedEvent 创建执行完成事件
func NewExecutionCompletedEvent(executionID, opportunityID string) *ExecutionCompletedEvent {
	return &ExecutionCompletedEvent{
		BaseEvent:     NewBaseEvent(EventTypeExecutionCompleted),
		ExecutionID:   executionID,
		OpportunityID: opportunityID,
	}
}

// WithdrawCompletedEvent - 提现完成
type WithdrawCompletedEvent struct {
	BaseEvent
	RequestID      string
	Chain          string   // 提现到的链
	Token          string   // 提现的代币
	Success        bool
	TxHash         string
	Amount         *big.Int
	Error          string
}

// NewWithdrawCompletedEvent 创建提现完成事件
func NewWithdrawCompletedEvent(requestID string) *WithdrawCompletedEvent {
	return &WithdrawCompletedEvent{
		BaseEvent: NewBaseEvent(EventTypeWithdrawCompleted),
		RequestID: requestID,
	}
}

// BalanceUpdatedEvent - 余额更新事件
type BalanceUpdatedEvent struct {
	BaseEvent
	Chain       string   // 链名称
	Token       string   // 代币符号
	Address     string   // 钱包地址
	OldBalance  *big.Int // 旧余额
	NewBalance  *big.Int // 新余额
	Reason      string   // 更新原因（deposit, withdraw, transfer等）
}

// NewBalanceUpdatedEvent 创建余额更新事件
func NewBalanceUpdatedEvent(chain, token, address string) *BalanceUpdatedEvent {
	return &BalanceUpdatedEvent{
		BaseEvent: NewBaseEvent(EventTypeBalanceUpdated),
		Chain:     chain,
		Token:     token,
		Address:   address,
	}
}
package events

import (
	"context"
	"fmt"
	"sync"
	"sync/atomic"
	"time"

	"github.com/google/uuid"
	"stargate/pkg/logger"
)

// BusState 事件总线状态
type BusState int32

const (
	// StateRunning 运行中
	StateRunning BusState = iota
	// StateShuttingDown 正在关闭
	StateShuttingDown
	// StateShutdown 已关闭
	StateShutdown
)

// memoryBus 内存事件总线实现
type memoryBus struct {
	mu          sync.RWMutex
	subscribers map[EventType][]*subscriber
	bufferSize  int
	state       atomic.Int32 // 使用 atomic.Int32 存储 BusState
	wg          sync.WaitGroup
	
	// 关闭相关
	shutdownOnce sync.Once
	shutdownCh   chan struct{}
	
	// 统计信息
	publishedCount  atomic.Int64
	droppedCount    atomic.Int64
	handledCount    atomic.Int64
	errorCount      atomic.Int64
	
	// 性能监控
	startTime    time.Time
	shutdownTime atomic.Value // 存储 time.Time
}

// subscriber 订阅者
type subscriber struct {
	id         string
	eventType  EventType
	handler    <PERSON><PERSON><PERSON><PERSON>
	ch         chan Event
	options    SubscribeOptions
	ctx        context.Context
	cancel     context.CancelFunc
	bus        *memoryBus
	mu         sync.Mutex   // 保护通道操作
	closed     bool         // 标记通道是否已关闭
}

// memorySubscription 订阅实现
type memorySubscription struct {
	id         string
	eventType  EventType
	bus        *memoryBus
	cancelFunc context.CancelFunc
}

// NewMemoryBus 创建内存事件总线
func NewMemoryBus(bufferSize int) EventBus {
	if bufferSize <= 0 {
		bufferSize = 100
	}
	
	bus := &memoryBus{
		subscribers: make(map[EventType][]*subscriber),
		bufferSize:  bufferSize,
		shutdownCh:  make(chan struct{}),
		startTime:   time.Now(),
	}
	bus.state.Store(int32(StateRunning))
	return bus
}

// Publish 发布事件
func (b *memoryBus) Publish(ctx context.Context, event Event) error {
	if b.state.Load() != int32(StateRunning) {
		return ErrEventBusShutdown
	}
	
	if event == nil {
		return ErrInvalidEvent
	}
	
	// 增加发布计数
	b.publishedCount.Add(1)
	
	// 获取订阅者副本
	b.mu.RLock()
	subs, exists := b.subscribers[event.Type()]
	if !exists || len(subs) == 0 {
		b.mu.RUnlock()
		logger.Debug().
			Str("event_type", string(event.Type())).
			Str("event_id", event.ID()).
			Msg("No subscribers for event type")
		return nil
	}
	
	// 复制订阅者列表，避免持有锁时间过长
	subscribersCopy := make([]*subscriber, len(subs))
	copy(subscribersCopy, subs)
	b.mu.RUnlock()
	
	// 异步发送事件到订阅者
	for _, sub := range subscribersCopy {
		// 检查订阅者是否仍然活跃
		select {
		case <-sub.ctx.Done():
			continue
		default:
		}
		
		// 根据订阅选项决定是否异步处理
		if sub.options.Async {
			// 检查通道是否已关闭
			sub.mu.Lock()
			if sub.closed {
				sub.mu.Unlock()
				continue
			}
			sub.mu.Unlock()
			
			// 使用 defer recover 防止 panic
			func() {
				defer func() {
					if r := recover(); r != nil {
						// 通道已关闭，忽略
						logger.Debug().
							Interface("panic", r).
							Str("subscriber_id", sub.id).
							Msg("Recovered from panic while sending event")
					}
				}()
				
				select {
				case sub.ch <- event:
					logger.Debug().
						Str("event_type", string(event.Type())).
						Str("event_id", event.ID()).
						Str("subscriber_id", sub.id).
						Msg("Event sent to subscriber")
				case <-ctx.Done():
					return
				default:
					// 缓冲区满，记录并丢弃
					b.droppedCount.Add(1)
					logger.Warn().
						Str("event_type", string(event.Type())).
						Str("event_id", event.ID()).
						Str("subscriber_id", sub.id).
						Msg("Event buffer full, dropping event")
				}
			}()
			
			// 检查上下文是否已取消
			if ctx.Err() != nil {
				return ctx.Err()
			}
		} else {
			// 同步处理
			if err := sub.handler(ctx, event); err != nil {
				b.errorCount.Add(1)
				logger.Error().
					Err(err).
					Str("event_type", string(event.Type())).
					Str("event_id", event.ID()).
					Str("subscriber_id", sub.id).
					Msg("Event handler error")
			} else {
				b.handledCount.Add(1)
			}
		}
	}
	
	return nil
}

// PublishBatch 批量发布事件
func (b *memoryBus) PublishBatch(ctx context.Context, events []Event) error {
	if b.state.Load() != int32(StateRunning) {
		return ErrEventBusShutdown
	}
	
	if len(events) == 0 {
		return nil
	}
	
	// 按事件类型分组
	eventsByType := make(map[EventType][]Event)
	for _, event := range events {
		if event == nil {
			continue
		}
		eventsByType[event.Type()] = append(eventsByType[event.Type()], event)
	}
	
	// 增加发布计数
	b.publishedCount.Add(int64(len(events)))
	
	// 获取所有相关订阅者
	b.mu.RLock()
	subscribersByType := make(map[EventType][]*subscriber)
	for eventType := range eventsByType {
		if subs, exists := b.subscribers[eventType]; exists && len(subs) > 0 {
			// 复制订阅者列表
			subscribersCopy := make([]*subscriber, len(subs))
			copy(subscribersCopy, subs)
			subscribersByType[eventType] = subscribersCopy
		}
	}
	b.mu.RUnlock()
	
	// 批量处理每种类型的事件
	for eventType, typeEvents := range eventsByType {
		subs, exists := subscribersByType[eventType]
		if !exists || len(subs) == 0 {
			logger.Debug().
				Str("event_type", string(eventType)).
				Int("event_count", len(typeEvents)).
				Msg("No subscribers for event type")
			continue
		}
		
		// 为每个订阅者发送该类型的所有事件
		for _, sub := range subs {
			// 检查订阅者是否仍然活跃
			select {
			case <-sub.ctx.Done():
				continue
			default:
			}
			
			// 批量发送事件到订阅者
			for _, event := range typeEvents {
				// 根据订阅选项决定是否异步处理
				if sub.options.Async {
					// 检查通道是否已关闭
					sub.mu.Lock()
					if sub.closed {
						sub.mu.Unlock()
						continue
					}
					sub.mu.Unlock()
					
					// 使用 defer recover 防止 panic
					func() {
						defer func() {
							if r := recover(); r != nil {
								logger.Debug().
									Interface("panic", r).
									Str("subscriber_id", sub.id).
									Msg("Recovered from panic while sending event")
							}
						}()
						
						select {
						case sub.ch <- event:
							b.handledCount.Add(1)
						case <-ctx.Done():
							return
						case <-sub.ctx.Done():
							return
						default:
							// 通道满了，增加丢弃计数
							b.droppedCount.Add(1)
							logger.Warn().
								Str("event_type", string(event.Type())).
								Str("event_id", event.ID()).
								Str("subscriber_id", sub.id).
								Msg("Event dropped due to full channel")
						}
					}()
				} else {
					// 同步处理
					if err := sub.handler(ctx, event); err != nil {
						b.errorCount.Add(1)
						logger.Error().
							Err(err).
							Str("event_type", string(event.Type())).
							Str("event_id", event.ID()).
							Str("subscriber_id", sub.id).
							Msg("Event handler error")
					} else {
						b.handledCount.Add(1)
					}
				}
			}
		}
	}
	
	return nil
}

// Subscribe 订阅事件
func (b *memoryBus) Subscribe(eventType EventType, handler EventHandler) (Subscription, error) {
	return b.SubscribeWithOptions(eventType, handler, DefaultSubscribeOptions())
}

// SubscribeWithOptions 使用选项订阅事件
func (b *memoryBus) SubscribeWithOptions(eventType EventType, handler EventHandler, opts SubscribeOptions) (Subscription, error) {
	if b.state.Load() != int32(StateRunning) {
		return nil, ErrEventBusShutdown
	}
	
	if handler == nil {
		return nil, ErrInvalidHandler
	}
	
	// 创建订阅者
	ctx, cancel := context.WithCancel(context.Background())
	sub := &subscriber{
		id:        uuid.New().String(),
		eventType: eventType,
		handler:   handler,
		ch:        make(chan Event, opts.BufferSize),
		options:   opts,
		ctx:       ctx,
		cancel:    cancel,
		bus:       b,
		closed:    false,
	}
	
	// 注册订阅者
	b.mu.Lock()
	b.subscribers[eventType] = append(b.subscribers[eventType], sub)
	b.mu.Unlock()
	
	// 如果是异步处理，启动事件处理协程
	if opts.Async {
		b.wg.Add(1)
		go b.handleEvents(sub)
	}
	
	// 创建订阅对象
	subscription := &memorySubscription{
		id:         sub.id,
		eventType:  eventType,
		bus:        b,
		cancelFunc: cancel,
	}
	
	logger.Info().
		Str("event_type", string(eventType)).
		Str("subscriber_id", sub.id).
		Bool("async", opts.Async).
		Int("buffer_size", opts.BufferSize).
		Msg("Subscribed to event")
	
	return subscription, nil
}

// handleEvents 处理异步事件
func (b *memoryBus) handleEvents(sub *subscriber) {
	defer b.wg.Done()
	defer func() {
		// 标记通道已关闭
		sub.mu.Lock()
		sub.closed = true
		sub.mu.Unlock()
		
		// 排空通道中的事件
		for {
			select {
			case _, ok := <-sub.ch:
				if !ok {
					return
				}
			default:
				return
			}
		}
	}()
	
	for {
		select {
		case <-sub.ctx.Done():
			// 订阅已取消
			return
		case event, ok := <-sub.ch:
			if !ok {
				// 通道已关闭
				return
			}
			
			// 处理事件，支持重试
			b.handleEventWithRetry(sub, event)
		}
	}
}

// handleEventWithRetry 处理事件并支持重试
func (b *memoryBus) handleEventWithRetry(sub *subscriber, event Event) {
	ctx := sub.ctx
	maxRetries := sub.options.MaxRetries
	retryDelay := sub.options.RetryDelay
	
	var err error
	for i := 0; i <= maxRetries; i++ {
		// 检查是否已关闭
		select {
		case <-ctx.Done():
			return
		default:
		}
		
		// 处理事件
		func() {
			defer func() {
				if r := recover(); r != nil {
					err = fmt.Errorf("%w: %v", ErrHandlerPanic, r)
					b.errorCount.Add(1)
					logger.Error().
						Interface("panic", r).
						Str("event_type", string(event.Type())).
						Str("event_id", event.ID()).
						Str("subscriber_id", sub.id).
						Msg("Event handler panic")
				}
			}()
			
			err = sub.handler(ctx, event)
		}()
		
		if err == nil {
			b.handledCount.Add(1)
			logger.Debug().
				Str("event_type", string(event.Type())).
				Str("event_id", event.ID()).
				Str("subscriber_id", sub.id).
				Msg("Event handled successfully")
			return
		}
		
		// 如果是最后一次尝试，记录错误
		if i == maxRetries {
			b.errorCount.Add(1)
			logger.Error().
				Err(err).
				Str("event_type", string(event.Type())).
				Str("event_id", event.ID()).
				Str("subscriber_id", sub.id).
				Int("attempts", i+1).
				Msg("Event handler failed after retries")
			return
		}
		
		// 等待后重试
		logger.Warn().
			Err(err).
			Str("event_type", string(event.Type())).
			Str("event_id", event.ID()).
			Str("subscriber_id", sub.id).
			Int("attempt", i+1).
			Int("max_retries", maxRetries).
			Dur("retry_delay", retryDelay).
			Msg("Event handler failed, retrying")
		
		timer := time.NewTimer(retryDelay * time.Duration(i+1))
		select {
		case <-ctx.Done():
			timer.Stop()
			return
		case <-timer.C:
			// 继续重试
		}
	}
}

// Unsubscribe 取消订阅
func (b *memoryBus) Unsubscribe(sub Subscription) error {
	if sub == nil {
		return ErrInvalidEvent
	}
	
	ms, ok := sub.(*memorySubscription)
	if !ok {
		return ErrInvalidEvent
	}
	
	// 调用取消函数
	ms.cancelFunc()
	
	// 从订阅者列表中移除
	b.mu.Lock()
	defer b.mu.Unlock()
	
	subs := b.subscribers[ms.eventType]
	for i, s := range subs {
		if s.id == ms.id {
			// 标记为已关闭
			s.mu.Lock()
			s.closed = true
			s.mu.Unlock()
			
			// 移除订阅者
			b.subscribers[ms.eventType] = append(subs[:i], subs[i+1:]...)
			
			logger.Info().
				Str("event_type", string(ms.eventType)).
				Str("subscriber_id", ms.id).
				Msg("Unsubscribed from event")
			
			return nil
		}
	}
	
	return ErrSubscriptionNotFound
}

// Shutdown 关闭事件总线
func (b *memoryBus) Shutdown(ctx context.Context) error {
	var err error
	b.shutdownOnce.Do(func() {
		err = b.doShutdown(ctx)
	})
	return err
}

// doShutdown 执行实际的关闭逻辑
func (b *memoryBus) doShutdown(ctx context.Context) error {
	// 更新状态为正在关闭
	if !b.state.CompareAndSwap(int32(StateRunning), int32(StateShuttingDown)) {
		// 已经在关闭或已关闭
		return nil
	}
	
	shutdownStart := time.Now()
	logger.Info().
		Dur("uptime", shutdownStart.Sub(b.startTime)).
		Msg("Starting graceful shutdown of memory event bus")
	
	// 关闭通道，通知所有协程
	close(b.shutdownCh)
	
	// 第一步：停止接受新的订阅和事件
	b.mu.Lock()
	allSubs := make([]*subscriber, 0)
	for _, subs := range b.subscribers {
		allSubs = append(allSubs, subs...)
	}
	subCount := len(allSubs)
	b.mu.Unlock()
	
	logger.Info().
		Int("active_subscribers", subCount).
		Msg("Notifying subscribers of shutdown")
	
	// 第二步：通知所有订阅者即将关闭
	b.mu.Lock()
	for eventType, subs := range b.subscribers {
		for _, sub := range subs {
			// 标记为已关闭
			sub.mu.Lock()
			if !sub.closed {
				sub.closed = true
				// 关闭通道前先发送一个nil事件作为关闭信号（如果是异步的）
				if sub.options.Async {
					close(sub.ch)
				}
			}
			sub.mu.Unlock()
			// 取消上下文
			sub.cancel()
		}
		// 清空订阅者列表
		delete(b.subscribers, eventType)
	}
	b.mu.Unlock()
	
	// 第三步：等待所有处理协程完成
	done := make(chan struct{})
	go func() {
		b.wg.Wait()
		close(done)
	}()
	
	// 第四步：等待完成或超时
	select {
	case <-done:
		// 成功关闭
		b.state.Store(int32(StateShutdown))
		b.shutdownTime.Store(time.Now())
		
		shutdownDuration := time.Since(shutdownStart)
		logger.Info().
			Int64("total_published", b.publishedCount.Load()).
			Int64("total_handled", b.handledCount.Load()).
			Int64("total_dropped", b.droppedCount.Load()).
			Int64("total_errors", b.errorCount.Load()).
			Dur("shutdown_duration", shutdownDuration).
			Msg("Memory event bus shutdown complete")
		return nil
		
	case <-ctx.Done():
		// 超时，强制关闭
		b.state.Store(int32(StateShutdown))
		b.shutdownTime.Store(time.Now())
		
		// 统计还有多少协程未完成
		activeCount := 0
		b.mu.RLock()
		for _, subs := range b.subscribers {
			activeCount += len(subs)
		}
		b.mu.RUnlock()
		
		logger.Warn().
			Int("active_handlers", activeCount).
			Dur("timeout", time.Since(shutdownStart)).
			Msg("Memory event bus shutdown timeout, forcing closure")
		return ctx.Err()
	}
}

// GetStats 获取统计信息
func (b *memoryBus) GetStats() map[string]int64 {
	stats := map[string]int64{
		"published": b.publishedCount.Load(),
		"handled":   b.handledCount.Load(),
		"dropped":   b.droppedCount.Load(),
		"errors":    b.errorCount.Load(),
		"state":     int64(b.state.Load()),
	}
	
	// 添加订阅者统计
	b.mu.RLock()
	totalSubs := 0
	for _, subs := range b.subscribers {
		totalSubs += len(subs)
	}
	stats["subscribers"] = int64(totalSubs)
	stats["event_types"] = int64(len(b.subscribers))
	b.mu.RUnlock()
	
	// 添加运行时间
	if shutdownTime, ok := b.shutdownTime.Load().(time.Time); ok {
		stats["uptime_ms"] = shutdownTime.Sub(b.startTime).Milliseconds()
	} else {
		stats["uptime_ms"] = time.Since(b.startTime).Milliseconds()
	}
	
	return stats
}

// GetState 获取事件总线状态
func (b *memoryBus) GetState() BusState {
	return BusState(b.state.Load())
}

// IsRunning 检查事件总线是否正在运行
func (b *memoryBus) IsRunning() bool {
	return b.state.Load() == int32(StateRunning)
}

// IsShuttingDown 检查事件总线是否正在关闭
func (b *memoryBus) IsShuttingDown() bool {
	return b.state.Load() == int32(StateShuttingDown)
}

// IsShutdown 检查事件总线是否已关闭
func (b *memoryBus) IsShutdown() bool {
	return b.state.Load() == int32(StateShutdown)
}

// GetSubscriberInfo 获取订阅者信息
func (b *memoryBus) GetSubscriberInfo() map[EventType][]string {
	b.mu.RLock()
	defer b.mu.RUnlock()
	
	info := make(map[EventType][]string)
	for eventType, subs := range b.subscribers {
		ids := make([]string, 0, len(subs))
		for _, sub := range subs {
			ids = append(ids, sub.id)
		}
		info[eventType] = ids
	}
	return info
}

// WaitForShutdown 等待事件总线完全关闭
func (b *memoryBus) WaitForShutdown(ctx context.Context) error {
	ticker := time.NewTicker(100 * time.Millisecond)
	defer ticker.Stop()
	
	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-ticker.C:
			if b.IsShutdown() {
				return nil
			}
		case <-b.shutdownCh:
			// 关闭信号已发出，继续等待状态变为 Shutdown
			continue
		}
	}
}

// ID 返回订阅ID
func (s *memorySubscription) ID() string {
	return s.id
}

// Unsubscribe 取消订阅
func (s *memorySubscription) Unsubscribe() error {
	return s.bus.Unsubscribe(s)
}
package events

import (
	"errors"
	"math/big"
)

// StargateExecutionBuilder Stargate执行请求事件构建器
type StargateExecutionBuilder struct {
	event *StargateExecutionEvent
}

// NewStargateExecutionBuilder 创建Stargate执行请求事件构建器
func NewStargateExecutionBuilder(opportunityID string) *StargateExecutionBuilder {
	return &StargateExecutionBuilder{
		event: NewStargateExecutionEvent(opportunityID),
	}
}

// WithChains 设置链
func (b *StargateExecutionBuilder) WithChains(source, dest string) *StargateExecutionBuilder {
	b.event.SourceChain = source
	b.event.DestChain = dest
	return b
}

// WithToken 设置代币
func (b *StargateExecutionBuilder) WithToken(symbol string) *StargateExecutionBuilder {
	b.event.TokenSymbol = symbol
	return b
}

// WithAmount 设置金额
func (b *StargateExecutionBuilder) WithAmount(amount, minAmountOut *big.Int) *StargateExecutionBuilder {
	b.event.Amount = amount
	b.event.MinAmountOut = minAmountOut
	return b
}

// WithSlippage 设置滑点
func (b *StargateExecutionBuilder) WithSlippage(maxSlippage float64) *StargateExecutionBuilder {
	b.event.MaxSlippage = maxSlippage
	return b
}

// WithStrategy 设置策略
func (b *StargateExecutionBuilder) WithStrategy(strategy string, priority int) *StargateExecutionBuilder {
	b.event.Strategy = strategy
	b.event.Priority = priority
	return b
}

// WithWallet 设置钱包
func (b *StargateExecutionBuilder) WithWallet(wallet string) *StargateExecutionBuilder {
	b.event.AssignedWallet = wallet
	return b
}

// WithGasLimit 设置Gas限制
func (b *StargateExecutionBuilder) WithGasLimit(gasLimit uint64) *StargateExecutionBuilder {
	b.event.GasLimit = gasLimit
	return b
}

// WithMetadata 添加元数据
func (b *StargateExecutionBuilder) WithMetadata(key string, value interface{}) *StargateExecutionBuilder {
	if b.event.Metadata == nil {
		b.event.Metadata = make(map[string]interface{})
	}
	b.event.Metadata[key] = value
	return b
}

// Build 构建事件
func (b *StargateExecutionBuilder) Build() (*StargateExecutionEvent, error) {
	// 验证必填字段
	if b.event.OpportunityID == "" {
		return nil, errors.New("opportunity ID is required")
	}
	if b.event.SourceChain == "" || b.event.DestChain == "" {
		return nil, errors.New("source and dest chains are required")
	}
	if b.event.TokenSymbol == "" {
		return nil, errors.New("token symbol is required")
	}
	if b.event.Amount == nil || b.event.Amount.Sign() <= 0 {
		return nil, errors.New("amount must be positive")
	}
	
	// 设置默认值
	if b.event.Strategy == "" {
		b.event.Strategy = "default"
	}
	if b.event.MaxSlippage == 0 {
		b.event.MaxSlippage = 0.01 // 1%
	}
	if b.event.GasLimit == 0 {
		b.event.GasLimit = 500000
	}
	
	return b.event, nil
}
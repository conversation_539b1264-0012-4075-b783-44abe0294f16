package events

import "errors"

var (
	// ErrEventBusShutdown 事件总线已关闭
	ErrEventBusShutdown = errors.New("event bus is shutdown")
	
	// ErrInvalidEvent 无效事件
	ErrInvalidEvent = errors.New("invalid event")
	
	// ErrInvalidEventType 无效事件类型
	ErrInvalidEventType = errors.New("invalid event type")
	
	// ErrInvalidHandler 无效处理器
	ErrInvalidHandler = errors.New("invalid handler")
	
	// ErrSubscriptionNotFound 订阅未找到
	ErrSubscriptionNotFound = errors.New("subscription not found")
	
	// ErrEventBufferFull 事件缓冲区满
	ErrEventBufferFull = errors.New("event buffer full")
	
	// ErrHandlerPanic 处理器panic
	ErrHandlerPanic = errors.New("handler panic")
	
	// ErrPublishTimeout 发布超时
	ErrPublishTimeout = errors.New("publish timeout")
	
	// ErrSubscribeTimeout 订阅超时
	ErrSubscribeTimeout = errors.New("subscribe timeout")
)
package events

import (
	"fmt"
	"math/big"
	"time"

	"github.com/google/uuid"
)


// BaseEvent 基础事件实现
type BaseEvent struct {
	id        string
	eventType EventType
	timestamp time.Time
}

// NewBaseEvent 创建基础事件
func NewBaseEvent(eventType EventType) BaseEvent {
	return BaseEvent{
		id:        uuid.New().String(),
		eventType: eventType,
		timestamp: time.Now(),
	}
}

// ID 返回事件ID
func (e BaseEvent) ID() string {
	return e.id
}

// Type 返回事件类型
func (e BaseEvent) Type() EventType {
	return e.eventType
}

// Timestamp 返回事件时间戳
func (e BaseEvent) Timestamp() time.Time {
	return e.timestamp
}

// StargateExecutionEvent Stargate执行请求事件
type StargateExecutionEvent struct {
	BaseEvent
	// 关联的机会ID
	OpportunityID   string
	// 执行参数
	SourceChain     string
	DestChain       string
	TokenSymbol     string
	Amount          *big.Int
	MinAmountOut    *big.Int
	MaxSlippage     float64
	// 执行策略
	Strategy        string
	Priority        int
	// 资源分配
	AssignedWallet  string
	GasLimit        uint64
	// 元数据
	Metadata        map[string]interface{}
}

// NewStargateExecutionEvent 创建Stargate执行请求事件
func NewStargateExecutionEvent(opportunityID string) *StargateExecutionEvent {
	return &StargateExecutionEvent{
		BaseEvent:     NewBaseEvent(EventTypeStargateExecution),
		OpportunityID: opportunityID,
		Metadata:      make(map[string]interface{}),
	}
}

// StargateCompletedEvent Stargate交易完成事件
type StargateCompletedEvent struct {
	BaseEvent
	// 关联的请求ID
	RequestID       string
	OpportunityID   string
	// 执行结果
	Success         bool
	ErrorMessage    string
	// 交易详情
	SourceTxHash    string
	DestTxHash      string
	ActualAmountIn  *big.Int
	ActualAmountOut *big.Int
	ActualProfit    *big.Int
	GasUsed         *big.Int
	// 时间统计
	ExecutionTime   time.Duration
	// 元数据
	Metadata        map[string]interface{}
}

// NewStargateCompletedEvent 创建Stargate交易完成事件
func NewStargateCompletedEvent(requestID, opportunityID string) *StargateCompletedEvent {
	return &StargateCompletedEvent{
		BaseEvent:     NewBaseEvent(EventTypeStargateCompleted),
		RequestID:     requestID,
		OpportunityID: opportunityID,
		Metadata:      make(map[string]interface{}),
	}
}

// RiskAlertEvent 风险警告事件
type RiskAlertEvent struct {
	BaseEvent
	// 风险类型
	AlertType       string
	Severity        string // critical, high, medium, low
	// 相关实体
	RelatedEntity   string // opportunity_id, trade_id, etc.
	EntityType      string // opportunity, trade, wallet, etc.
	// 风险详情
	RiskScore       float64
	Description     string
	RecommendedAction string
	// 元数据
	Metadata        map[string]interface{}
}

// NewRiskAlertEvent 创建风险警告事件
func NewRiskAlertEvent(alertType, severity string) *RiskAlertEvent {
	return &RiskAlertEvent{
		BaseEvent: NewBaseEvent(EventTypeRiskAlert),
		AlertType: alertType,
		Severity:  severity,
		Metadata:  make(map[string]interface{}),
	}
}

// FundingStatusEvent 资金状态事件
type FundingStatusEvent struct {
	BaseEvent
	// 链和钱包
	Chain           string
	WalletAddress   string
	// 资金状态
	TokenBalances   map[string]*big.Int
	NativeBalance   *big.Int
	// 资金需求
	RequiredAmount  *big.Int
	RequiredToken   string
	IsSufficient    bool
	// 建议操作
	SuggestedAction string
	// 元数据
	Metadata        map[string]interface{}
}

// NewFundingStatusEvent 创建资金状态事件
func NewFundingStatusEvent(chain, wallet string) *FundingStatusEvent {
	return &FundingStatusEvent{
		BaseEvent:     NewBaseEvent(EventTypeFundingStatus),
		Chain:         chain,
		WalletAddress: wallet,
		TokenBalances: make(map[string]*big.Int),
		Metadata:      make(map[string]interface{}),
	}
}

// StgArbPath Stargate套利路径（包含完整的源->目标信息）
type StgArbPath struct {
	// 完整路径信息
	SrcChain       string  // 源链（冗余但方便）
	DstChain       string  // 目标链
	TokenSymbol    string  // 代币符号（冗余但方便）
	
	// 端点信息
	DstEndpointID  uint32  // 目标链endpoint ID
	
	// 金额信息
	PathCredit     uint64  // 路径可用credit
	AllocatedAmt   uint64  // 分配的实际金额
	
	// 奖励信息  
	RewardRate     uint64  // 奖励率（百万分之一）
	RewardAmount   float64 // 预估奖励金额
	
	// 路径评分和优先级
	Score          float64 // 综合评分（用于排序）
	Priority       int     // 优先级
}

// PathKey 返回路径的唯一标识
func (p *StgArbPath) PathKey() string {
	return fmt.Sprintf("%s:%s->%s", p.SrcChain, p.TokenSymbol, p.DstChain)
}

// StargateArbitrageEvent Stargate套利事件
type StargateArbitrageEvent struct {
	BaseEvent
	
	// 池标识
	SourceChain    string  // 源链
	TokenSymbol    string  // 代币符号
	
	// 版本控制
	Version        int64   // 版本号（纳秒时间戳）
	BlockHeight    uint64  // 区块高度（可选）
	
	// 池状态快照
	PoolDeficit    uint64  // 池总deficit
	TotalAllocatedAmt uint64  // 总分配金额
	
	// 套利路径列表（已按score排序）
	ArbPaths       []StgArbPath
	
	// 时效性
	CheckedAt      time.Time     // 检查时间
	ValidUntil     time.Time     // 有效期
	CheckInterval  time.Duration // 检查间隔
	
	// 统计信息
	TotalPaths     int     // 总路径数（包括被过滤的）
	ActivePaths    int     // 活跃路径数（ArbPaths的长度）
	
	// 扩展信息
	Metadata       map[string]interface{}
}

// PoolKey 返回池的唯一键
func (e *StargateArbitrageEvent) PoolKey() string {
	return fmt.Sprintf("%s:%s", e.SourceChain, e.TokenSymbol)
}

// IsValid 检查事件是否仍有效
func (e *StargateArbitrageEvent) IsValid() bool {
	return time.Now().Before(e.ValidUntil)
}

// NewStargateArbitrageEvent 创建新的Stargate套利事件
func NewStargateArbitrageEvent(srcChain, token string) *StargateArbitrageEvent {
	now := time.Now()
	return &StargateArbitrageEvent{
		BaseEvent:    NewBaseEvent(EventTypeStargateArbitrage),
		SourceChain:  srcChain,
		TokenSymbol:  token,
		Version:      now.UnixNano(),
		CheckedAt:    now,
		ArbPaths:     make([]StgArbPath, 0),
		Metadata:     make(map[string]interface{}),
	}
}
package monitor

import (
	"context"
	"fmt"
	"slices"
	"sort"
	"sync"
	"time"

	"stargate/internal/config"
	"stargate/pkg/contracts"
	"stargate/pkg/events"
	"stargate/pkg/logger"
	"stargate/pkg/telegram"
)

const (
	// RPC 相关的并发限制
	DeficitCalculationConcurrency = 30 // 限制同时计算 deficit 的并发数
	BatchFetchConcurrency         = 10 // 限制批量获取配置的并发数
)

// CheckResult 表示检查结果
type CheckResult struct {
	SrcChain         string
	DstChain         string
	Token            string
	Deficit          uint64
	Credit           uint64
	RewardableAmount uint64
	Reward           float64
	RewardMillionth  uint64
	Error            error
}

// ChainGraph 表示链和代币的图结构
type ChainGraph struct {
	// Edges 是邻接表，key是源链，value是目标链到路径的映射
	Edges map[string]map[string][]TokenPath
}

// TokenPath 表示代币路径
type TokenPath struct {
	Token      string
	FeeLib     *contracts.FeeLib
	Pool       *contracts.Pool
	EndpointID uint32
}

// PoolKey 表示池的唯一标识
type PoolKey struct {
	Chain string
	Token string
}

// Config 表示 Monitor 的配置
type Config struct {
	// Client 是 Stargate 合约客户端
	Client *contracts.StargateClient
	
	// EventBus 是事件总线
	EventBus events.EventBus
	
	// TelegramClient 是 Telegram 客户端（可选）
	TelegramClient *telegram.Client
	
	// CheckInterval 是检查间隔
	CheckInterval time.Duration
	
	// MinRewardAmount 是最小奖励金额阈值
	MinRewardAmount float64
	
	// MinRewardMillionthByChain 是每个链的最小奖励率阈值
	MinRewardMillionthByChain map[string]uint64
}


// Monitor 是使用事件驱动架构的 Monitor
type Monitor struct {
	config *Config
	
	// Cached graph - built once and reused
	cachedGraph *ChainGraph
	graphOnce   sync.Once
	
	// Context for cancellation
	ctx    context.Context
	cancel context.CancelFunc
	
	// Telegram message channel for async sending
	telegramCh chan string
	telegramWg sync.WaitGroup
	
	// Chain goroutines management
	chainWg sync.WaitGroup
}

// NewMonitor 创建一个新的事件驱动 Monitor
func NewMonitor(config *Config) *Monitor {
	ctx, cancel := context.WithCancel(context.Background())
	
	m := &Monitor{
		config: config,
		ctx:    ctx,
		cancel: cancel,
	}
	
	// 只有配置了 TelegramClient 才创建通道和启动 goroutine
	if config.TelegramClient != nil {
		m.telegramCh = make(chan string, 100) // 缓冲通道，避免阻塞
		m.telegramWg.Add(1)
		go m.telegramSender()
	}
	
	return m
}

// Start 启动监控
func (m *Monitor) Start(ctx context.Context) error {
	logger.Info().Msg("Starting event-driven monitor...")
	
	// 获取所有链配置
	chains := m.config.Client.GetChains()
	if len(chains) == 0 {
		return fmt.Errorf("no chains configured")
	}
	
	// 获取链配置（从config中）
	chainConfigs := config.GetConfig().Chains
	
	// 为每条链启动独立的 goroutine
	for chainName := range chains {
		// 获取该链的检查间隔
		checkInterval := m.config.CheckInterval // 默认使用全局间隔
		if chainConfig, exists := chainConfigs[chainName]; exists && chainConfig.CheckInterval > 0 {
			checkInterval = chainConfig.CheckInterval
		}
		
		// 为每条链启动独立的监控 goroutine
		m.chainWg.Add(1)
		go m.checkChainRewards(ctx, chainName, checkInterval)
		
		logger.Debug().
			Str("chain", chainName).
			Dur("interval", checkInterval).
			Msg("Started chain monitor")
	}
	
	// 等待所有链的 goroutine 完成
	m.chainWg.Wait()
	
	logger.Info().Msg("All chain monitors stopped")
	return nil
}

// Stop 停止监控
func (m *Monitor) Stop() {
	// 取消上下文，通知所有 goroutine 停止
	m.cancel()
	
	// 等待所有链的 goroutine 完成
	m.chainWg.Wait()
	
	// 关闭 Telegram 通道并等待消息发送完成
	if m.config.TelegramClient != nil {
		close(m.telegramCh)
		m.telegramWg.Wait()
	}
	
	logger.Info().Msg("Monitor stopped successfully")
}

// telegramSender 处理 Telegram 消息发送的 goroutine
func (m *Monitor) telegramSender() {
	defer m.telegramWg.Done()
	
	for message := range m.telegramCh {
		if err := m.config.TelegramClient.SendMessage(message); err != nil {
			logger.Error().Err(err).Msg("Failed to send telegram message")
		}
		
		// 简单的速率限制，避免发送过快
		select {
		case <-m.ctx.Done():
			return
		case <-time.After(100 * time.Millisecond):
		}
	}
}

// getMinRewardMillionth 获取指定链的最小奖励率阈值
func (m *Monitor) getMinRewardMillionth(dstChain string) uint64 {
	if threshold, exists := m.config.MinRewardMillionthByChain[dstChain]; exists {
		return threshold
	}
	return m.config.MinRewardMillionthByChain["default"]
}

// getGraph 获取缓存的图结构，如果没有则构建一次
func (m *Monitor) getGraph() *ChainGraph {
	m.graphOnce.Do(func() {
		m.cachedGraph = m.buildGraph()
	})
	return m.cachedGraph
}

// buildGraph 构建链和代币的图结构
func (m *Monitor) buildGraph() *ChainGraph {
	chains := m.config.Client.GetChains()
	graph := &ChainGraph{
		Edges: make(map[string]map[string][]TokenPath),
	}

	// 构建邻接表
	for srcName, srcChain := range chains {
		graph.Edges[srcName] = make(map[string][]TokenPath)

		for token := range srcChain.Stargate.FeeLibs {
			feeLib, err := m.config.Client.GetFeeLib(srcChain.ChainID, token)
			if err != nil {
				logger.Error().
					Err(err).
					Str("chain", srcName).
					Str("token", token).
					Msg("Failed to get fee lib")
				continue
			}

			pool, err := m.config.Client.GetPool(srcChain.ChainID, token)
			if err != nil {
				logger.Error().
					Err(err).
					Str("chain", srcName).
					Str("token", token).
					Msg("Failed to get pool")
				continue
			}

			// 找到所有支持这个代币的目标链
			for dstName, dstChain := range chains {
				if dstChain.ChainID == srcChain.ChainID {
					continue
				}
				if _, exists := dstChain.Stargate.Pools[token]; !exists {
					continue
				}

				// 添加边
				graph.Edges[srcName][dstName] = append(
					graph.Edges[srcName][dstName],
					TokenPath{
						Token:      token,
						FeeLib:     feeLib,
						Pool:       pool,
						EndpointID: dstChain.EndpointID,
					},
				)
			}
		}
	}

	return graph
}


// getFeeConfigFromCache 从缓存中获取费用配置
func getFeeConfigFromCache(cache map[string]map[string]map[uint32]contracts.FeeConfig, chain string, token string, endpointID uint32) (contracts.FeeConfig, bool) {
	if chainConfigs, exists := cache[chain]; exists {
		if tokenConfigs, exists := chainConfigs[token]; exists {
			if config, ok := tokenConfigs[endpointID]; ok {
				return config, true
			}
		}
	}
	return contracts.FeeConfig{}, false
}

// getPathCreditFromCache 从缓存中获取路径 credit
func getPathCreditFromCache(cache map[string]map[uint32]uint64, key string, endpointID uint32) (uint64, bool) {
	if pathCredits, exists := cache[key]; exists {
		if credit, ok := pathCredits[endpointID]; ok {
			return credit, true
		}
	}
	return 0, false
}


// checkChainRewards 检查单个链的奖励机会
func (m *Monitor) checkChainRewards(ctx context.Context, srcChain string, checkInterval time.Duration) {
	defer m.chainWg.Done()
	
	// 创建该链专用的 ticker
	ticker := time.NewTicker(checkInterval)
	defer ticker.Stop()
	
	logger.Debug().
		Str("chain", srcChain).
		Dur("interval", checkInterval).
		Msg("Starting chain monitor")
	
	// 立即执行一次检查
	checkFunc := func() {
		if err := m.checkSingleChainRewards(ctx, srcChain); err != nil {
			if err == context.Canceled {
				return
			}
			logger.Error().
				Err(err).
				Str("chain", srcChain).
				Msg("Failed to check chain rewards")
		}
	}
	
	// 立即执行
	checkFunc()
	
	// 循环检查
	for {
		select {
		case <-ctx.Done():
			logger.Warn().
				Str("chain", srcChain).
				Msg("Chain monitor stopped")
			return
		case <-ticker.C:
			checkFunc()
		}
	}
}

// checkSingleChainRewards 检查单个链的奖励机会
func (m *Monitor) checkSingleChainRewards(ctx context.Context, srcChain string) error {
	graph := m.getGraph()
	
	// 只获取该链相关的路径
	dstChains, exists := graph.Edges[srcChain]
	if !exists {
		return nil // 该链没有出边
	}
	
	// 计算该链的 deficit
	chainDeficits := make(map[PoolKey]uint64)
	var wg sync.WaitGroup
	var mu sync.Mutex
	
	// 获取该链的配置
	chains := m.config.Client.GetChains()
	chain, exists := chains[srcChain]
	if !exists {
		return fmt.Errorf("chain %s not found", srcChain)
	}
	
	// 获取该链的 checkInterval
	checkInterval := m.config.CheckInterval // 默认值
	chainConfigs := config.GetConfig().Chains
	if chainConfig, exists := chainConfigs[srcChain]; exists && chainConfig.CheckInterval > 0 {
		checkInterval = chainConfig.CheckInterval
	}
	
	// 获取该链所有 token 的 deficit
	for token := range chain.Stargate.Pools {
		wg.Add(1)
		go func(chain, tok string) {
			defer wg.Done()
			
			pool, err := m.config.Client.GetPool(m.config.Client.GetChainID(chain), tok)
			if err != nil {
				logger.Error().
					Err(err).
					Str("chain", chain).
					Str("token", tok).
					Msg("Failed to get pool")
				return
			}
			
			deficit, err := pool.CalculateDeficit(m.config.Client)
			if err != nil {
				logger.Error().
					Err(err).
					Str("chain", chain).
					Str("token", tok).
					Msg("Failed to get deficit")
				return
			}
			
			if deficit > 0 {
				mu.Lock()
				chainDeficits[PoolKey{Chain: chain, Token: tok}] = deficit
				mu.Unlock()
				
				logger.Debug().
					Str("chain", chain).
					Str("token", tok).
					Uint64("deficit", deficit).
					Msg("Found deficit")
			}
		}(srcChain, token)
	}
	wg.Wait()
	
	if len(chainDeficits) == 0 {
		return nil // 该链没有 deficit
	}
	
	// 批量获取该链的费用配置
	feeConfigs := m.preBatchFetchChainFeeConfigs(ctx, srcChain, dstChains)
	
	// 批量获取该链的路径 credit
	pathCredits := m.preBatchFetchChainPathCredits(ctx, srcChain, dstChains, chainDeficits)
	
	// 收集有奖励的路径
	type PathReward struct {
		Result CheckResult
		Path   TokenPath
	}
	
	chainTokenPaths := make(map[PoolKey][]PathReward)

	logger.Debug().
		Str("chain", srcChain).
		Int("paths", len(dstChains)).
		Msg("Checking paths")
		
	// 检查所有路径
	for dstChain, paths := range dstChains {
		for _, path := range paths {
			key := PoolKey{Chain: srcChain, Token: path.Token}
			deficit, exists := chainDeficits[key]
			if !exists || deficit == 0 {
				continue
			}
			
			// 从缓存获取费用配置
			feeConfig, hasFeeConfig := getFeeConfigFromCache(feeConfigs, srcChain, path.Token, path.EndpointID)
			if !hasFeeConfig || feeConfig.RewardMillionth == nil || feeConfig.RewardMillionth.Uint64() == 0 {
				continue
			}
			
			// 从缓存获取 credit
			creditKey := fmt.Sprintf("%s:%s", srcChain, path.Token)
			credit, hasCredit := getPathCreditFromCache(pathCredits, creditKey, path.EndpointID)
			if !hasCredit || credit == 0 {
				continue
			}
			
			// 计算奖励
			result := CheckResult{
				SrcChain:         srcChain,
				DstChain:         dstChain,
				Token:            path.Token,
				Deficit:          deficit,
				Credit:           credit,
				RewardMillionth:  feeConfig.RewardMillionth.Uint64(),
				RewardableAmount: min(credit, deficit),
			}
			result.Reward = float64(result.RewardMillionth) / 1000000 * float64(result.RewardableAmount) / 1000000
			
			chainTokenPaths[key] = append(chainTokenPaths[key], PathReward{
				Result: result,
				Path:   path,
			})
		}
	}
	
	// 处理有奖励的路径，创建池级别事件
	for poolKey, pathRewards := range chainTokenPaths {
		// 按奖励从高到低排序
		sort.SliceStable(pathRewards, func(i, j int) bool {
			return pathRewards[i].Result.Reward > pathRewards[j].Result.Reward
		})
		
		// 创建池级别事件
		poolEvent := events.NewStargateArbitrageEvent(poolKey.Chain, poolKey.Token)
		poolEvent.PoolDeficit = pathRewards[0].Result.Deficit
		poolEvent.CheckInterval = checkInterval
		poolEvent.ValidUntil = time.Now().Add(checkInterval) // 有效期为一个检查周期
		poolEvent.TotalPaths = len(pathRewards)
		
		// 处理路径，分配 deficit
		remainingDeficit := poolEvent.PoolDeficit
		
		for i, pr := range pathRewards {
			result := pr.Result
			
			// 先检查奖励率是否满足最小阈值
			minRewardMillionth := m.getMinRewardMillionth(result.DstChain)
			if result.RewardMillionth < minRewardMillionth {
				break
			}
			
			// 根据剩余 deficit 调整实际可执行金额
			actualAmount := min(result.Credit, remainingDeficit)
			if actualAmount == 0 {
				break
			}
			
			// 更新结果中的实际金额和奖励
			result.RewardableAmount = actualAmount
			result.Reward = float64(result.RewardMillionth) / 1000000 * float64(actualAmount) / 1000000
			
			// 检查奖励金额是否满足条件
			if result.Reward < m.config.MinRewardAmount {
				break
			}
			
			// 创建 StgArbPath
			arbPath := events.StgArbPath{
				SrcChain:      result.SrcChain,
				DstChain:      result.DstChain,
				TokenSymbol:   result.Token,
				DstEndpointID: pr.Path.EndpointID,
				PathCredit:    result.Credit,
				AllocatedAmt:  actualAmount,
				RewardRate:    result.RewardMillionth,
				RewardAmount:  result.Reward,
				Score:         result.Reward, // 简单使用奖励作为分数
				Priority:      i,
			}

			
			poolEvent.ArbPaths = append(poolEvent.ArbPaths, arbPath)
			
			// 异步发送 Telegram 通知
			if m.config.TelegramClient != nil {
				sdAmount := float64(actualAmount) / 1000000
				message := fmt.Sprintf(
					"PathV2: <b>%s -> %s (%s)</b>, Amount: <b>%.2f</b>, Rate: %d millionths, Reward: <b>%.2f</b>",
					result.SrcChain, result.DstChain, result.Token, sdAmount, result.RewardMillionth, result.Reward)
				
				select {
				case m.telegramCh <- message:
				default:
					logger.Warn().Msg("Telegram notification channel is full, skipping")
				}
			}
			
			// 减少剩余 deficit
			remainingDeficit -= actualAmount
			
			logger.Debug().
				Str("path", fmt.Sprintf("%s->%s", poolKey.Chain, result.DstChain)).
				Str("token", poolKey.Token).
				Uint64("allocated", actualAmount).
				Float64("reward", result.Reward).
				Uint64("remaining_deficit", remainingDeficit).
				Msg("Allocated deficit to path")
		}
		
		// 设置活跃路径数和总分配金额
		poolEvent.ActivePaths = len(poolEvent.ArbPaths)
		poolEvent.TotalAllocatedAmt = poolEvent.PoolDeficit - remainingDeficit
		
		// 发布池级别事件
		if poolEvent.ActivePaths > 0 {
			if err := m.config.EventBus.Publish(ctx, poolEvent); err != nil {
				logger.Error().
					Err(err).
					Str("pool_key", poolEvent.PoolKey()).
					Int("path_count", poolEvent.ActivePaths).
					Msg("Failed to publish pool arbitrage event")
			} else {
				logger.Info().
					Str("pool_key", poolEvent.PoolKey()).
					Int("total_paths", poolEvent.TotalPaths).
					Int("active_paths", poolEvent.ActivePaths).
					Uint64("pool_deficit", poolEvent.PoolDeficit).
					Uint64("total_allocated", poolEvent.TotalAllocatedAmt).
					Int64("version", poolEvent.Version).
					Msg("Published pool arbitrage event")
			}
		}
	}
	
	return nil
}

// preBatchFetchChainFeeConfigs 批量获取单个链的费用配置
func (m *Monitor) preBatchFetchChainFeeConfigs(ctx context.Context, srcChain string, dstChains map[string][]TokenPath) map[string]map[string]map[uint32]contracts.FeeConfig {
	configs := make(map[string]map[string]map[uint32]contracts.FeeConfig)
	var mu sync.Mutex
	var wg sync.WaitGroup
	
	// 收集需要查询的 endpoint IDs
	tokenEndpoints := make(map[string][]uint32)
	for _, paths := range dstChains {
		for _, path := range paths {
			if !slices.Contains(tokenEndpoints[path.Token], path.EndpointID) {
				tokenEndpoints[path.Token] = append(tokenEndpoints[path.Token], path.EndpointID)
			}
		}
	}
	
	// 为每个 token 获取费用配置
	for token, endpointIDs := range tokenEndpoints {
		wg.Add(1)
		go func(tok string, eids []uint32) {
			defer wg.Done()
			
			feeLib, err := m.config.Client.GetFeeLib(m.config.Client.GetChainID(srcChain), tok)
			if err != nil {
				logger.Error().
					Err(err).
					Str("chain", srcChain).
					Str("token", tok).
					Msg("Failed to get fee lib")
				return
			}
			
			batchConfigs, err := feeLib.BatchFeeConfigs(m.config.Client, eids)
			if err != nil {
				logger.Error().
					Err(err).
					Str("chain", srcChain).
					Str("token", tok).
					Msg("Failed to batch get fee configs")
				return
			}
			
			mu.Lock()
			if _, exists := configs[srcChain]; !exists {
				configs[srcChain] = make(map[string]map[uint32]contracts.FeeConfig)
			}
			if _, exists := configs[srcChain][tok]; !exists {
				configs[srcChain][tok] = make(map[uint32]contracts.FeeConfig)
			}
			for eid, config := range batchConfigs {
				configs[srcChain][tok][eid] = config
			}
			mu.Unlock()
		}(token, endpointIDs)
	}
	
	wg.Wait()
	return configs
}

// preBatchFetchChainPathCredits 批量获取单个链的路径 credit
func (m *Monitor) preBatchFetchChainPathCredits(ctx context.Context, srcChain string, dstChains map[string][]TokenPath, chainDeficits map[PoolKey]uint64) map[string]map[uint32]uint64 {
	credits := make(map[string]map[uint32]uint64)
	var mu sync.Mutex
	var wg sync.WaitGroup
	
	// 收集需要查询的 endpoint IDs
	tokenEndpoints := make(map[string][]uint32)
	for _, paths := range dstChains {
		for _, path := range paths {
			key := PoolKey{Chain: srcChain, Token: path.Token}
			if deficit, exists := chainDeficits[key]; exists && deficit > 0 {
				if !slices.Contains(tokenEndpoints[path.Token], path.EndpointID) {
					tokenEndpoints[path.Token] = append(tokenEndpoints[path.Token], path.EndpointID)
				}
			}
		}
	}
	
	// 为每个 token 获取 credits
	for token, endpointIDs := range tokenEndpoints {
		wg.Add(1)
		go func(tok string, eids []uint32) {
			defer wg.Done()
			
			pool, err := m.config.Client.GetPool(m.config.Client.GetChainID(srcChain), tok)
			if err != nil {
				logger.Error().
					Err(err).
					Str("chain", srcChain).
					Str("token", tok).
					Msg("Failed to get pool")
				return
			}
			
			batchCredits, err := pool.BatchPaths(m.config.Client, eids)
			if err != nil {
				logger.Error().
					Err(err).
					Str("chain", srcChain).
					Str("token", tok).
					Msg("Failed to batch get path credits")
				return
			}
			
			creditKey := fmt.Sprintf("%s:%s", srcChain, tok)
			mu.Lock()
			if _, exists := credits[creditKey]; !exists {
				credits[creditKey] = make(map[uint32]uint64)
			}
			for eid, credit := range batchCredits {
				credits[creditKey][eid] = credit
			}
			mu.Unlock()
		}(token, endpointIDs)
	}
	
	wg.Wait()
	return credits
}
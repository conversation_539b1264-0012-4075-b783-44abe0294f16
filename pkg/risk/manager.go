package risk

import (
	"context"
	"fmt"
	"math/big"
	"sort"
	"strings"
	"sync"
	"time"

	"stargate/internal/config"
	"stargate/pkg/arbitrage"
	"stargate/pkg/events"
	"stargate/pkg/exchange"
	"stargate/pkg/logger"
	"stargate/pkg/wallet"

	"github.com/google/uuid"
)

// pendingOpportunity 待处理的机会
type pendingOpportunity struct {
	opportunity *events.ValidatedOpportunityEvent
	amount      *big.Int  // 交易金额
	score       float64   // 从最佳路径获取的score
	addedAt     time.Time // 加入队列时间
}

// PendingQueue 待处理机会的优先级队列
type PendingQueue struct {
	opportunities []*pendingOpportunity
	byID          map[string]*pendingOpportunity // 快速查找
	mu            sync.RWMutex
}

// NewPendingQueue 创建新的待处理队列
func NewPendingQueue() *PendingQueue {
	return &PendingQueue{
		opportunities: make([]*pendingOpportunity, 0),
		byID:          make(map[string]*pendingOpportunity),
	}
}

// Add 添加机会到队列（自动排序）
func (q *PendingQueue) Add(opp *events.ValidatedOpportunityEvent) {
	q.mu.Lock()
	defer q.mu.Unlock()
	
	// 如果已存在，先删除旧的
	if _, exists := q.byID[opp.OpportunityID]; exists {
		q.removeUnsafe(opp.OpportunityID)
	}
	
	// 提取金额和score（使用第一个路径）
	var amount *big.Int
	var score float64
	if len(opp.ArbPaths) > 0 {
		amount = new(big.Int).SetUint64(uint64(opp.ArbPaths[0].RewardAmount * 1e6))
		score = opp.ArbPaths[0].Score
	} else {
		amount = big.NewInt(0)
		score = 0
	}
	
	pending := &pendingOpportunity{
		opportunity: opp,
		amount:      amount,
		score:       score,
		addedAt:     time.Now(),
	}
	
	// 插入到正确位置保持有序
	idx := sort.Search(len(q.opportunities), func(i int) bool {
		// 金额大的在前
		cmp := q.opportunities[i].amount.Cmp(amount)
		if cmp != 0 {
			return cmp < 0 // 如果当前元素金额小于新元素，返回true
		}
		// 金额相同，score高的在前
		if q.opportunities[i].score != score {
			return q.opportunities[i].score < score
		}
		// score相同，先进先出
		return q.opportunities[i].addedAt.After(pending.addedAt)
	})
	
	// 插入元素
	q.opportunities = append(q.opportunities, nil)
	copy(q.opportunities[idx+1:], q.opportunities[idx:])
	q.opportunities[idx] = pending
	q.byID[opp.OpportunityID] = pending
}

// GetByChainToken 获取特定链/代币的机会（已排序）
func (q *PendingQueue) GetByChainToken(chain, token string) []*pendingOpportunity {
	q.mu.RLock()
	defer q.mu.RUnlock()
	
	result := make([]*pendingOpportunity, 0)
	for _, pending := range q.opportunities {
		if pending.opportunity.SourceChain == chain && 
		   pending.opportunity.TokenSymbol == token {
			result = append(result, pending)
		}
	}
	return result
}

// GetByPoolKey 获取特定池子的机会
func (q *PendingQueue) GetByPoolKey(poolKey string) []*pendingOpportunity {
	q.mu.RLock()
	defer q.mu.RUnlock()
	
	result := make([]*pendingOpportunity, 0)
	for _, pending := range q.opportunities {
		if pending.opportunity.PoolKey() == poolKey {
			result = append(result, pending)
		}
	}
	return result
}

// Remove 从队列中移除机会
func (q *PendingQueue) Remove(opportunityID string) {
	q.mu.Lock()
	defer q.mu.Unlock()
	q.removeUnsafe(opportunityID)
}

// removeUnsafe 内部使用的移除方法（不加锁）
func (q *PendingQueue) removeUnsafe(opportunityID string) {
	pending, exists := q.byID[opportunityID]
	if !exists {
		return
	}
	
	// 从数组中移除
	for i, p := range q.opportunities {
		if p == pending {
			q.opportunities = append(q.opportunities[:i], q.opportunities[i+1:]...)
			break
		}
	}
	
	// 从map中移除
	delete(q.byID, opportunityID)
}

// Size 返回队列大小
func (q *PendingQueue) Size() int {
	q.mu.RLock()
	defer q.mu.RUnlock()
	return len(q.opportunities)
}

// GetAll 获取所有待处理机会（已排序）
func (q *PendingQueue) GetAll() []*events.ValidatedOpportunityEvent {
	q.mu.RLock()
	defer q.mu.RUnlock()
	
	result := make([]*events.ValidatedOpportunityEvent, 0, len(q.opportunities))
	for _, pending := range q.opportunities {
		result = append(result, pending.opportunity)
	}
	return result
}

// Manager 风险管理器
type Manager struct {
	config      *config.RiskConfig
	eventBus    events.EventBus
	blacklist   map[string]bool
	mu          sync.RWMutex
	
	// 每日交易量追踪
	dailyVolume     *big.Float
	volumeResetTime time.Time
	
	// 紧急停止
	emergencyStop bool
	stopReason    string
	
	// 统计信息
	totalAssessed   int64
	totalApproved   int64
	totalRejected   int64
	rejectionReasons map[string]int64
	
	// Event subscription
	subscription events.Subscription
	
	// 钱包管理器
	walletManager *wallet.Manager
	
	// 交易所客户端
	exchangeClient *exchange.ExchangeClient
	
	// 待处理的机会（等待资金）
	pendingQueue *PendingQueue
}

// fundingStatus 资金状态
type fundingStatus struct {
	sufficient   bool
	available    *big.Int
	required     *big.Int
	shortage     *big.Int
	canWithdraw  bool
}

// NewManager 创建新的风险管理器
func NewManager(cfg *config.RiskConfig, eventBus events.EventBus, walletManager *wallet.Manager, exchangeClient *exchange.ExchangeClient) *Manager {
	return &Manager{
		config:          cfg,
		eventBus:        eventBus,
		blacklist:       make(map[string]bool),
		dailyVolume:     big.NewFloat(0),
		volumeResetTime: time.Now().UTC().Truncate(24 * time.Hour),
		rejectionReasons: make(map[string]int64),
		walletManager:   walletManager,
		exchangeClient:  exchangeClient,
		pendingQueue:    NewPendingQueue(),
	}
}

// SetEventSubscription sets the event subscription for cleanup
func (m *Manager) SetEventSubscription(sub events.Subscription) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.subscription = sub
}

// Stop stops the risk manager and cleans up resources
func (m *Manager) Stop() error {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	if m.subscription != nil {
		if err := m.subscription.Unsubscribe(); err != nil {
			logger.Error().Err(err).Msg("Failed to unsubscribe from events")
		}
	}
	
	logger.Info().Msg("Risk manager stopped")
	return nil
}

// HandleValidatedOpportunity 处理验证过的套利机会事件
func (m *Manager) HandleValidatedOpportunity(ctx context.Context, event events.Event) error {
	validatedOpp, ok := event.(*events.ValidatedOpportunityEvent)
	if !ok {
		return fmt.Errorf("invalid event type: expected ValidatedOpportunityEvent")
	}
	
	// 检查并使同一池子的旧机会失效
	m.invalidateOldOpportunities(validatedOpp)
	
	// 获取最佳路径（ArbPaths已经按score排序）
	// todo
	bestPath := validatedOpp.ArbPaths[0]
	
	// 执行风险评估
	assessment := m.assessRiskForPath(validatedOpp, &bestPath)
	
	// 根据评估结果发布相应事件
	if !assessment.approved {
		// 发布拒绝事件
		rejectedEvent := events.NewOpportunityRejectedEvent(validatedOpp.OpportunityID)
		rejectedEvent.Reason = assessment.reason
		rejectedEvent.RiskScore = float64(assessment.score)
		
		return m.eventBus.Publish(ctx, rejectedEvent)
	}
	
	// 将uint64金额转换为big.Int
	amount := new(big.Int).SetUint64(bestPath.AllocatedAmt)
	
	// 检查资金是否充足
	fundingStatus := m.checkFundingStatus(validatedOpp.SourceChain, validatedOpp.TokenSymbol, amount)
	
	if !fundingStatus.sufficient {
		logger.Info().
			Str("opportunity_id", validatedOpp.OpportunityID).
			Str("chain", validatedOpp.SourceChain).
			Str("token", validatedOpp.TokenSymbol).
			Str("required", amount.String()).
			Str("available", fundingStatus.available.String()).
			Str("shortage", fundingStatus.shortage.String()).
			Msg("Insufficient funds for opportunity")
		
		// 如果配置了自动提现且有CEX支持
		if m.config.AutoWithdraw && fundingStatus.canWithdraw {
			// 将机会加入待处理队列
			m.queuePendingOpportunity(validatedOpp)
			
			// 发布提现事件
			withdrawEvent := events.NewCexWithdrawEvent(uuid.New().String())
			withdrawEvent.Exchange = "binance" // TODO: 从配置获取
			withdrawEvent.Chain = validatedOpp.SourceChain
			withdrawEvent.TokenSymbol = validatedOpp.TokenSymbol
			withdrawEvent.Amount = fundingStatus.shortage
			withdrawEvent.ToAddress = m.getWalletAddress(validatedOpp.SourceChain)
			withdrawEvent.Reason = fmt.Sprintf("opportunity_%s_shortage", validatedOpp.OpportunityID)
			
			if err := m.eventBus.Publish(ctx, withdrawEvent); err != nil {
				logger.Error().Err(err).Msg("Failed to publish withdraw event")
				// 即使发布失败，机会已经在队列中，可以手动提现后处理
			}
			
			logger.Info().
				Str("opportunity_id", validatedOpp.OpportunityID).
				Str("chain", validatedOpp.SourceChain).
				Str("token", validatedOpp.TokenSymbol).
				Str("shortage", fundingStatus.shortage.String()).
				Msg("Opportunity queued pending withdrawal")
			
			// 返回nil，不拒绝机会
			return nil
		}
		
		// 只有在不支持自动提现时才拒绝机会
		rejectedEvent := events.NewOpportunityRejectedEvent(validatedOpp.OpportunityID)
		rejectedEvent.Reason = fmt.Sprintf("insufficient funds and no auto-withdraw: need %s, have %s", 
			amount.String(), fundingStatus.available.String())
		rejectedEvent.RiskScore = 0
		
		return m.eventBus.Publish(ctx, rejectedEvent)
	}
	
	// 计算最小输出金额（考虑滑点）
	// expectedProfit := new(big.Int).SetUint64(uint64(bestPath.RewardAmount * 1e6)) // 转换为最小单位
	// minAmountOut := m.calculateMinAmountOut(amount, expectedProfit)
	
	// 发布执行事件
	executeEvent := events.NewStargateExecuteEvent(validatedOpp.OpportunityID, uuid.New().String())
	executeEvent.SourceChain = validatedOpp.SourceChain
	executeEvent.DestChain = bestPath.DstChain
	executeEvent.TokenSymbol = validatedOpp.TokenSymbol
	executeEvent.Amount = amount
	executeEvent.MinAmountOut = amount
	executeEvent.WalletAddress = m.getWalletAddress(validatedOpp.SourceChain)
	
	return m.eventBus.Publish(ctx, executeEvent)
}

// riskAssessmentResult 内部风险评估结果
type riskAssessmentResult struct {
	approved bool
	reason   string
	score    int
	level    arbitrage.RiskLevel
}

// assessRiskForPath 对特定路径执行风险评估
func (m *Manager) assessRiskForPath(opportunity *events.ValidatedOpportunityEvent, path *events.StgArbPath) *riskAssessmentResult {
	m.mu.Lock()
	m.totalAssessed++
	m.mu.Unlock()
	
	logger.Debug().
		Str("opportunity_id", opportunity.OpportunityID).
		Str("path", path.PathKey()).
		Msg("Assessing opportunity risk for path")
	
	// 默认通过
	result := &riskAssessmentResult{
		approved: true,
		level:    arbitrage.RiskLevelLow,
		score:    0,
	}
	
	// 1. 检查紧急停止
	if m.IsEmergencyStopped() {
		result.approved = false
		result.reason = fmt.Sprintf("Emergency stop: %s", m.stopReason)
		result.level = arbitrage.RiskLevelHigh
		m.recordRejection("emergency_stop")
		return result
	}
	
	// 2. 检查代币白名单
	if !m.isTokenWhitelisted(opportunity.TokenSymbol) {
		result.approved = false
		result.reason = "Token not whitelisted"
		result.level = arbitrage.RiskLevelHigh
		result.score = 10
		m.recordRejection("token_not_whitelisted")
		return result
	}
	
	// 3. 检查代币黑名单
	if m.isTokenBlacklisted(opportunity.TokenSymbol) {
		result.approved = false
		result.reason = "Token is blacklisted"
		result.level = arbitrage.RiskLevelHigh
		result.score = 10
		m.recordRejection("token_blacklisted")
		return result
	}
	
	// 4. 检查链白名单
	if !m.isChainWhitelisted(opportunity.SourceChain) || 
	   !m.isChainWhitelisted(path.DstChain) {
		result.approved = false
		result.reason = "Chain not whitelisted"
		result.level = arbitrage.RiskLevelHigh
		result.score = 10
		m.recordRejection("chain_not_whitelisted")
		return result
	}
	
	// 5. 检查最小奖励阈值
	if path.RewardAmount < m.config.MinRewardThreshold {
		result.approved = false
		result.reason = "Reward below minimum threshold"
		result.level = arbitrage.RiskLevelMedium
		result.score = 8
		m.recordRejection("reward_below_threshold")
		return result
	}
	
	// 6. 检查单笔交易限额
	if path.RewardAmount > m.config.MaxSingleTrade {
		result.approved = false
		result.reason = "Trade size exceeds limit"
		result.level = arbitrage.RiskLevelHigh
		result.score = 9
		m.recordRejection("exceeds_single_trade_limit")
		return result
	}

	logger.Info().
		Str("opportunity_id", opportunity.OpportunityID).
		Str("path", path.PathKey()).
		Bool("approved", result.approved).
		Str("level", string(result.level)).
		Int("score", result.score).
		Msg("Risk assessment completed")
	
	return result
}

// checkFundingStatus 检查资金状态
func (m *Manager) checkFundingStatus(chain, token string, required *big.Int) *fundingStatus {
	status := &fundingStatus{
		sufficient:  false,
		required:    required,
		available:   big.NewInt(0),
		shortage:    big.NewInt(0),
		canWithdraw: false,
	}
	
	// 获取主钱包地址
	primaryAddress := m.walletManager.GetPrimaryAddress()
	if primaryAddress.Hex() == "******************************************" {
		logger.Warn().
			Str("chain", chain).
			Str("token", token).
			Msg("No primary wallet address")
		status.shortage = new(big.Int).Set(required)
		return status
	}
	
	// 获取余额
	balance, err := m.walletManager.GetBalance(context.Background(), chain, primaryAddress, token)
	if err != nil {
		logger.Error().
			Err(err).
			Str("chain", chain).
			Str("token", token).
			Msg("Failed to check balance")
		status.shortage = new(big.Int).Set(required)
		return status
	}
	
	status.available = balance.Amount
	
	// 检查是否充足
	if balance.Amount.Cmp(required) >= 0 {
		status.sufficient = true
		return status
	}
	
	// 计算短缺金额
	status.shortage = new(big.Int).Sub(required, balance.Amount)
	
	// 检查是否支持CEX提现（简单判断：有exchange客户端且是USDT/USDC）
	if m.exchangeClient != nil {
		tokenUpper := strings.ToUpper(token)
		if tokenUpper == "USDT" || tokenUpper == "USDC" {
			status.canWithdraw = true
		}
	}
	
	logger.Debug().
		Str("chain", chain).
		Str("token", token).
		Str("required", required.String()).
		Str("available", balance.Amount.String()).
		Str("shortage", status.shortage.String()).
		Bool("can_withdraw", status.canWithdraw).
		Msg("Funding status checked")
	
	return status
}

// calculateMinAmountOut 计算最小输出金额
func (m *Manager) calculateMinAmountOut(amount, expectedProfit *big.Int) *big.Int {
	// 计算考虑滑点后的最小输出金额
	// minOut = amount + expectedProfit * (1 - maxSlippage)
	slippageMultiplier := 1.0 - m.config.MaxSlippage
	adjustedProfit := new(big.Float).SetInt(expectedProfit)
	adjustedProfit.Mul(adjustedProfit, big.NewFloat(slippageMultiplier))
	
	minOut := new(big.Int)
	adjustedProfit.Int(minOut)
	minOut.Add(amount, minOut)
	
	return minOut
}

// getWalletAddress 获取指定链的钱包地址
func (m *Manager) getWalletAddress(chain string) string {
	// 从配置中获取钱包地址
	// 在实际实现中，这应该从 WalletManager 获取
	walletAddresses := map[string]string{
		"ethereum": "******************************************",
		"polygon":  "******************************************",
		"arbitrum": "******************************************",
		"optimism": "******************************************",
		"bsc":      "******************************************",
	}
	
	if addr, ok := walletAddresses[chain]; ok {
		return addr
	}
	
	// 默认地址
	return "******************************************"
}

// CheckSlippage 检查滑点是否在允许范围内
func (m *Manager) CheckSlippage(expectedPrice, actualPrice float64) (bool, float64) {
	if expectedPrice == 0 {
		return false, 0
	}
	
	slippage := (actualPrice - expectedPrice) / expectedPrice
	if slippage < 0 {
		slippage = -slippage
	}
	
	return slippage <= m.config.MaxSlippage, slippage
}

// UpdateDailyVolume 更新每日交易量
func (m *Manager) UpdateDailyVolume(amount float64) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	// 检查是否需要重置计数器
	now := time.Now().UTC().Truncate(24 * time.Hour)
	if now.After(m.volumeResetTime) {
		m.dailyVolume = big.NewFloat(0)
		m.volumeResetTime = now
		logger.Info().Msg("Daily volume counter reset")
	}
	
	// 更新交易量
	m.dailyVolume.Add(m.dailyVolume, big.NewFloat(amount))
	
	volume, _ := m.dailyVolume.Float64()
	logger.Info().
		Float64("daily_volume", volume).
		Float64("limit", m.config.MaxDailyVolume).
		Msg("Daily volume updated")
}

// AddToBlacklist 添加代币到黑名单
func (m *Manager) AddToBlacklist(token string) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	token = strings.ToUpper(token)
	m.blacklist[token] = true
	
	logger.Info().
		Str("token", token).
		Msg("Token added to blacklist")
}

// RemoveFromBlacklist 从黑名单移除代币
func (m *Manager) RemoveFromBlacklist(token string) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	token = strings.ToUpper(token)
	delete(m.blacklist, token)
	
	logger.Info().
		Str("token", token).
		Msg("Token removed from blacklist")
}

// EmergencyStop 紧急停止所有交易
func (m *Manager) EmergencyStop(reason string) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.emergencyStop = true
	m.stopReason = reason
	
	logger.Error().
		Str("reason", reason).
		Msg("EMERGENCY STOP ACTIVATED")
}

// Resume 恢复交易
func (m *Manager) Resume() {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.emergencyStop = false
	m.stopReason = ""
	
	logger.Info().Msg("Trading resumed")
}

// IsEmergencyStopped 检查是否处于紧急停止状态
func (m *Manager) IsEmergencyStopped() bool {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.emergencyStop
}

// GetStatistics 获取风险管理统计信息
func (m *Manager) GetStatistics() map[string]interface{} {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	volume, _ := m.dailyVolume.Float64()
	approvalRate := float64(0)
	if m.totalAssessed > 0 {
		approvalRate = float64(m.totalApproved) / float64(m.totalAssessed) * 100
	}
	
	stats := map[string]interface{}{
		"total_assessed":    m.totalAssessed,
		"total_approved":    m.totalApproved,
		"total_rejected":    m.totalRejected,
		"approval_rate":     approvalRate,
		"daily_volume":      volume,
		"emergency_stop":    m.emergencyStop,
		"rejection_reasons": m.rejectionReasons,
	}
	
	return stats
}

// 内部辅助方法

func (m *Manager) isTokenWhitelisted(token string) bool {
	// 如果白名单为空，允许所有代币
	if len(m.config.TokenWhitelist) == 0 {
		return true
	}
	
	token = strings.ToUpper(token)
	for _, whitelisted := range m.config.TokenWhitelist {
		if strings.ToUpper(whitelisted) == token {
			return true
		}
	}
	return false
}

func (m *Manager) isTokenBlacklisted(token string) bool {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	token = strings.ToUpper(token)
	return m.blacklist[token]
}

func (m *Manager) isChainWhitelisted(chain string) bool {
	// 如果链白名单为空，允许所有链
	if len(m.config.ChainWhitelist) == 0 {
		return true
	}
	
	chain = strings.ToLower(chain)
	for _, whitelisted := range m.config.ChainWhitelist {
		if strings.ToLower(whitelisted) == chain {
			return true
		}
	}
	return false
}

func (m *Manager) recordRejection(reason string) {
	m.mu.Lock()
	defer m.mu.Unlock()
	
	m.totalRejected++
	m.rejectionReasons[reason]++
}

// queuePendingOpportunity 将机会加入待处理队列
func (m *Manager) queuePendingOpportunity(opp *events.ValidatedOpportunityEvent) {
	m.pendingQueue.Add(opp)
	logger.Debug().
		Str("opportunity_id", opp.OpportunityID).
		Int("queue_size", m.pendingQueue.Size()).
		Msg("Opportunity added to pending queue")
}

// invalidateOldOpportunities 使同一池子的旧机会失效
func (m *Manager) invalidateOldOpportunities(newOpp *events.ValidatedOpportunityEvent) {
	poolKey := newOpp.PoolKey()
	oldOpportunities := m.pendingQueue.GetByPoolKey(poolKey)
	invalidatedCount := 0
	
	// 遍历所有同池子的待处理机会
	for _, pending := range oldOpportunities {
		// 如果不是当前机会，则移除
		if pending.opportunity.OpportunityID != newOpp.OpportunityID {
			m.pendingQueue.Remove(pending.opportunity.OpportunityID)
			invalidatedCount++
			
			logger.Info().
				Str("invalidated_id", pending.opportunity.OpportunityID).
				Str("new_id", newOpp.OpportunityID).
				Str("pool_key", poolKey).
				Msg("Invalidated old pending opportunity due to new opportunity")
		}
	}
	
	if invalidatedCount > 0 {
		logger.Info().
			Str("pool_key", poolKey).
			Int("invalidated_count", invalidatedCount).
			Int("remaining_pending", m.pendingQueue.Size()).
			Msg("Invalidated old opportunities for pool")
	}
}

// HandleBalanceUpdated 处理余额更新事件
func (m *Manager) HandleBalanceUpdated(ctx context.Context, event events.Event) error {
	balanceEvent, ok := event.(*events.BalanceUpdatedEvent)
	if !ok {
		return fmt.Errorf("invalid event type: expected BalanceUpdatedEvent")
	}
	
	// 只处理余额增加的情况
	if balanceEvent.NewBalance.Cmp(balanceEvent.OldBalance) <= 0 {
		return nil
	}
	
	logger.Info().
		Str("chain", balanceEvent.Chain).
		Str("token", balanceEvent.Token).
		Str("old_balance", balanceEvent.OldBalance.String()).
		Str("new_balance", balanceEvent.NewBalance.String()).
		Str("reason", balanceEvent.Reason).
		Msg("Balance increased, checking pending opportunities")
	
	// 获取该链/代币的所有待处理机会（已按优先级排序）
	pendingOpps := m.pendingQueue.GetByChainToken(balanceEvent.Chain, balanceEvent.Token)
	
	if len(pendingOpps) == 0 {
		logger.Debug().
			Str("chain", balanceEvent.Chain).
			Str("token", balanceEvent.Token).
			Msg("No pending opportunities for this balance update")
		return nil
	}
	
	logger.Info().
		Str("chain", balanceEvent.Chain).
		Str("token", balanceEvent.Token).
		Int("pending_count", len(pendingOpps)).
		Msg("Re-evaluating pending opportunities after balance update")
	
	// 按优先级顺序处理机会
	for _, pending := range pendingOpps {
		// 检查资金是否充足
		fundingStatus := m.checkFundingStatus(balanceEvent.Chain, balanceEvent.Token, pending.amount)
		
		if fundingStatus.sufficient {
			// 资金充足，从队列中移除并重新处理
			m.pendingQueue.Remove(pending.opportunity.OpportunityID)
			
			logger.Info().
				Str("opportunity_id", pending.opportunity.OpportunityID).
				Str("amount", pending.amount.String()).
				Float64("score", pending.score).
				Msg("Processing pending opportunity after balance update")
			
			// 重新处理机会
			if err := m.HandleValidatedOpportunity(ctx, pending.opportunity); err != nil {
				logger.Error().
					Err(err).
					Str("opportunity_id", pending.opportunity.OpportunityID).
					Msg("Failed to re-evaluate pending opportunity")
			}
		} else {
			// 资金仍然不足，保留在队列中
			logger.Debug().
				Str("opportunity_id", pending.opportunity.OpportunityID).
				Str("required", pending.amount.String()).
				Str("available", fundingStatus.available.String()).
				Msg("Opportunity still pending due to insufficient funds")
		}
	}
	
	return nil
}

// HandleWithdrawCompleted 处理提现完成事件（保留作为备用）
func (m *Manager) HandleWithdrawCompleted(ctx context.Context, event events.Event) error {
	withdrawEvent, ok := event.(*events.WithdrawCompletedEvent)
	if !ok {
		return fmt.Errorf("invalid event type: expected WithdrawCompletedEvent")
	}
	
	logger.Info().
		Str("chain", withdrawEvent.Chain).
		Str("token", withdrawEvent.Token).
		Str("amount", withdrawEvent.Amount.String()).
		Bool("success", withdrawEvent.Success).
		Msg("Handling withdraw completed event")
	
	if !withdrawEvent.Success {
		// 提现失败，不处理待处理的机会
		return nil
	}
	
	// 创建一个余额更新事件来复用处理逻辑
	balanceEvent := events.NewBalanceUpdatedEvent(
		withdrawEvent.Chain,
		withdrawEvent.Token,
		"", // 地址未知
	)
	balanceEvent.OldBalance = big.NewInt(0) // 假设之前余额为0
	balanceEvent.NewBalance = withdrawEvent.Amount
	balanceEvent.Reason = "withdraw_completed"
	
	// 复用余额更新的处理逻辑
	return m.HandleBalanceUpdated(ctx, balanceEvent)
}

// GetPendingOpportunities 获取待处理的机会列表（已按优先级排序）
func (m *Manager) GetPendingOpportunities() []*events.ValidatedOpportunityEvent {
	return m.pendingQueue.GetAll()
}
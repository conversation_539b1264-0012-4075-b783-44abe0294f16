package telegram

import (
	"fmt"
	"net/http"
	"net/url"

	"stargate/pkg/logger"
)

// Client represents a Telegram client
type Client struct {
	botToken string
	chatID   string
	baseURL  string
}

// NewClient creates a new Telegram client
func NewClient(botToken, chatID string) *Client {
	return &Client{
		botToken: botToken,
		chatID:   chatID,
		baseURL:  "https://api.telegram.org/bot",
	}
}

// SendMessage sends a message to the Telegram chat
func (c *Client) SendMessage(message string) error {
	endpoint := fmt.Sprintf("%s%s/sendMessage", c.baseURL, c.botToken)

	params := url.Values{}
	params.Add("chat_id", c.chatID)
	params.Add("text", message)
	params.Add("parse_mode", "HTML")

	resp, err := http.PostForm(endpoint, params)
	if err != nil {
		return fmt.Errorf("failed to send message: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("telegram API returned non-200 status code: %d", resp.StatusCode)
	}

	logger.Debug().Str("message", message).Msg("Telegram message sent successfully")
	return nil
}

package execution

import (
	"context"
	"fmt"
	"math/big"
	"sync"
	"time"

	"stargate/pkg/events"
	"stargate/pkg/logger"
	starTypes "stargate/pkg/types"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/pkg/errors"
)

// ExecutionEngine 是执行引擎，专注于执行交易
type ExecutionEngine struct {
	// 核心依赖
	eventBus         events.EventBus
	stargateProtocol *StargateProtocol
	
	// 执行追踪
	activeExecutions sync.Map // executionID -> *ActiveExecution
	
	// 生命周期
	ctx          context.Context
	cancel       context.CancelFunc
	subscription events.Subscription
	
	// 配置
	confirmationBlocks int
	executionTimeout   time.Duration
}

// ActiveExecution 跟踪活跃的执行
type ActiveExecution struct {
	ExecutionID   string
	OpportunityID string
	StartTime     time.Time
	Request       *starTypes.TradeRequest
	TxHash        string
	Status        string // "executing", "confirming", "completed", "failed"
}

// NewExecutionEngine 创建执行引擎
func NewExecutionEngine(
	eventBus events.EventBus,
	stargateProtocol *StargateProtocol,
	confirmationBlocks int,
	executionTimeout time.Duration,
) *ExecutionEngine {
	ctx, cancel := context.WithCancel(context.Background())
	
	return &ExecutionEngine{
		eventBus:           eventBus,
		stargateProtocol:   stargateProtocol,
		ctx:                ctx,
		cancel:             cancel,
		confirmationBlocks: confirmationBlocks,
		executionTimeout:   executionTimeout,
	}
}

// Start 启动执行引擎
func (e *ExecutionEngine) Start(ctx context.Context) error {
	// 订阅执行事件
	sub, err := e.eventBus.Subscribe(events.EventTypeStargateExecute, e.handleExecuteEvent)
	if err != nil {
		return errors.Wrap(err, "failed to subscribe to execute events")
	}
	e.subscription = sub
	
	logger.Info().Msg("Execution engine started")
	return nil
}

// Stop 停止执行引擎
func (e *ExecutionEngine) Stop() error {
	e.cancel()
	
	if e.subscription != nil {
		if err := e.subscription.Unsubscribe(); err != nil {
			logger.Error().Err(err).Msg("Failed to unsubscribe from events")
		}
	}
	
	// 等待所有活跃执行完成或超时
	timeout := time.After(30 * time.Second)
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()
	
	for {
		select {
		case <-timeout:
			logger.Warn().Msg("Timeout waiting for active executions to complete")
			return nil
		case <-ticker.C:
			count := 0
			e.activeExecutions.Range(func(_, _ interface{}) bool {
				count++
				return true
			})
			if count == 0 {
				logger.Info().Msg("All active executions completed")
				return nil
			}
			logger.Info().Int("active_count", count).Msg("Waiting for active executions")
		}
	}
}

// handleExecuteEvent 处理执行事件
func (e *ExecutionEngine) handleExecuteEvent(ctx context.Context, event events.Event) error {
	execEvent, ok := event.(*events.StargateExecuteEvent)
	if !ok {
		return fmt.Errorf("invalid event type: expected StargateExecuteEvent")
	}
	
	logger.Info().
		Str("execution_id", execEvent.ExecutionID).
		Str("opportunity_id", execEvent.OpportunityID).
		Str("source_chain", execEvent.SourceChain).
		Str("dest_chain", execEvent.DestChain).
		Str("amount", execEvent.Amount.String()).
		Msg("Received execute event")
	
	// 创建执行记录
	execution := &ActiveExecution{
		ExecutionID:   execEvent.ExecutionID,
		OpportunityID: execEvent.OpportunityID,
		StartTime:     time.Now(),
		Status:        "executing",
	}
	
	// 存储执行记录
	e.activeExecutions.Store(execEvent.ExecutionID, execution)
	
	// 异步执行跨链发送
	go e.executeCrossSend(ctx, execEvent, execution)
	
	return nil
}

// executeCrossSend 执行实际的跨链发送
func (e *ExecutionEngine) executeCrossSend(ctx context.Context, execEvent *events.StargateExecuteEvent, execution *ActiveExecution) {
	// 确保清理执行记录
	defer e.activeExecutions.Delete(execEvent.ExecutionID)
	
	// 1. 准备跨链发送参数
	crossSendParams := &CrossSendParams{
		SrcChain:     execEvent.SourceChain,
		DstChain:     execEvent.DestChain,
		TokenSymbol:  execEvent.TokenSymbol,
		AmountIn:     execEvent.Amount,
		MinAmountOut: execEvent.MinAmountOut,
		ToAddress:    common.HexToAddress(execEvent.WalletAddress),
		Deadline:     e.executionTimeout,
	}
	
	logger.Info().
		Str("execution_id", execEvent.ExecutionID).
		Str("source_chain", execEvent.SourceChain).
		Str("dest_chain", execEvent.DestChain).
		Str("token", execEvent.TokenSymbol).
		Str("amount", execEvent.Amount.String()).
		Msg("Executing Stargate cross-chain send")
	
	// 2. 执行跨链发送
	tx, err := e.stargateProtocol.CrossSend(ctx, crossSendParams)
	if err != nil {
		logger.Error().
			Err(err).
			Str("execution_id", execEvent.ExecutionID).
			Msg("Failed to execute cross-chain send")
		
		// 发布失败事件
		e.publishExecutionResult(ctx, execEvent, nil, nil, nil, err)
		return
	}
	
	execution.TxHash = tx.Hash().Hex()
	execution.Status = "confirming"
	
	logger.Info().
		Str("execution_id", execEvent.ExecutionID).
		Str("tx_hash", tx.Hash().Hex()).
		Msg("Cross-chain send transaction submitted")
	
	// 3. 等待确认
	receipt, err := e.waitForConfirmation(ctx, execEvent.SourceChain, tx)
	if err != nil {
		logger.Error().
			Err(err).
			Str("execution_id", execEvent.ExecutionID).
			Str("tx_hash", tx.Hash().Hex()).
			Msg("Failed to confirm transaction")
		
		// 发布失败事件
		txHash := tx.Hash().Hex()
		e.publishExecutionResult(ctx, execEvent, &txHash, nil, nil, err)
		return
	}
	
	// 检查交易是否成功
	if receipt.Status == 0 {
		err := fmt.Errorf("transaction reverted on chain")
		logger.Error().
			Str("execution_id", execEvent.ExecutionID).
			Str("tx_hash", tx.Hash().Hex()).
			Msg("Transaction reverted")
		
		// 发布失败事件
		txHash := tx.Hash().Hex()
		e.publishExecutionResult(ctx, execEvent, &txHash, nil, nil, err)
		return
	}
	
	execution.Status = "completed"
	gasUsed := new(big.Int).SetUint64(receipt.GasUsed)
	
	logger.Info().
		Str("execution_id", execEvent.ExecutionID).
		Str("tx_hash", tx.Hash().Hex()).
		Uint64("gas_used", receipt.GasUsed).
		Uint64("block_number", receipt.BlockNumber.Uint64()).
		Msg("Cross-chain send confirmed successfully")
	
	// 4. 计算实际利润（简化版，不监控目标链）
	// 在真实场景中，可能需要监控目标链的到账情况
	estimatedProfit := new(big.Int).Sub(execEvent.MinAmountOut, execEvent.Amount)
	
	// 5. 发布成功事件
	txHash := tx.Hash().Hex()
	e.publishExecutionResult(ctx, execEvent, &txHash, estimatedProfit, gasUsed, nil)
}

// waitForConfirmation 等待交易确认
func (e *ExecutionEngine) waitForConfirmation(ctx context.Context, chain string, tx *types.Transaction) (*types.Receipt, error) {
	// 获取客户端
	client, err := e.stargateProtocol.walletManager.GetClient(chain)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get client")
	}
	
	// 等待交易确认
	confirmCtx, cancel := context.WithTimeout(ctx, 10*time.Minute)
	defer cancel()
	
	// 轮询检查交易状态
	ticker := time.NewTicker(3 * time.Second)
	defer ticker.Stop()
	
	for {
		select {
		case <-confirmCtx.Done():
			return nil, errors.New("timeout waiting for confirmation")
		case <-ticker.C:
			receipt, err := client.TransactionReceipt(confirmCtx, tx.Hash())
			if err != nil {
				// 交易还未被确认，继续等待
				continue
			}
			
			// 检查区块确认数
			if receipt.BlockNumber != nil {
				currentBlock, err := client.BlockNumber(confirmCtx)
				if err == nil {
					confirmations := currentBlock - receipt.BlockNumber.Uint64()
					if confirmations >= uint64(e.confirmationBlocks) {
						return receipt, nil
					}
				}
			}
		}
	}
}

// publishExecutionResult 发布执行结果事件
func (e *ExecutionEngine) publishExecutionResult(
	ctx context.Context,
	execEvent *events.StargateExecuteEvent,
	txHash *string,
	actualProfit *big.Int,
	gasUsed *big.Int,
	err error,
) {
	completedEvent := events.NewExecutionCompletedEvent(execEvent.ExecutionID, execEvent.OpportunityID)
	completedEvent.Success = err == nil
	if txHash != nil {
		completedEvent.TxHash = *txHash
	}
	completedEvent.ActualProfit = actualProfit
	completedEvent.GasUsed = gasUsed
	if err != nil {
		completedEvent.Error = err.Error()
	}
	
	if pubErr := e.eventBus.Publish(ctx, completedEvent); pubErr != nil {
		logger.Error().
			Err(pubErr).
			Str("execution_id", execEvent.ExecutionID).
			Msg("Failed to publish execution completed event")
	}
}

// GetActiveExecutions 获取当前活跃的执行
func (e *ExecutionEngine) GetActiveExecutions() []*ActiveExecution {
	var executions []*ActiveExecution
	
	e.activeExecutions.Range(func(key, value interface{}) bool {
		if exec, ok := value.(*ActiveExecution); ok {
			executions = append(executions, exec)
		}
		return true
	})
	
	return executions
}
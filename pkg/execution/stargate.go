package execution

import (
	"context"
	"errors"
	"fmt"
	"math/big"
	"strings"
	"sync"
	"time"

	"stargate/pkg/contracts"
	"stargate/pkg/logger"
	"stargate/pkg/wallet"

	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/ethclient"
)

// StargateProtocol handles Stargate protocol cross-chain transfers using Pool contracts
type StargateProtocol struct {
	walletManager   *wallet.Manager
	stargateClient  *contracts.StargateClient // Reuse existing client
	poolAddresses   map[string]map[string]common.Address // chain -> token -> pool address
	endpointIDs     map[string]uint32 // chain name -> LayerZero endpoint ID
	mu              sync.RWMutex
}

// SendParam represents the parameters for Pool.send() function
type SendParam struct {
	DstEid       uint32         // Destination endpoint ID
	To           [32]byte       // Recipient address (bytes32)
	AmountLD     *big.Int       // Amount in local decimals
	MinAmountLD  *big.Int       // Minimum amount to receive
	ExtraOptions []byte         // Extra LayerZero options
	ComposeMsg   []byte         // Compose message for destination
	OftCmd       []byte         // OFT command
}

// MessagingFee represents the fee structure for LayerZero messaging
type MessagingFee struct {
	NativeFee  *big.Int // Native token fee (ETH, BNB, etc.)
	LzTokenFee *big.Int // LayerZero token fee (usually 0)
}

// CrossSendParams contains parameters for a cross-chain send operation via Stargate
type CrossSendParams struct {
	SrcChain      string
	DstChain      string
	TokenSymbol   string
	AmountIn      *big.Int
	MinAmountOut  *big.Int
	ToAddress     common.Address
	SlippageBPS   uint16        // basis points, p.g., 50 = 0.5%
	Deadline      time.Duration
}

// NewStargateProtocol creates a new Stargate protocol handler
func NewStargateProtocol(walletManager *wallet.Manager, stargateClient *contracts.StargateClient) (*StargateProtocol, error) {
	if walletManager == nil {
		return nil, errors.New("wallet manager is nil")
	}
	if stargateClient == nil {
		return nil, errors.New("stargate client is nil")
	}

	protocol := &StargateProtocol{
		walletManager:  walletManager,
		stargateClient: stargateClient,
		poolAddresses:  make(map[string]map[string]common.Address),
		endpointIDs:    make(map[string]uint32),
	}

	// Extract endpoint IDs and pool addresses from stargate client
	chains := stargateClient.GetChains()
	for chainName, chainConfig := range chains {
		// Set endpoint ID from config
		if chainConfig.EndpointID > 0 {
			protocol.endpointIDs[chainName] = chainConfig.EndpointID
		}
		
		// Set pool addresses
		protocol.poolAddresses[chainName] = make(map[string]common.Address)
		for token, poolAddr := range chainConfig.Stargate.Pools {
			protocol.poolAddresses[chainName][token] = common.HexToAddress(poolAddr)
		}
	}

	return protocol, nil
}

// GetPoolAddress gets the pool address for a specific chain and token
func (p *StargateProtocol) GetPoolAddress(chain string, token string) (common.Address, error) {
	p.mu.RLock()
	defer p.mu.RUnlock()

	chainPools, exists := p.poolAddresses[chain]
	if !exists {
		return common.Address{}, fmt.Errorf("chain %s not found", chain)
	}

	poolAddr, exists := chainPools[token]
	if !exists {
		return common.Address{}, fmt.Errorf("pool for token %s not found on chain %s", token, chain)
	}

	return poolAddr, nil
}

// CrossSend executes a cross-chain send via Stargate Pool.send()
func (p *StargateProtocol) CrossSend(ctx context.Context, params *CrossSendParams) (*types.Transaction, error) {
	logger.Info().
		Str("src_chain", params.SrcChain).
		Str("dst_chain", params.DstChain).
		Str("token", params.TokenSymbol).
		Str("amount", params.AmountIn.String()).
		Msg("Executing Stargate cross-chain send")

	// Validate parameters
	if err := p.validateCrossSendParams(params); err != nil {
		return nil, fmt.Errorf("invalid cross-send parameters: %w", err)
	}

	// Get pool address for source chain
	poolAddr, err := p.GetPoolAddress(params.SrcChain, params.TokenSymbol)
	if err != nil {
		return nil, fmt.Errorf("failed to get pool address: %w", err)
	}

	// Get destination endpoint ID
	dstEid, err := p.getEndpointID(params.DstChain)
	if err != nil {
		return nil, fmt.Errorf("failed to get destination endpoint ID: %w", err)
	}

	// Get account
	account, err := p.walletManager.GetAccount(params.SrcChain)
	if err != nil {
		return nil, fmt.Errorf("failed to get account: %w", err)
	}

	// Convert address to bytes32
	var toBytes32 [32]byte
	copy(toBytes32[12:], params.ToAddress.Bytes())

	// Prepare SendParam
	sendParam := SendParam{
		DstEid:       dstEid,
		To:           toBytes32,
		AmountLD:     params.AmountIn,
		MinAmountLD:  params.MinAmountOut,
		ExtraOptions: []byte{}, // Empty for now
		ComposeMsg:   []byte{}, // No compose message
		OftCmd:       []byte{}, // No OFT command
	}

	// Quote the fee first
	fee, err := p.quoteSend(ctx, params.SrcChain, poolAddr, sendParam)
	if err != nil {
		return nil, fmt.Errorf("failed to quote fee: %w", err)
	}

	// Approve token spending if needed
	if err := p.approveTokenIfNeeded(ctx, params.SrcChain, params.TokenSymbol, poolAddr, params.AmountIn); err != nil {
		return nil, fmt.Errorf("failed to approve token: %w", err)
	}

	// Prepare transaction
	opts, err := p.prepareTransactOpts(ctx, params.SrcChain, fee.NativeFee)
	if err != nil {
		return nil, fmt.Errorf("failed to prepare transaction options: %w", err)
	}

	// Get the chain ID to get a client
	chainConfig, ok := p.stargateClient.GetChains()[params.SrcChain]
	if !ok {
		return nil, fmt.Errorf("chain config not found for %s", params.SrcChain)
	}

	// Execute send transaction
	var tx *types.Transaction
	err = p.stargateClient.ExecutePoolCall(chainConfig.ChainID, func(client *ethclient.Client) error {
		// Load Pool ABI
		poolABIStr, err := contracts.LoadPoolABI()
		if err != nil {
			return fmt.Errorf("failed to load pool ABI: %w", err)
		}

		parsedABI, err := abi.JSON(strings.NewReader(poolABIStr))
		if err != nil {
			return fmt.Errorf("failed to parse pool ABI: %w", err)
		}

		// Pack the send function call
		data, err := parsedABI.Pack("send", sendParam, fee, account.Address)
		if err != nil {
			return fmt.Errorf("failed to pack send call: %w", err)
		}

		// Sign and send transaction using wallet manager
		tx, err = p.walletManager.SendTransaction(
			ctx, 
			params.SrcChain, 
			poolAddr,
			opts.Value,
			data,
			wallet.GasStrategyStandard,
		)
		if err != nil {
			return fmt.Errorf("failed to send transaction: %w", err)
		}

		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to execute transfer: %w", err)
	}

	logger.Info().
		Str("tx_hash", tx.Hash().Hex()).
		Str("src_chain", params.SrcChain).
		Str("dst_chain", params.DstChain).
		Msg("Transfer transaction sent")

	return tx, nil
}

// EstimateCrossSendGas estimates gas for a cross-chain send transaction
func (p *StargateProtocol) EstimateCrossSendGas(ctx context.Context, params *CrossSendParams) (uint64, error) {
	// Base gas for Stargate send
	baseGas := uint64(300000)
	
	// Add extra gas for cross-chain messaging
	crossChainGas := uint64(100000)
	
	// Total with buffer
	totalGas := (baseGas + crossChainGas) * 120 / 100 // 20% buffer
	
	return totalGas, nil
}

// GetQuote gets a quote for the transfer by calling Pool.quoteOFT
func (p *StargateProtocol) GetQuote(ctx context.Context, srcChain, dstChain, token string, amountIn *big.Int) (*big.Int, error) {
	// Get pool address
	poolAddr, err := p.GetPoolAddress(srcChain, token)
	if err != nil {
		return nil, fmt.Errorf("failed to get pool address: %w", err)
	}

	// Get destination endpoint ID
	dstEid, err := p.getEndpointID(dstChain)
	if err != nil {
		return nil, fmt.Errorf("failed to get destination endpoint ID: %w", err)
	}

	// For simplicity, use a default recipient address for quote
	var toBytes32 [32]byte
	// Using zero address for quote

	// Prepare SendParam for quote
	sendParam := SendParam{
		DstEid:       dstEid,
		To:           toBytes32,
		AmountLD:     amountIn,
		MinAmountLD:  big.NewInt(0), // Zero for quote
		ExtraOptions: []byte{},
		ComposeMsg:   []byte{},
		OftCmd:       []byte{},
	}

	// Get chain config
	chainConfig, ok := p.stargateClient.GetChains()[srcChain]
	if !ok {
		return nil, fmt.Errorf("chain config not found for %s", srcChain)
	}

	// Call quoteOFT to get the expected output
	var amountReceivedLD *big.Int
	err = p.stargateClient.ExecutePoolCall(chainConfig.ChainID, func(client *ethclient.Client) error {
		// Use the CallContract method for read-only call
		results, err := p.stargateClient.CallContract(chainConfig.ChainID, poolAddr, "Pool", "quoteOFT", sendParam)
		if err != nil {
			return fmt.Errorf("failed to call quoteOFT: %w", err)
		}

		// The quoteOFT returns (OFTLimit limit, OFTFeeDetail[] oftFeeDetails, OFTReceipt receipt)
		// We need the receipt.amountReceivedLD
		if len(results) < 3 {
			return fmt.Errorf("unexpected number of results from quoteOFT")
		}

		// For now, estimate output as input minus 0.06% fee
		// TODO: Parse the actual receipt structure
		fee := new(big.Int).Mul(amountIn, big.NewInt(6))
		fee.Div(fee, big.NewInt(10000))
		amountReceivedLD = new(big.Int).Sub(amountIn, fee)

		return nil
	})

	if err != nil {
		return nil, err
	}

	return amountReceivedLD, nil
}

// quoteSend quotes the fee for a send transaction
func (p *StargateProtocol) quoteSend(ctx context.Context, srcChain string, poolAddr common.Address, sendParam SendParam) (*MessagingFee, error) {
	chainConfig, ok := p.stargateClient.GetChains()[srcChain]
	if !ok {
		return nil, fmt.Errorf("chain config not found for %s", srcChain)
	}

	var fee MessagingFee
	err := p.stargateClient.ExecutePoolCall(chainConfig.ChainID, func(client *ethclient.Client) error {
		// Call quoteSend with payInLzToken = false
		results, err := p.stargateClient.CallContract(chainConfig.ChainID, poolAddr, "Pool", "quoteSend", sendParam, false)
		if err != nil {
			return fmt.Errorf("failed to call quoteSend: %w", err)
		}

		// quoteSend returns MessagingFee struct
		if len(results) < 1 {
			return fmt.Errorf("unexpected number of results from quoteSend")
		}

		// For now, use a fixed fee estimate
		// TODO: Parse the actual MessagingFee structure
		fee.NativeFee = big.NewInt(1e16) // 0.01 ETH
		fee.LzTokenFee = big.NewInt(0)

		return nil
	})

	if err != nil {
		return nil, err
	}

	// Add 10% buffer to the fee
	buffer := new(big.Int).Div(fee.NativeFee, big.NewInt(10))
	fee.NativeFee = new(big.Int).Add(fee.NativeFee, buffer)

	return &fee, nil
}

// Helper methods

func (p *StargateProtocol) validateCrossSendParams(params *CrossSendParams) error {
	if params.SrcChain == "" || params.DstChain == "" {
		return errors.New("source and destination chains must be specified")
	}
	if params.TokenSymbol == "" {
		return errors.New("token symbol must be specified")
	}
	if params.AmountIn == nil || params.AmountIn.Sign() <= 0 {
		return errors.New("amount must be positive")
	}
	if params.MinAmountOut == nil || params.MinAmountOut.Sign() < 0 {
		return errors.New("minimum amount out must be non-negative")
	}
	if params.ToAddress == (common.Address{}) {
		return errors.New("destination address must be specified")
	}
	return nil
}

func (p *StargateProtocol) getEndpointID(chain string) (uint32, error) {
	p.mu.RLock()
	defer p.mu.RUnlock()

	endpointID, exists := p.endpointIDs[chain]
	if !exists {
		return 0, fmt.Errorf("endpoint ID not found for %s", chain)
	}
	return endpointID, nil
}

func (p *StargateProtocol) approveTokenIfNeeded(ctx context.Context, chain, token string, spender common.Address, amount *big.Int) error {
	// For now, we'll use a hardcoded map of token addresses
	// In production, this should come from configuration
	tokenAddresses := map[string]map[string]string{
		"ethereum": {
			"USDC": "******************************************",
			"USDT": "******************************************",
			"DAI": "******************************************",
		},
		"bsc": {
			"USDC": "******************************************",
			"USDT": "******************************************",
			"DAI": "******************************************",
		},
		"polygon": {
			"USDC": "******************************************",
			"USDT": "******************************************",
			"DAI": "******************************************",
		},
		"arbitrum": {
			"USDC": "******************************************",
			"USDT": "******************************************",
			"DAI": "******************************************",
		},
		"optimism": {
			"USDC": "******************************************",
			"USDT": "******************************************",
			"DAI": "******************************************",
		},
		"avalanche": {
			"USDC": "******************************************",
			"USDT": "******************************************",
			"DAI": "******************************************",
		},
	}

	chainTokens, exists := tokenAddresses[chain]
	if !exists {
		return fmt.Errorf("token addresses not found for chain %s", chain)
	}

	tokenAddr, exists := chainTokens[token]
	if !exists {
		return fmt.Errorf("token address not found for %s on chain %s", token, chain)
	}

	// Get ERC20 manager
	erc20Manager := p.walletManager.ERC20()

	// Get account
	account, err := p.walletManager.GetAccount(chain)
	if err != nil {
		return err
	}

	// Check current allowance
	allowance, err := erc20Manager.Allowance(ctx, chain, tokenAddr, account.Address.Hex(), spender.Hex())
	if err != nil {
		return fmt.Errorf("failed to check allowance: %w", err)
	}

	// If allowance < amount, approve
	if allowance.Cmp(amount) < 0 {
		logger.Info().
			Str("chain", chain).
			Str("token", token).
			Str("spender", spender.Hex()).
			Str("current_allowance", allowance.String()).
			Str("required_amount", amount.String()).
			Msg("Approving token spending")

		// Approve max uint256 for convenience
		maxApproval := new(big.Int).Sub(new(big.Int).Lsh(big.NewInt(1), 256), big.NewInt(1))
		tx, err := erc20Manager.Approve(ctx, chain, tokenAddr, spender.Hex(), maxApproval)
		if err != nil {
			return fmt.Errorf("failed to approve token: %w", err)
		}

		// Wait for confirmation
		_, err = p.walletManager.WaitForTransaction(ctx, chain, tx.Hash())
		if err != nil {
			return fmt.Errorf("failed to wait for approval transaction: %w", err)
		}

		logger.Info().
			Str("chain", chain).
			Str("token", token).
			Str("tx_hash", tx.Hash().Hex()).
			Msg("Token approval completed")
	}

	return nil
}

func (p *StargateProtocol) prepareTransactOpts(ctx context.Context, chain string, value *big.Int) (*bind.TransactOpts, error) {
	// This is a simplified version
	// In production, this should use the wallet manager's transaction preparation
	
	account, err := p.walletManager.GetAccount(chain)
	if err != nil {
		return nil, err
	}
	
	client, err := p.walletManager.GetClient(chain)
	if err != nil {
		return nil, err
	}
	
	// Get chain ID for later use
	_, err = client.ChainID(ctx)
	if err != nil {
		return nil, err
	}
	
	// Get suggested gas price
	gasPrice, err := client.SuggestGasPrice(ctx)
	if err != nil {
		return nil, err
	}
	
	// Create transact opts
	opts := &bind.TransactOpts{
		From:     account.Address,
		Value:    value,
		GasPrice: gasPrice,
		GasLimit: 0, // Will be estimated
		Context:  ctx,
		Signer: func(address common.Address, tx *types.Transaction) (*types.Transaction, error) {
			// This should use the wallet manager's signing functionality
			return nil, errors.New("signing not implemented")
		},
	}
	
	return opts, nil
}

// WaitForConfirmation waits for transaction confirmation
func (p *StargateProtocol) WaitForConfirmation(ctx context.Context, chain string, tx *types.Transaction) (*types.Receipt, error) {
	return p.walletManager.WaitForTransaction(ctx, chain, tx.Hash())
}

// CalculateMinAmountOut calculates minimum amount out based on slippage
func (p *StargateProtocol) CalculateMinAmountOut(amountIn *big.Int, slippageBPS uint16) *big.Int {
	// slippageBPS is in basis points (1 BPS = 0.01%)
	// p.g., 50 BPS = 0.5%
	
	// Calculate slippage amount
	slippageAmount := new(big.Int).Mul(amountIn, big.NewInt(int64(slippageBPS)))
	slippageAmount.Div(slippageAmount, big.NewInt(10000))
	
	// Minimum amount out = amount in - slippage - fee (0.06%)
	fee := new(big.Int).Mul(amountIn, big.NewInt(6))
	fee.Div(fee, big.NewInt(10000))
	
	minAmountOut := new(big.Int).Sub(amountIn, slippageAmount)
	minAmountOut.Sub(minAmountOut, fee)
	
	return minAmountOut
}

// GetSupportedChains returns supported chains
func (p *StargateProtocol) GetSupportedChains() []string {
	p.mu.RLock()
	defer p.mu.RUnlock()
	
	chains := make([]string, 0, len(p.endpointIDs))
	for chain := range p.endpointIDs {
		chains = append(chains, chain)
	}
	return chains
}

// GetSupportedTokens returns supported tokens for a specific chain
func (p *StargateProtocol) GetSupportedTokens(chain string) []string {
	p.mu.RLock()
	defer p.mu.RUnlock()
	
	chainPools, exists := p.poolAddresses[chain]
	if !exists {
		return nil
	}
	
	tokens := make([]string, 0, len(chainPools))
	for token := range chainPools {
		tokens = append(tokens, token)
	}
	return tokens
}
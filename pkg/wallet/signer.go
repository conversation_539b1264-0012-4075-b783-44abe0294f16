package wallet

import (
	"crypto/ecdsa"
	"errors"
	"fmt"
	"math/big"
	"sync"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/crypto"
	"stargate/pkg/logger"
)

// Signer 签名器接口
type Signer interface {
	// SignTransaction 签名交易
	SignTransaction(tx *types.Transaction, chainID *big.Int) (*types.Transaction, error)
	// SignMessage 签名消息
	SignMessage(message []byte) ([]byte, error)
	// GetAddress 获取签名地址
	GetAddress() common.Address
}

// PrivateKeySigner 使用私钥直接签名的签名器
type PrivateKeySigner struct {
	privateKey *ecdsa.PrivateKey
	address    common.Address
	mu         sync.RWMutex
}

// NewPrivateKeySigner 创建私钥签名器
func NewPrivateKeySigner(privateKey *ecdsa.PrivateKey) (*PrivateKeySigner, error) {
	if privateKey == nil {
		return nil, errors.New("private key is nil")
	}

	publicKey := privateKey.Public()
	publicKeyECDSA, ok := publicKey.(*ecdsa.PublicKey)
	if !ok {
		return nil, errors.New("cannot assert type: publicKey is not of type *ecdsa.PublicKey")
	}

	address := crypto.PubkeyToAddress(*publicKeyECDSA)

	return &PrivateKeySigner{
		privateKey: privateKey,
		address:    address,
	}, nil
}

// NewPrivateKeySignerFromKeystore 从 keystore 创建私钥签名器
func NewPrivateKeySignerFromKeystore(km *KeystoreManager, address common.Address, password string) (*PrivateKeySigner, error) {
	// 导出私钥
	privateKey, err := km.ExportPrivateKey(address, password)
	if err != nil {
		return nil, fmt.Errorf("failed to export private key: %w", err)
	}

	return NewPrivateKeySigner(privateKey)
}

// SignTransaction 使用私钥签名交易
func (s *PrivateKeySigner) SignTransaction(tx *types.Transaction, chainID *big.Int) (*types.Transaction, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if s.privateKey == nil {
		return nil, errors.New("private key not loaded")
	}

	// 创建签名器
	signer := types.LatestSignerForChainID(chainID)

	// 签名交易
	signedTx, err := types.SignTx(tx, signer, s.privateKey)
	if err != nil {
		return nil, fmt.Errorf("failed to sign transaction: %w", err)
	}

	logger.Debug().
		Str("from", s.address.Hex()).
		Str("tx_hash", signedTx.Hash().Hex()).
		Str("chain_id", chainID.String()).
		Msg("Transaction signed")

	return signedTx, nil
}

// SignMessage 使用私钥签名消息
func (s *PrivateKeySigner) SignMessage(message []byte) ([]byte, error) {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if s.privateKey == nil {
		return nil, errors.New("private key not loaded")
	}

	// 计算消息哈希
	hash := crypto.Keccak256Hash(message)

	// 签名
	signature, err := crypto.Sign(hash.Bytes(), s.privateKey)
	if err != nil {
		return nil, fmt.Errorf("failed to sign message: %w", err)
	}

	logger.Debug().
		Str("address", s.address.Hex()).
		Hex("message_hash", hash.Bytes()).
		Msg("Message signed")

	return signature, nil
}

// GetAddress 获取签名地址
func (s *PrivateKeySigner) GetAddress() common.Address {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.address
}

// Clear 清除私钥（安全考虑）
func (s *PrivateKeySigner) Clear() {
	s.mu.Lock()
	defer s.mu.Unlock()
	
	s.privateKey = nil
	logger.Info().
		Str("address", s.address.Hex()).
		Msg("Private key cleared from memory")
}

// SignerManager 签名管理器，管理多个签名器
type SignerManager struct {
	signers map[common.Address]Signer
	primary common.Address
	mu      sync.RWMutex
}

// NewSignerManager 创建签名管理器
func NewSignerManager() *SignerManager {
	return &SignerManager{
		signers: make(map[common.Address]Signer),
	}
}

// AddSigner 添加签名器
func (sm *SignerManager) AddSigner(signer Signer) error {
	if signer == nil {
		return errors.New("signer is nil")
	}

	address := signer.GetAddress()
	
	sm.mu.Lock()
	defer sm.mu.Unlock()
	
	sm.signers[address] = signer
	
	// 如果是第一个签名器，设为主签名器
	if len(sm.signers) == 1 {
		sm.primary = address
	}
	
	logger.Info().
		Str("address", address.Hex()).
		Bool("is_primary", address == sm.primary).
		Msg("Signer added")
	
	return nil
}

// GetSigner 获取指定地址的签名器
func (sm *SignerManager) GetSigner(address common.Address) (Signer, error) {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	
	signer, exists := sm.signers[address]
	if !exists {
		return nil, fmt.Errorf("signer not found for address %s", address.Hex())
	}
	
	return signer, nil
}

// GetPrimarySigner 获取主签名器
func (sm *SignerManager) GetPrimarySigner() (Signer, error) {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	
	if sm.primary == (common.Address{}) {
		return nil, errors.New("no primary signer set")
	}
	
	signer, exists := sm.signers[sm.primary]
	if !exists {
		return nil, errors.New("primary signer not found")
	}
	
	return signer, nil
}

// SetPrimary 设置主签名器
func (sm *SignerManager) SetPrimary(address common.Address) error {
	sm.mu.Lock()
	defer sm.mu.Unlock()
	
	if _, exists := sm.signers[address]; !exists {
		return fmt.Errorf("signer not found for address %s", address.Hex())
	}
	
	sm.primary = address
	
	logger.Info().
		Str("address", address.Hex()).
		Msg("Primary signer updated")
	
	return nil
}

// RemoveSigner 移除签名器
func (sm *SignerManager) RemoveSigner(address common.Address) error {
	sm.mu.Lock()
	defer sm.mu.Unlock()
	
	if _, exists := sm.signers[address]; !exists {
		return fmt.Errorf("signer not found for address %s", address.Hex())
	}
	
	// 如果是私钥签名器，清除私钥
	if privateKeySigner, ok := sm.signers[address].(*PrivateKeySigner); ok {
		privateKeySigner.Clear()
	}
	
	delete(sm.signers, address)
	
	// 如果移除的是主签名器，需要重新选择
	if address == sm.primary {
		sm.primary = common.Address{}
		for addr := range sm.signers {
			sm.primary = addr
			break
		}
	}
	
	logger.Info().
		Str("address", address.Hex()).
		Msg("Signer removed")
	
	return nil
}

// GetAddresses 获取所有签名器地址
func (sm *SignerManager) GetAddresses() []common.Address {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	
	addresses := make([]common.Address, 0, len(sm.signers))
	for addr := range sm.signers {
		addresses = append(addresses, addr)
	}
	
	return addresses
}

// Clear 清除所有签名器
func (sm *SignerManager) Clear() {
	sm.mu.Lock()
	defer sm.mu.Unlock()
	
	// 清除所有私钥签名器的私钥
	for _, signer := range sm.signers {
		if privateKeySigner, ok := signer.(*PrivateKeySigner); ok {
			privateKeySigner.Clear()
		}
	}
	
	sm.signers = make(map[common.Address]Signer)
	sm.primary = common.Address{}
	
	logger.Info().Msg("All signers cleared")
}
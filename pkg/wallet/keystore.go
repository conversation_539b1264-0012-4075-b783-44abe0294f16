package wallet

import (
	"crypto/ecdsa"
	"errors"
	"fmt"
	"io"
	"math/big"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/ethereum/go-ethereum/accounts"
	"github.com/ethereum/go-ethereum/accounts/keystore"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"stargate/pkg/logger"
)

// KeystoreManager keystore管理器
type KeystoreManager struct {
	keystore      *keystore.KeyStore
	keystorePath  string
	
	// 账户缓存
	accounts      map[common.Address]*accounts.Account
	unlocked      map[common.Address]time.Time
	mu            sync.RWMutex
	
	// 解锁时间配置
	unlockDuration time.Duration
}

// NewKeystoreManager 创建新的keystore管理器
func NewKeystoreManager(keystorePath string, unlockDuration time.Duration) (*KeystoreManager, error) {
	if keystorePath == "" {
		return nil, errors.New("keystore path is empty")
	}

	// 创建目录（如果不存在）
	if err := os.MkdirAll(keystorePath, 0700); err != nil {
		return nil, fmt.Errorf("failed to create keystore directory: %w", err)
	}

	// 创建keystore实例
	ks := keystore.NewKeyStore(keystorePath, keystore.StandardScryptN, keystore.StandardScryptP)

	if unlockDuration == 0 {
		unlockDuration = 5 * time.Minute // 默认5分钟
	}

	km := &KeystoreManager{
		keystore:       ks,
		keystorePath:   keystorePath,
		accounts:       make(map[common.Address]*accounts.Account),
		unlocked:       make(map[common.Address]time.Time),
		unlockDuration: unlockDuration,
	}

	// 加载现有账户
	if err := km.loadAccounts(); err != nil {
		return nil, fmt.Errorf("failed to load accounts: %w", err)
	}

	// 启动解锁超时检查
	go km.unlockTimeoutChecker()

	return km, nil
}

// CreateAccount 创建新账户
func (km *KeystoreManager) CreateAccount(password string) (common.Address, error) {
	if err := ValidateKeyStrength(password); err != nil {
		return common.Address{}, fmt.Errorf("weak password: %w", err)
	}

	account, err := km.keystore.NewAccount(password)
	if err != nil {
		return common.Address{}, fmt.Errorf("failed to create account: %w", err)
	}

	km.mu.Lock()
	km.accounts[account.Address] = &account
	km.mu.Unlock()

	logger.Info().
		Str("address", account.Address.Hex()).
		Msg("New account created")

	return account.Address, nil
}

// ImportPrivateKey 导入私钥
func (km *KeystoreManager) ImportPrivateKey(privateKey *ecdsa.PrivateKey, password string) (common.Address, error) {
	if privateKey == nil {
		return common.Address{}, errors.New("private key is nil")
	}
	
	if err := ValidateKeyStrength(password); err != nil {
		return common.Address{}, fmt.Errorf("weak password: %w", err)
	}

	account, err := km.keystore.ImportECDSA(privateKey, password)
	if err != nil {
		return common.Address{}, fmt.Errorf("failed to import private key: %w", err)
	}

	km.mu.Lock()
	km.accounts[account.Address] = &account
	km.mu.Unlock()

	logger.Info().
		Str("address", account.Address.Hex()).
		Msg("Private key imported")

	return account.Address, nil
}

// ExportPrivateKey 导出私钥
func (km *KeystoreManager) ExportPrivateKey(address common.Address, password string) (*ecdsa.PrivateKey, error) {
	km.mu.RLock()
	_, exists := km.accounts[address]
	km.mu.RUnlock()

	if !exists {
		return nil, fmt.Errorf("account %s not found", address.Hex())
	}

	// 读取keystore文件
	keyJSON, err := km.readKeyFile(address)
	if err != nil {
		return nil, err
	}

	// 解密私钥
	key, err := keystore.DecryptKey(keyJSON, password)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt key: %w", err)
	}

	return key.PrivateKey, nil
}

// UnlockAccount 解锁账户
func (km *KeystoreManager) UnlockAccount(address common.Address, password string) error {
	km.mu.RLock()
	account, exists := km.accounts[address]
	km.mu.RUnlock()

	if !exists {
		return fmt.Errorf("account %s not found", address.Hex())
	}

	// 尝试解锁
	if err := km.keystore.Unlock(*account, password); err != nil {
		return fmt.Errorf("failed to unlock account: %w", err)
	}

	// 记录解锁时间
	km.mu.Lock()
	km.unlocked[address] = time.Now().Add(km.unlockDuration)
	km.mu.Unlock()

	logger.Info().
		Str("address", address.Hex()).
		Dur("duration", km.unlockDuration).
		Msg("Account unlocked")

	return nil
}

// LockAccount 锁定账户
func (km *KeystoreManager) LockAccount(address common.Address) error {
	km.mu.RLock()
	account, exists := km.accounts[address]
	km.mu.RUnlock()

	if !exists {
		return fmt.Errorf("account %s not found", address.Hex())
	}

	// 锁定账户
	if err := km.keystore.Lock(account.Address); err != nil {
		return fmt.Errorf("failed to lock account: %w", err)
	}

	// 移除解锁记录
	km.mu.Lock()
	delete(km.unlocked, address)
	km.mu.Unlock()

	logger.Info().
		Str("address", address.Hex()).
		Msg("Account locked")

	return nil
}

// IsUnlocked 检查账户是否已解锁
func (km *KeystoreManager) IsUnlocked(address common.Address) bool {
	km.mu.RLock()
	unlockTime, exists := km.unlocked[address]
	km.mu.RUnlock()

	if !exists {
		return false
	}

	// 检查是否超时
	if time.Now().After(unlockTime) {
		// 自动锁定
		km.LockAccount(address)
		return false
	}

	return true
}

// GetAccounts 获取所有账户
func (km *KeystoreManager) GetAccounts() []common.Address {
	km.mu.RLock()
	defer km.mu.RUnlock()

	addresses := make([]common.Address, 0, len(km.accounts))
	for addr := range km.accounts {
		addresses = append(addresses, addr)
	}
	return addresses
}

// DeleteAccount 删除账户
func (km *KeystoreManager) DeleteAccount(address common.Address, password string) error {
	km.mu.RLock()
	account, exists := km.accounts[address]
	km.mu.RUnlock()

	if !exists {
		return fmt.Errorf("account %s not found", address.Hex())
	}

	// 删除账户
	if err := km.keystore.Delete(*account, password); err != nil {
		return fmt.Errorf("failed to delete account: %w", err)
	}

	// 从缓存中移除
	km.mu.Lock()
	delete(km.accounts, address)
	delete(km.unlocked, address)
	km.mu.Unlock()

	logger.Info().
		Str("address", address.Hex()).
		Msg("Account deleted")

	return nil
}

// UpdatePassword 更新账户密码
func (km *KeystoreManager) UpdatePassword(address common.Address, oldPassword, newPassword string) error {
	if err := ValidateKeyStrength(newPassword); err != nil {
		return fmt.Errorf("weak new password: %w", err)
	}

	km.mu.RLock()
	account, exists := km.accounts[address]
	km.mu.RUnlock()

	if !exists {
		return fmt.Errorf("account %s not found", address.Hex())
	}

	// 更新密码
	if err := km.keystore.Update(*account, oldPassword, newPassword); err != nil {
		return fmt.Errorf("failed to update password: %w", err)
	}

	logger.Info().
		Str("address", address.Hex()).
		Msg("Account password updated")

	return nil
}

// BackupKeystore 备份keystore
func (km *KeystoreManager) BackupKeystore(backupPath string) error {
	// 创建备份目录
	if err := os.MkdirAll(backupPath, 0700); err != nil {
		return fmt.Errorf("failed to create backup directory: %w", err)
	}

	// 获取所有keystore文件
	files, err := os.ReadDir(km.keystorePath)
	if err != nil {
		return fmt.Errorf("failed to read keystore directory: %w", err)
	}

	// 复制文件
	for _, file := range files {
		if file.IsDir() || !strings.HasPrefix(file.Name(), "UTC--") {
			continue
		}

		src := filepath.Join(km.keystorePath, file.Name())
		dst := filepath.Join(backupPath, file.Name())

		if err := copyFile(src, dst); err != nil {
			return fmt.Errorf("failed to copy %s: %w", file.Name(), err)
		}
	}

	logger.Info().
		Str("backup_path", backupPath).
		Int("files", len(files)).
		Msg("Keystore backed up")

	return nil
}

// RestoreKeystore 恢复keystore
func (km *KeystoreManager) RestoreKeystore(backupPath string) error {
	// 验证备份目录
	if _, err := os.Stat(backupPath); err != nil {
		return fmt.Errorf("backup path not found: %w", err)
	}

	// 获取备份文件
	files, err := os.ReadDir(backupPath)
	if err != nil {
		return fmt.Errorf("failed to read backup directory: %w", err)
	}

	// 复制文件到keystore目录
	for _, file := range files {
		if file.IsDir() || !strings.HasPrefix(file.Name(), "UTC--") {
			continue
		}

		src := filepath.Join(backupPath, file.Name())
		dst := filepath.Join(km.keystorePath, file.Name())

		// 如果文件已存在，跳过
		if _, err := os.Stat(dst); err == nil {
			logger.Warn().
				Str("file", file.Name()).
				Msg("Keystore file already exists, skipping")
			continue
		}

		if err := copyFile(src, dst); err != nil {
			return fmt.Errorf("failed to restore %s: %w", file.Name(), err)
		}
	}

	// 重新加载账户
	if err := km.loadAccounts(); err != nil {
		return fmt.Errorf("failed to reload accounts: %w", err)
	}

	logger.Info().
		Str("backup_path", backupPath).
		Int("files", len(files)).
		Msg("Keystore restored")

	return nil
}

// loadAccounts 加载所有账户
func (km *KeystoreManager) loadAccounts() error {
	accts := km.keystore.Accounts()
	
	km.mu.Lock()
	defer km.mu.Unlock()

	// 清空现有账户
	km.accounts = make(map[common.Address]*accounts.Account)

	// 加载新账户
	for i := range accts {
		km.accounts[accts[i].Address] = &accts[i]
	}

	logger.Info().
		Int("count", len(accts)).
		Msg("Accounts loaded")

	return nil
}

// readKeyFile 读取密钥文件
func (km *KeystoreManager) readKeyFile(address common.Address) ([]byte, error) {
	// 查找对应的keystore文件
	files, err := os.ReadDir(km.keystorePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read keystore directory: %w", err)
	}

	addrStr := strings.ToLower(address.Hex()[2:]) // 移除0x前缀并转小写
	
	for _, file := range files {
		if file.IsDir() {
			continue
		}
		
		// keystore文件名包含地址
		if strings.Contains(strings.ToLower(file.Name()), addrStr) {
			path := filepath.Join(km.keystorePath, file.Name())
			return os.ReadFile(path)
		}
	}

	return nil, fmt.Errorf("keystore file not found for address %s", address.Hex())
}

// unlockTimeoutChecker 解锁超时检查器
func (km *KeystoreManager) unlockTimeoutChecker() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		km.mu.Lock()
		now := time.Now()
		
		// 检查并锁定超时的账户
		for addr, unlockTime := range km.unlocked {
			if now.After(unlockTime) {
				// 锁定账户
				if account, exists := km.accounts[addr]; exists {
					km.keystore.Lock(account.Address)
				}
				delete(km.unlocked, addr)
				
				logger.Info().
					Str("address", addr.Hex()).
					Msg("Account auto-locked due to timeout")
			}
		}
		
		km.mu.Unlock()
	}
}

// copyFile 复制文件
func copyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer destFile.Close()

	if _, err := io.Copy(destFile, sourceFile); err != nil {
		return err
	}

	// 复制文件权限
	sourceInfo, err := os.Stat(src)
	if err != nil {
		return err
	}
	return os.Chmod(dst, sourceInfo.Mode())
}

// SignTransaction 使用账户签名交易
func (km *KeystoreManager) SignTransaction(address common.Address, tx *types.Transaction, chainID *big.Int) (*types.Transaction, error) {
	// 检查账户是否解锁
	if !km.IsUnlocked(address) {
		return nil, fmt.Errorf("account %s is locked", address.Hex())
	}

	km.mu.RLock()
	account, exists := km.accounts[address]
	km.mu.RUnlock()

	if !exists {
		return nil, fmt.Errorf("account %s not found", address.Hex())
	}

	// 签名交易
	signedTx, err := km.keystore.SignTx(*account, tx, chainID)
	if err != nil {
		return nil, fmt.Errorf("failed to sign transaction: %w", err)
	}

	return signedTx, nil
}

// SignMessage 使用账户签名消息
func (km *KeystoreManager) SignMessage(address common.Address, message []byte) ([]byte, error) {
	// 检查账户是否解锁
	if !km.IsUnlocked(address) {
		return nil, fmt.Errorf("account %s is locked", address.Hex())
	}

	km.mu.RLock()
	account, exists := km.accounts[address]
	km.mu.RUnlock()

	if !exists {
		return nil, fmt.Errorf("account %s not found", address.Hex())
	}

	// 添加以太坊消息前缀
	hash := accounts.TextHash(message)

	// 签名
	signature, err := km.keystore.SignHash(*account, hash)
	if err != nil {
		return nil, fmt.Errorf("failed to sign message: %w", err)
	}

	return signature, nil
}

// KeystoreInfo 密钥库信息
type KeystoreInfo struct {
	Address  common.Address `json:"address"`
	URL      string         `json:"url"`
	Unlocked bool           `json:"unlocked"`
}

// GetKeystoreInfo 获取keystore信息
func (km *KeystoreManager) GetKeystoreInfo() []KeystoreInfo {
	km.mu.RLock()
	defer km.mu.RUnlock()

	info := make([]KeystoreInfo, 0, len(km.accounts))
	for addr, account := range km.accounts {
		info = append(info, KeystoreInfo{
			Address:  addr,
			URL:      account.URL.String(),
			Unlocked: km.IsUnlocked(addr),
		})
	}

	return info
}

// GetKeystore 获取 keystore 实例
func (km *KeystoreManager) GetKeystore() *keystore.KeyStore {
	return km.keystore
}

// GetAccount 获取账户信息
func (km *KeystoreManager) GetAccount(address common.Address) (*accounts.Account, error) {
	km.mu.RLock()
	defer km.mu.RUnlock()
	
	account, exists := km.accounts[address]
	if !exists {
		return nil, fmt.Errorf("account %s not found", address.Hex())
	}
	
	return account, nil
}
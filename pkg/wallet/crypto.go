package wallet

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/ecdsa"
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"errors"
	"fmt"
	"io"

	"github.com/ethereum/go-ethereum/crypto"
	"golang.org/x/crypto/scrypt"
)

const (
	// ScryptN is the N parameter of Scrypt encryption algorithm, using 256MB memory and taking approximately 1s CPU time on a modern processor.
	ScryptN = 1 << 18
	// ScryptP is the P parameter of Scrypt encryption algorithm
	ScryptP = 1
	// KeyLen is the length of the derived key
	KeyLen = 32
	// SaltLen is the length of the salt
	SaltLen = 32
)

// GeneratePrivateKey 生成新的ECDSA私钥
func GeneratePrivateKey() (*ecdsa.PrivateKey, error) {
	privateKey, err := crypto.GenerateKey()
	if err != nil {
		return nil, fmt.Errorf("failed to generate private key: %w", err)
	}
	return privateKey, nil
}

// EncryptedKey 包含加密的私钥和相关参数
type EncryptedKey struct {
	CipherText string `json:"cipher_text"`
	Salt       string `json:"salt"`
	Nonce      string `json:"nonce"`
	// Scrypt parameters
	N int `json:"n"`
	R int `json:"r"`
	P int `json:"p"`
}

// EncryptPrivateKey 使用AES-256-GCM加密私钥
func EncryptPrivateKey(privateKey *ecdsa.PrivateKey, password string) (*EncryptedKey, error) {
	if privateKey == nil {
		return nil, errors.New("private key is nil")
	}
	if password == "" {
		return nil, errors.New("password is empty")
	}

	// 将私钥转换为字节
	privateKeyBytes := crypto.FromECDSA(privateKey)
	
	// 生成随机盐
	salt := make([]byte, SaltLen)
	if _, err := io.ReadFull(rand.Reader, salt); err != nil {
		return nil, fmt.Errorf("failed to generate salt: %w", err)
	}

	// 使用scrypt派生密钥
	derivedKey, err := scrypt.Key([]byte(password), salt, ScryptN, ScryptP, ScryptP, KeyLen)
	if err != nil {
		return nil, fmt.Errorf("failed to derive key: %w", err)
	}

	// 创建AES-GCM加密器
	block, err := aes.NewCipher(derivedKey)
	if err != nil {
		return nil, fmt.Errorf("failed to create cipher: %w", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %w", err)
	}

	// 生成随机nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return nil, fmt.Errorf("failed to generate nonce: %w", err)
	}

	// 加密私钥
	cipherText := gcm.Seal(nil, nonce, privateKeyBytes, nil)

	// 清理内存中的敏感数据
	clearBytes(privateKeyBytes)
	clearBytes(derivedKey)

	return &EncryptedKey{
		CipherText: hex.EncodeToString(cipherText),
		Salt:       hex.EncodeToString(salt),
		Nonce:      hex.EncodeToString(nonce),
		N:          ScryptN,
		R:          ScryptP,
		P:          ScryptP,
	}, nil
}

// DecryptPrivateKey 解密私钥
func DecryptPrivateKey(encryptedKey *EncryptedKey, password string) (*ecdsa.PrivateKey, error) {
	if encryptedKey == nil {
		return nil, errors.New("encrypted key is nil")
	}
	if password == "" {
		return nil, errors.New("password is empty")
	}

	// 解码十六进制字符串
	cipherText, err := hex.DecodeString(encryptedKey.CipherText)
	if err != nil {
		return nil, fmt.Errorf("failed to decode cipher text: %w", err)
	}

	salt, err := hex.DecodeString(encryptedKey.Salt)
	if err != nil {
		return nil, fmt.Errorf("failed to decode salt: %w", err)
	}

	nonce, err := hex.DecodeString(encryptedKey.Nonce)
	if err != nil {
		return nil, fmt.Errorf("failed to decode nonce: %w", err)
	}

	// 使用scrypt派生密钥
	derivedKey, err := scrypt.Key([]byte(password), salt, encryptedKey.N, encryptedKey.R, encryptedKey.P, KeyLen)
	if err != nil {
		return nil, fmt.Errorf("failed to derive key: %w", err)
	}
	defer clearBytes(derivedKey)

	// 创建AES-GCM解密器
	block, err := aes.NewCipher(derivedKey)
	if err != nil {
		return nil, fmt.Errorf("failed to create cipher: %w", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %w", err)
	}

	// 解密
	privateKeyBytes, err := gcm.Open(nil, nonce, cipherText, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt: %w", err)
	}
	defer clearBytes(privateKeyBytes)

	// 恢复私钥
	privateKey, err := crypto.ToECDSA(privateKeyBytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse private key: %w", err)
	}

	return privateKey, nil
}

// ValidateKeyStrength 验证密钥强度
func ValidateKeyStrength(password string) error {
	if len(password) < 12 {
		return errors.New("password must be at least 12 characters long")
	}
	
	// 检查密码复杂度
	var hasUpper, hasLower, hasNumber bool
	for _, char := range password {
		switch {
		case 'A' <= char && char <= 'Z':
			hasUpper = true
		case 'a' <= char && char <= 'z':
			hasLower = true
		case '0' <= char && char <= '9':
			hasNumber = true
		}
	}

	if !hasUpper || !hasLower || !hasNumber {
		return errors.New("password must contain uppercase, lowercase, and numbers")
	}

	return nil
}

// DeriveAddress 从私钥派生以太坊地址
func DeriveAddress(privateKey *ecdsa.PrivateKey) (string, error) {
	if privateKey == nil {
		return "", errors.New("private key is nil")
	}

	publicKey := privateKey.Public()
	publicKeyECDSA, ok := publicKey.(*ecdsa.PublicKey)
	if !ok {
		return "", errors.New("failed to cast public key to ECDSA")
	}

	address := crypto.PubkeyToAddress(*publicKeyECDSA)
	return address.Hex(), nil
}

// HashPassword 对密码进行哈希（用于额外的验证层）
func HashPassword(password string) string {
	hash := sha256.Sum256([]byte(password))
	return hex.EncodeToString(hash[:])
}

// clearBytes 安全清理字节切片中的敏感数据
func clearBytes(b []byte) {
	for i := range b {
		b[i] = 0
	}
}
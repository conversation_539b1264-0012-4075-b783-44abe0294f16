package wallet

import (
	"context"
	"errors"
	"fmt"
	"math/big"
	"sync"
	"time"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/ethclient"
	"stargate/pkg/logger"
)

// GasPrice Gas价格信息
type GasPrice struct {
	// Legacy gas price
	GasPrice *big.Int `json:"gas_price,omitempty"`
	
	// EIP-1559 gas prices
	BaseFee              *big.Int `json:"base_fee,omitempty"`
	MaxFeePerGas         *big.Int `json:"max_fee_per_gas,omitempty"`
	MaxPriorityFeePerGas *big.Int `json:"max_priority_fee_per_gas,omitempty"`
	
	// Metadata
	Chain          string    `json:"chain"`
	IsEIP1559      bool      `json:"is_eip1559"`
	UpdatedAt      time.Time `json:"updated_at"`
	BlockNumber    uint64    `json:"block_number"`
}

// GasStrategy Gas策略
type GasStrategy string

const (
	GasStrategySlow     GasStrategy = "slow"
	GasStrategyStandard GasStrategy = "standard"
	GasStrategyFast     GasStrategy = "fast"
)

// GasEstimator Gas估算器
type GasEstimator struct {
	clientManager *ClientManager
	
	// Gas价格缓存
	priceCache sync.Map // map[chain]GasPrice
	
	// 历史Gas价格追踪
	priceHistory sync.Map // map[chain][]GasPrice
	
	// 链特定配置
	chainConfigs map[string]*ChainGasConfig
	mu           sync.RWMutex
}

// ChainGasConfig 链特定的Gas配置
type ChainGasConfig struct {
	// 支持EIP-1559
	SupportsEIP1559 bool
	
	// Gas倍率（用于不同策略）
	SlowMultiplier     float64
	StandardMultiplier float64
	FastMultiplier     float64
	
	// 最大Gas价格限制
	MaxGasPrice         *big.Int
	MaxPriorityFeePerGas *big.Int
}

// NewGasEstimator 创建Gas估算器
func NewGasEstimator(clientManager *ClientManager) *GasEstimator {
	return &GasEstimator{
		clientManager: clientManager,
		chainConfigs:  getDefaultChainGasConfigs(),
	}
}

// EstimateGas 估算交易Gas用量
func (ge *GasEstimator) EstimateGas(ctx context.Context, chain string, msg ethereum.CallMsg) (uint64, error) {
	client, err := ge.clientManager.GetClient(chain)
	if err != nil {
		return 0, fmt.Errorf("failed to get client: %w", err)
	}

	// 估算Gas
	gasLimit, err := client.EstimateGas(ctx, msg)
	if err != nil {
		return 0, fmt.Errorf("failed to estimate gas: %w", err)
	}

	// 添加10%的缓冲区
	gasLimit = gasLimit * 110 / 100

	logger.Debug().
		Str("chain", chain).
		Uint64("gas_limit", gasLimit).
		Msg("Gas estimated")

	return gasLimit, nil
}

// GetGasPrice 获取当前Gas价格
func (ge *GasEstimator) GetGasPrice(ctx context.Context, chain string, strategy GasStrategy) (*GasPrice, error) {
	// 检查缓存
	if cached, ok := ge.priceCache.Load(chain); ok {
		price := cached.(GasPrice)
		// 如果缓存不超过30秒，直接返回
		if time.Since(price.UpdatedAt) < 30*time.Second {
			return ge.applyStrategy(&price, strategy), nil
		}
	}

	// 获取新的Gas价格
	client, err := ge.clientManager.GetClient(chain)
	if err != nil {
		return nil, fmt.Errorf("failed to get client: %w", err)
	}

	price, err := ge.fetchGasPrice(ctx, client, chain)
	if err != nil {
		return nil, err
	}

	// 更新缓存
	ge.priceCache.Store(chain, *price)
	
	// 记录历史
	ge.addPriceHistory(chain, price)

	return ge.applyStrategy(price, strategy), nil
}

// fetchGasPrice 从链上获取Gas价格
func (ge *GasEstimator) fetchGasPrice(ctx context.Context, client *ethclient.Client, chain string) (*GasPrice, error) {
	ge.mu.RLock()
	config := ge.chainConfigs[chain]
	ge.mu.RUnlock()

	if config == nil {
		config = getDefaultChainGasConfig()
	}

	price := &GasPrice{
		Chain:     chain,
		UpdatedAt: time.Now(),
	}

	// 获取最新区块
	block, err := client.BlockByNumber(ctx, nil)
	if err == nil && block != nil {
		price.BlockNumber = block.NumberU64()
	}

	// 检查是否支持EIP-1559
	if config.SupportsEIP1559 {
		// 获取基础费用
		if block != nil && block.BaseFee() != nil {
			price.BaseFee = block.BaseFee()
			price.IsEIP1559 = true

			// 获取优先费用建议
			priorityFee, err := client.SuggestGasTipCap(ctx)
			if err == nil {
				price.MaxPriorityFeePerGas = priorityFee
				// maxFeePerGas = baseFee * 2 + priorityFee
				price.MaxFeePerGas = new(big.Int).Add(
					new(big.Int).Mul(price.BaseFee, big.NewInt(2)),
					priorityFee,
				)
			}
		}
	}

	// 如果不支持EIP-1559或获取失败，使用传统Gas价格
	if !price.IsEIP1559 {
		gasPrice, err := client.SuggestGasPrice(ctx)
		if err != nil {
			return nil, fmt.Errorf("failed to get gas price: %w", err)
		}
		price.GasPrice = gasPrice
	}

	return price, nil
}

// applyStrategy 应用Gas策略
func (ge *GasEstimator) applyStrategy(price *GasPrice, strategy GasStrategy) *GasPrice {
	ge.mu.RLock()
	config := ge.chainConfigs[price.Chain]
	ge.mu.RUnlock()

	if config == nil {
		config = getDefaultChainGasConfig()
	}

	// 复制价格信息
	result := *price
	
	multiplier := config.StandardMultiplier
	switch strategy {
	case GasStrategySlow:
		multiplier = config.SlowMultiplier
	case GasStrategyFast:
		multiplier = config.FastMultiplier
	}

	// 应用倍率
	if result.IsEIP1559 {
		// EIP-1559: 只调整优先费
		if result.MaxPriorityFeePerGas != nil {
			result.MaxPriorityFeePerGas = new(big.Int).Mul(
				result.MaxPriorityFeePerGas,
				big.NewInt(int64(multiplier*100)),
			)
			result.MaxPriorityFeePerGas.Div(result.MaxPriorityFeePerGas, big.NewInt(100))
			
			// 确保不超过最大限制
			if config.MaxPriorityFeePerGas != nil && result.MaxPriorityFeePerGas.Cmp(config.MaxPriorityFeePerGas) > 0 {
				result.MaxPriorityFeePerGas = new(big.Int).Set(config.MaxPriorityFeePerGas)
			}
		}
		
		// 重新计算 maxFeePerGas
		if result.BaseFee != nil && result.MaxPriorityFeePerGas != nil {
			result.MaxFeePerGas = new(big.Int).Add(
				new(big.Int).Mul(result.BaseFee, big.NewInt(2)),
				result.MaxPriorityFeePerGas,
			)
		}
	} else {
		// Legacy: 调整整体Gas价格
		if result.GasPrice != nil {
			result.GasPrice = new(big.Int).Mul(
				result.GasPrice,
				big.NewInt(int64(multiplier*100)),
			)
			result.GasPrice.Div(result.GasPrice, big.NewInt(100))
			
			// 确保不超过最大限制
			if config.MaxGasPrice != nil && result.GasPrice.Cmp(config.MaxGasPrice) > 0 {
				result.GasPrice = new(big.Int).Set(config.MaxGasPrice)
			}
		}
	}

	return &result
}

// addPriceHistory 添加价格历史
func (ge *GasEstimator) addPriceHistory(chain string, price *GasPrice) {
	const maxHistory = 100

	// 获取或创建历史记录
	var history []GasPrice
	if h, ok := ge.priceHistory.Load(chain); ok {
		history = h.([]GasPrice)
	}

	// 添加新记录
	history = append(history, *price)

	// 限制历史记录长度
	if len(history) > maxHistory {
		history = history[len(history)-maxHistory:]
	}

	ge.priceHistory.Store(chain, history)
}

// GetPriceHistory 获取Gas价格历史
func (ge *GasEstimator) GetPriceHistory(chain string, duration time.Duration) []GasPrice {
	if h, ok := ge.priceHistory.Load(chain); ok {
		history := h.([]GasPrice)
		cutoff := time.Now().Add(-duration)
		
		var result []GasPrice
		for _, price := range history {
			if price.UpdatedAt.After(cutoff) {
				result = append(result, price)
			}
		}
		return result
	}
	return nil
}

// EstimateTransactionCost 估算交易总成本
func (ge *GasEstimator) EstimateTransactionCost(ctx context.Context, chain string, msg ethereum.CallMsg, strategy GasStrategy) (*big.Int, error) {
	// 估算Gas用量
	gasLimit, err := ge.EstimateGas(ctx, chain, msg)
	if err != nil {
		return nil, err
	}

	// 获取Gas价格
	gasPrice, err := ge.GetGasPrice(ctx, chain, strategy)
	if err != nil {
		return nil, err
	}

	// 计算总成本
	var cost *big.Int
	if gasPrice.IsEIP1559 && gasPrice.MaxFeePerGas != nil {
		cost = new(big.Int).Mul(gasPrice.MaxFeePerGas, big.NewInt(int64(gasLimit)))
	} else if gasPrice.GasPrice != nil {
		cost = new(big.Int).Mul(gasPrice.GasPrice, big.NewInt(int64(gasLimit)))
	} else {
		return nil, errors.New("no gas price available")
	}

	return cost, nil
}

// BuildTransaction 构建交易（包含Gas设置）
func (ge *GasEstimator) BuildTransaction(ctx context.Context, chain string, from, to common.Address, value *big.Int, data []byte, strategy GasStrategy) (*types.Transaction, error) {
	// 获取nonce
	client, err := ge.clientManager.GetClient(chain)
	if err != nil {
		return nil, fmt.Errorf("failed to get client: %w", err)
	}

	nonce, err := client.PendingNonceAt(ctx, from)
	if err != nil {
		return nil, fmt.Errorf("failed to get nonce: %w", err)
	}

	// 估算Gas
	msg := ethereum.CallMsg{
		From:  from,
		To:    &to,
		Value: value,
		Data:  data,
	}
	gasLimit, err := ge.EstimateGas(ctx, chain, msg)
	if err != nil {
		return nil, err
	}

	// 获取Gas价格
	gasPrice, err := ge.GetGasPrice(ctx, chain, strategy)
	if err != nil {
		return nil, err
	}

	// 获取链ID
	chainID, err := ge.clientManager.GetChainID(chain)
	if err != nil {
		return nil, err
	}

	// 构建交易
	var tx *types.Transaction
	if gasPrice.IsEIP1559 {
		// EIP-1559交易
		tx = types.NewTx(&types.DynamicFeeTx{
			ChainID:   chainID,
			Nonce:     nonce,
			GasTipCap: gasPrice.MaxPriorityFeePerGas,
			GasFeeCap: gasPrice.MaxFeePerGas,
			Gas:       gasLimit,
			To:        &to,
			Value:     value,
			Data:      data,
		})
	} else {
		// Legacy交易
		tx = types.NewTransaction(nonce, to, value, gasLimit, gasPrice.GasPrice, data)
	}

	return tx, nil
}

// getDefaultChainGasConfigs 获取默认的链Gas配置
func getDefaultChainGasConfigs() map[string]*ChainGasConfig {
	// 100 gwei max
	maxGasPrice := big.NewInt(100000000000)
	// 10 gwei max priority fee
	maxPriorityFee := big.NewInt(10000000000)

	return map[string]*ChainGasConfig{
		"ethereum": {
			SupportsEIP1559:      true,
			SlowMultiplier:       0.8,
			StandardMultiplier:   1.0,
			FastMultiplier:       1.5,
			MaxGasPrice:          maxGasPrice,
			MaxPriorityFeePerGas: maxPriorityFee,
		},
		"bsc": {
			SupportsEIP1559:    false,
			SlowMultiplier:     0.9,
			StandardMultiplier: 1.0,
			FastMultiplier:     1.2,
			MaxGasPrice:        big.NewInt(20000000000), // 20 gwei
		},
		"polygon": {
			SupportsEIP1559:      true,
			SlowMultiplier:       0.8,
			StandardMultiplier:   1.0,
			FastMultiplier:       2.0,
			MaxGasPrice:          big.NewInt(500000000000), // 500 gwei
			MaxPriorityFeePerGas: big.NewInt(50000000000),  // 50 gwei
		},
		"arbitrum": {
			SupportsEIP1559:      true,
			SlowMultiplier:       0.9,
			StandardMultiplier:   1.0,
			FastMultiplier:       1.3,
			MaxGasPrice:          big.NewInt(10000000000), // 10 gwei
			MaxPriorityFeePerGas: big.NewInt(1000000000),  // 1 gwei
		},
		"optimism": {
			SupportsEIP1559:      true,
			SlowMultiplier:       0.9,
			StandardMultiplier:   1.0,
			FastMultiplier:       1.3,
			MaxGasPrice:          big.NewInt(10000000000), // 10 gwei
			MaxPriorityFeePerGas: big.NewInt(1000000000),  // 1 gwei
		},
		"avalanche": {
			SupportsEIP1559:      true,
			SlowMultiplier:       0.8,
			StandardMultiplier:   1.0,
			FastMultiplier:       1.5,
			MaxGasPrice:          big.NewInt(100000000000), // 100 gwei
			MaxPriorityFeePerGas: big.NewInt(10000000000),  // 10 gwei
		},
	}
}

// getDefaultChainGasConfig 获取默认的Gas配置
func getDefaultChainGasConfig() *ChainGasConfig {
	return &ChainGasConfig{
		SupportsEIP1559:    false,
		SlowMultiplier:     0.8,
		StandardMultiplier: 1.0,
		FastMultiplier:     1.5,
		MaxGasPrice:        big.NewInt(100000000000), // 100 gwei
	}
}
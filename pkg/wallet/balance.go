package wallet

import (
	"context"
	"errors"
	"fmt"
	"math/big"
	"strings"
	"sync"
	"time"

	"stargate/pkg/logger"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/ethclient"
)

// TokenInfo 代币信息
type TokenInfo struct {
	Address  common.Address `json:"address"`
	Symbol   string         `json:"symbol"`
	Name     string         `json:"name"`
	Decimals uint8          `json:"decimals"`
	IsNative bool           `json:"is_native"`
}

// Balance 余额信息
type Balance struct {
	Token    TokenInfo `json:"token"`
	Amount   *big.Int  `json:"amount"`
	Chain    string    `json:"chain"`
	UpdateAt time.Time `json:"update_at"`
}

// BalanceQuery 余额查询器
type BalanceQuery struct {
	clientManager *ClientManager
	tokenCache    sync.Map // map[chain:address]TokenInfo
	erc20ABI      abi.ABI
	
	// 代币白名单
	tokenWhitelist map[string]map[string]bool // map[chain]map[address]bool
	mu             sync.RWMutex
}

// NewBalanceQuery 创建余额查询器
func NewBalanceQuery(clientManager *ClientManager) (*BalanceQuery, error) {
	if clientManager == nil {
		return nil, errors.New("client manager is nil")
	}

	// 解析 ERC20 ABI
	parsedABI, err := abi.JSON(strings.NewReader(ERC20ABI))
	if err != nil {
		return nil, fmt.Errorf("failed to parse ERC20 ABI: %w", err)
	}

	return &BalanceQuery{
		clientManager:  clientManager,
		erc20ABI:       parsedABI,
		tokenWhitelist: make(map[string]map[string]bool),
	}, nil
}

// AddTokenWhitelist 添加代币到白名单
func (bq *BalanceQuery) AddTokenWhitelist(chain string, tokenAddress string) {
	bq.mu.Lock()
	defer bq.mu.Unlock()

	if bq.tokenWhitelist[chain] == nil {
		bq.tokenWhitelist[chain] = make(map[string]bool)
	}
	
	// 统一转换为校验和地址格式
	bq.tokenWhitelist[chain][strings.ToLower(tokenAddress)] = true
}


// GetBalance 获取余额
func (bq *BalanceQuery) GetBalance(ctx context.Context, chain string, walletAddress string, tokenAddress string) (*Balance, error) {
	// 获取客户端
	client, err := bq.clientManager.GetClient(chain)
	if err != nil {
		return nil, fmt.Errorf("failed to get client: %w", err)
	}

	// 转换地址
	wallet := common.HexToAddress(walletAddress)
	
	// 检查是否为原生币
	if tokenAddress == "" || strings.ToLower(tokenAddress) == "0x0" {
		return bq.getNativeBalance(ctx, client, chain, wallet)
	}

	// 获取 ERC20 余额
	return bq.getERC20Balance(ctx, client, chain, wallet, tokenAddress)
}

// getNativeBalance 获取原生币余额
func (bq *BalanceQuery) getNativeBalance(ctx context.Context, client *ethclient.Client, chain string, wallet common.Address) (*Balance, error) {
	balance, err := client.BalanceAt(ctx, wallet, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get native balance: %w", err)
	}

	// 获取原生币信息
	nativeInfo := bq.getNativeTokenInfo(chain)

	return &Balance{
		Token:    nativeInfo,
		Amount:   balance,
		Chain:    chain,
		UpdateAt: time.Now(),
	}, nil
}

// getERC20Balance 获取 ERC20 代币余额
func (bq *BalanceQuery) getERC20Balance(ctx context.Context, client *ethclient.Client, chain string, wallet common.Address, tokenAddress string) (*Balance, error) {
	token := common.HexToAddress(tokenAddress)

	// 获取或缓存代币信息
	tokenInfo, err := bq.getTokenInfo(ctx, client, chain, token)
	if err != nil {
		return nil, fmt.Errorf("failed to get token info: %w", err)
	}

	// 构造 balanceOf 调用
	data, err := bq.erc20ABI.Pack("balanceOf", wallet)
	if err != nil {
		return nil, fmt.Errorf("failed to pack balanceOf: %w", err)
	}

	// 调用合约
	msg := ethereum.CallMsg{
		To:   &token,
		Data: data,
	}

	result, err := client.CallContract(ctx, msg, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to call balanceOf: %w", err)
	}

	// 解析结果
	var balance *big.Int
	err = bq.erc20ABI.UnpackIntoInterface(&balance, "balanceOf", result)
	if err != nil {
		return nil, fmt.Errorf("failed to unpack balance: %w", err)
	}

	return &Balance{
		Token:    tokenInfo,
		Amount:   balance,
		Chain:    chain,
		UpdateAt: time.Now(),
	}, nil
}

// getTokenInfo 获取代币信息（带缓存）
func (bq *BalanceQuery) getTokenInfo(ctx context.Context, client *ethclient.Client, chain string, token common.Address) (TokenInfo, error) {
	cacheKey := fmt.Sprintf("%s:%s", chain, token.Hex())
	
	// 检查缓存
	if cached, ok := bq.tokenCache.Load(cacheKey); ok {
		return cached.(TokenInfo), nil
	}

	// 获取代币信息
	info := TokenInfo{
		Address:  token,
		IsNative: false,
	}

	// 获取 symbol
	if symbol, err := bq.callTokenMethod(ctx, client, token, "symbol"); err == nil {
		info.Symbol = symbol.(string)
	}

	// 获取 name
	if name, err := bq.callTokenMethod(ctx, client, token, "name"); err == nil {
		info.Name = name.(string)
	}

	// 获取 decimals
	if decimals, err := bq.callTokenMethod(ctx, client, token, "decimals"); err == nil {
		info.Decimals = decimals.(uint8)
	}

	// 缓存结果
	bq.tokenCache.Store(cacheKey, info)

	logger.Debug().
		Str("chain", chain).
		Str("token", token.Hex()).
		Str("symbol", info.Symbol).
		Uint8("decimals", info.Decimals).
		Msg("Token info cached")

	return info, nil
}

// callTokenMethod 调用代币方法
func (bq *BalanceQuery) callTokenMethod(ctx context.Context, client *ethclient.Client, token common.Address, method string) (interface{}, error) {
	data, err := bq.erc20ABI.Pack(method)
	if err != nil {
		return nil, err
	}

	msg := ethereum.CallMsg{
		To:   &token,
		Data: data,
	}

	result, err := client.CallContract(ctx, msg, nil)
	if err != nil {
		return nil, err
	}

	// 解析结果
	output, err := bq.erc20ABI.Unpack(method, result)
	if err != nil {
		return nil, err
	}

	if len(output) == 0 {
		return nil, errors.New("empty output")
	}

	return output[0], nil
}

// GetMultipleBalances 批量获取余额
func (bq *BalanceQuery) GetMultipleBalances(ctx context.Context, chain string, walletAddress string, tokenAddresses []string) ([]*Balance, error) {
	balances := make([]*Balance, 0, len(tokenAddresses))
	
	// 使用 goroutine 并发查询
	type result struct {
		index   int
		balance *Balance
		err     error
	}
	
	results := make(chan result, len(tokenAddresses))
	
	for i, tokenAddr := range tokenAddresses {
		go func(idx int, addr string) {
			bal, err := bq.GetBalance(ctx, chain, walletAddress, addr)
			results <- result{index: idx, balance: bal, err: err}
		}(i, tokenAddr)
	}
	
	// 收集结果
	errors := make([]error, len(tokenAddresses))
	tempBalances := make([]*Balance, len(tokenAddresses))
	
	for i := 0; i < len(tokenAddresses); i++ {
		res := <-results
		if res.err != nil {
			errors[res.index] = res.err
			logger.Warn().
				Str("chain", chain).
				Str("token", tokenAddresses[res.index]).
				Err(res.err).
				Msg("Failed to get balance")
		} else {
			tempBalances[res.index] = res.balance
		}
	}
	
	// 构建结果（忽略错误的查询）
	for _, bal := range tempBalances {
		if bal != nil {
			balances = append(balances, bal)
		}
	}
	
	return balances, nil
}

// getNativeTokenInfo 获取原生币信息
func (bq *BalanceQuery) getNativeTokenInfo(chain string) TokenInfo {
	nativeTokens := map[string]TokenInfo{
		"ethereum": {
			Symbol:   "ETH",
			Name:     "Ethereum",
			Decimals: 18,
			IsNative: true,
		},
		"bsc": {
			Symbol:   "BNB",
			Name:     "Binance Coin",
			Decimals: 18,
			IsNative: true,
		},
		"polygon": {
			Symbol:   "MATIC",
			Name:     "Polygon",
			Decimals: 18,
			IsNative: true,
		},
		"arbitrum": {
			Symbol:   "ETH",
			Name:     "Ethereum",
			Decimals: 18,
			IsNative: true,
		},
		"optimism": {
			Symbol:   "ETH",
			Name:     "Ethereum",
			Decimals: 18,
			IsNative: true,
		},
		"avalanche": {
			Symbol:   "AVAX",
			Name:     "Avalanche",
			Decimals: 18,
			IsNative: true,
		},
	}

	if info, exists := nativeTokens[chain]; exists {
		return info
	}

	// 默认返回 ETH
	return TokenInfo{
		Symbol:   "ETH",
		Name:     "Unknown",
		Decimals: 18,
		IsNative: true,
	}
}

// FormatBalance 格式化余额显示
func FormatBalance(balance *Balance) string {
	if balance.Amount == nil {
		return "0"
	}

	// 转换为浮点数
	divisor := new(big.Float).SetInt(new(big.Int).Exp(big.NewInt(10), big.NewInt(int64(balance.Token.Decimals)), nil))
	amount := new(big.Float).SetInt(balance.Amount)
	result := new(big.Float).Quo(amount, divisor)

	return fmt.Sprintf("%s %s", result.Text('f', 6), balance.Token.Symbol)
}
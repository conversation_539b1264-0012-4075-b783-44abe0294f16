package wallet

import (
	"context"
	"math/big"
	"sync"
	"time"

	"stargate/pkg/logger"

	"github.com/ethereum/go-ethereum/common"
)

// MonitorStatus 监控状态
type MonitorStatus struct {
	Running           bool                        `json:"running"`
	LastCheckTime     time.Time                   `json:"last_check_time"`
	BalanceAlerts     []BalanceAlert              `json:"balance_alerts"`
	TransactionCount  map[string]int              `json:"transaction_count"`
	GasUsage          map[string]*big.Int         `json:"gas_usage"`
	ErrorCount        int                         `json:"error_count"`
	SuccessRate       float64                     `json:"success_rate"`
}

// BalanceAlert 余额告警
type BalanceAlert struct {
	Chain     string    `json:"chain"`
	Address   string    `json:"address"`
	Token     string    `json:"token"`
	Balance   *big.Int  `json:"balance"`
	Threshold *big.Int  `json:"threshold"`
	Timestamp time.Time `json:"timestamp"`
	Type      string    `json:"type"` // "low", "sudden_decrease"
}

// TransactionEvent 交易事件
type TransactionEvent struct {
	Chain     string         `json:"chain"`
	TxHash    common.Hash    `json:"tx_hash"`
	From      common.Address `json:"from"`
	To        common.Address `json:"to"`
	Value     *big.Int       `json:"value"`
	GasUsed   uint64         `json:"gas_used"`
	Status    uint64         `json:"status"` // 1 for success, 0 for failure
	Timestamp time.Time      `json:"timestamp"`
}

// WalletMonitor 钱包监控器
type WalletMonitor struct {
	manager         *Manager
	checkInterval   time.Duration
	
	// 监控数据
	alerts          []BalanceAlert
	transactions    []TransactionEvent
	
	// 统计数据
	txCount         map[string]int      // chain -> count
	gasUsage        map[string]*big.Int // chain -> total gas used
	successCount    int
	failureCount    int
	
	// 阈值配置
	lowBalanceThresholds map[string]*big.Int // chain -> threshold for native token
	
	mu              sync.RWMutex
	running         bool
	lastCheckTime   time.Time
}

// NewWalletMonitor 创建钱包监控器
func NewWalletMonitor(manager *Manager, checkInterval time.Duration) *WalletMonitor {
	if checkInterval == 0 {
		checkInterval = 30 * time.Second
	}

	wm := &WalletMonitor{
		manager:       manager,
		checkInterval: checkInterval,
		alerts:        make([]BalanceAlert, 0),
		transactions:  make([]TransactionEvent, 0),
		txCount:       make(map[string]int),
		gasUsage:      make(map[string]*big.Int),
		lowBalanceThresholds: getDefaultLowBalanceThresholds(),
	}

	return wm
}

// Start 启动监控
func (wm *WalletMonitor) Start(ctx context.Context) {
	wm.mu.Lock()
	if wm.running {
		wm.mu.Unlock()
		return
	}
	wm.running = true
	wm.mu.Unlock()

	logger.Info().
		Dur("interval", wm.checkInterval).
		Msg("Wallet monitor started")

	go wm.monitorLoop(ctx)
}

// Stop 停止监控
func (wm *WalletMonitor) Stop() {
	wm.mu.Lock()
	wm.running = false
	wm.mu.Unlock()

	logger.Info().Msg("Wallet monitor stopped")
}

// monitorLoop 监控循环
func (wm *WalletMonitor) monitorLoop(ctx context.Context) {
	ticker := time.NewTicker(wm.checkInterval)
	defer ticker.Stop()

	// 立即执行一次检查
	wm.performCheck(ctx)

	for {
		select {
		case <-ctx.Done():
			wm.Stop()
			return
		case <-ticker.C:
			wm.performCheck(ctx)
		}
	}
}

// performCheck 执行检查
func (wm *WalletMonitor) performCheck(ctx context.Context) {
	wm.mu.Lock()
	wm.lastCheckTime = time.Now()
	wm.mu.Unlock()

	// 获取主地址
	primaryAddr := wm.manager.GetPrimaryAddress()
	if primaryAddr == (common.Address{}) {
		logger.Warn().Msg("No primary address set, skipping monitor check")
		return
	}

	// 检查所有链的余额
	chains := wm.manager.clientManager.GetAllChains()
	for chainName := range chains {
		// 检查原生币余额
		wm.checkBalance(ctx, chainName, primaryAddr, "")
		
		// TODO: 检查重要代币余额
		// 这里可以从配置中读取需要监控的代币列表
	}

	// 清理过期告警
	wm.cleanupOldAlerts()

	logger.Debug().
		Int("alerts", len(wm.alerts)).
		Int("tx_count", wm.successCount+wm.failureCount).
		Msg("Monitor check completed")
}

// checkBalance 检查余额
func (wm *WalletMonitor) checkBalance(ctx context.Context, chain string, address common.Address, tokenAddress string) {
	// 直接从链上查询最新余额，不使用缓存
	balance, err := wm.manager.balanceQuery.GetBalance(ctx, chain, address.Hex(), tokenAddress)
	if err != nil {
		logger.Error().
			Str("chain", chain).
			Str("address", address.Hex()).
			Str("token", tokenAddress).
			Err(err).
			Msg("Failed to get balance from chain")
		return
	}
	
	// 检查缓存中的余额是否发生变化
	cachedBalance, found := wm.manager.cache.GetBalance(chain, address.Hex(), tokenAddress)
	if found && cachedBalance != nil && cachedBalance.Amount != nil && balance.Amount != nil {
		// 如果余额没有变化，不需要更新缓存
		if cachedBalance.Amount.Cmp(balance.Amount) == 0 {
			return
		}
	}
	
	// 更新 manager 的缓存并发布事件
	wm.manager.UpdateBalanceWithEvent(context.Background(), chain, address.Hex(), tokenAddress, balance)
}

// RecordTransaction 记录交易
func (wm *WalletMonitor) RecordTransaction(event TransactionEvent) {
	wm.mu.Lock()
	defer wm.mu.Unlock()

	wm.transactions = append(wm.transactions, event)
	
	// 更新统计
	wm.txCount[event.Chain]++
	
	if wm.gasUsage[event.Chain] == nil {
		wm.gasUsage[event.Chain] = big.NewInt(0)
	}
	wm.gasUsage[event.Chain].Add(wm.gasUsage[event.Chain], big.NewInt(int64(event.GasUsed)))
	
	if event.Status == 1 {
		wm.successCount++
	} else {
		wm.failureCount++
	}
	
	// 限制历史记录长度
	if len(wm.transactions) > 1000 {
		wm.transactions = wm.transactions[len(wm.transactions)-1000:]
	}
}

// GetStatus 获取监控状态
func (wm *WalletMonitor) GetStatus() *MonitorStatus {
	wm.mu.RLock()
	defer wm.mu.RUnlock()

	successRate := float64(0)
	total := wm.successCount + wm.failureCount
	if total > 0 {
		successRate = float64(wm.successCount) / float64(total) * 100
	}

	// 复制数据避免外部修改
	alerts := make([]BalanceAlert, len(wm.alerts))
	copy(alerts, wm.alerts)
	
	txCount := make(map[string]int)
	for k, v := range wm.txCount {
		txCount[k] = v
	}
	
	gasUsage := make(map[string]*big.Int)
	for k, v := range wm.gasUsage {
		gasUsage[k] = new(big.Int).Set(v)
	}

	return &MonitorStatus{
		Running:          wm.running,
		LastCheckTime:    wm.lastCheckTime,
		BalanceAlerts:    alerts,
		TransactionCount: txCount,
		GasUsage:         gasUsage,
		ErrorCount:       wm.failureCount,
		SuccessRate:      successRate,
	}
}

// SetLowBalanceThreshold 设置低余额阈值
func (wm *WalletMonitor) SetLowBalanceThreshold(chain string, threshold *big.Int) {
	wm.mu.Lock()
	defer wm.mu.Unlock()
	
	wm.lowBalanceThresholds[chain] = threshold
}

// cleanupOldAlerts 清理过期告警
func (wm *WalletMonitor) cleanupOldAlerts() {
	wm.mu.Lock()
	defer wm.mu.Unlock()

	// 保留最近24小时的告警
	cutoff := time.Now().Add(-24 * time.Hour)
	newAlerts := make([]BalanceAlert, 0)
	
	for _, alert := range wm.alerts {
		if alert.Timestamp.After(cutoff) {
			newAlerts = append(newAlerts, alert)
		}
	}
	
	wm.alerts = newAlerts
}

// getDefaultLowBalanceThresholds 获取默认的低余额阈值
func getDefaultLowBalanceThresholds() map[string]*big.Int {
	// 单位：Wei
	return map[string]*big.Int{
		"ethereum": new(big.Int).Mul(big.NewInt(5), big.NewInt(1e16)),  // 0.05 ETH
		"bsc":      new(big.Int).Mul(big.NewInt(1), big.NewInt(1e16)),  // 0.01 BNB
		"polygon":  new(big.Int).Mul(big.NewInt(10), big.NewInt(1e18)), // 10 MATIC
		"arbitrum": new(big.Int).Mul(big.NewInt(1), big.NewInt(1e16)),  // 0.01 ETH
		"optimism": new(big.Int).Mul(big.NewInt(1), big.NewInt(1e16)),  // 0.01 ETH
		"avalanche": new(big.Int).Mul(big.NewInt(5), big.NewInt(1e17)), // 0.5 AVAX
	}
}
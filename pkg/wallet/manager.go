package wallet

import (
	"context"
	"crypto/ecdsa"
	"errors"
	"fmt"
	"math/big"
	"sync"
	"time"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/ethclient"
	"stargate/internal/config"
	"stargate/pkg/events"
	"stargate/pkg/logger"
)

// Account 账户信息
type Account struct {
	Address    common.Address
	privateKey *ecdsa.PrivateKey
}

// Manager 钱包管理器，整合所有钱包功能
type Manager struct {
	config          *config.WalletConfig
	clientManager   *ClientManager
	keystoreManager *KeystoreManager
	signerManager   *SignerManager // 签名管理器
	balanceQuery    *BalanceQuery
	gasEstimator    *GasEstimator
	erc20Manager    *ERC20Manager
	
	// 缓存系统
	cache           *WalletCache
	
	// 监控相关
	monitor         MonitorInterface
	wsManager       *WebSocketManager // WebSocket 管理器
	monitorCtx      context.Context
	monitorCancel   context.CancelFunc
	
	// 主钱包地址
	primaryAddress  common.Address
	
	// 链配置（用于获取监控的代币）
	chainConfigs    map[string]*config.ChainConfig
	
	// 事件总线
	eventBus        events.EventBus
	
	mu              sync.RWMutex
}

// NewManager 创建新的钱包管理器
func NewManager(cfg *config.WalletConfig, eventBus events.EventBus) (*Manager, error) {
	if cfg == nil {
		return nil, errors.New("wallet config is nil")
	}

	// 创建客户端管理器
	clientManager := NewClientManager(30 * time.Second)

	// 创建keystore管理器
	keystoreManager, err := NewKeystoreManager(cfg.KeystorePath, 5*time.Minute)
	if err != nil {
		return nil, fmt.Errorf("failed to create keystore manager: %w", err)
	}

	// 创建余额查询器
	balanceQuery, err := NewBalanceQuery(clientManager)
	if err != nil {
		return nil, fmt.Errorf("failed to create balance query: %w", err)
	}

	// 创建Gas估算器
	gasEstimator := NewGasEstimator(clientManager)
	
	// 创建缓存系统
	cache := NewWalletCache(nil) // 使用默认配置
	
	// 创建签名管理器
	signerManager := NewSignerManager()

	m := &Manager{
		config:          cfg,
		clientManager:   clientManager,
		keystoreManager: keystoreManager,
		signerManager:   signerManager,
		balanceQuery:    balanceQuery,
		gasEstimator:    gasEstimator,
		cache:           cache,
		eventBus:        eventBus,
	}
	
	// 创建 ERC20 管理器
	m.erc20Manager = NewERC20Manager(m)

	return m, nil
}

// LoadWallet 加载钱包账户（第一步：只加载账户信息）
func (m *Manager) LoadWallet() error {
	// 获取或创建主钱包地址
	accounts := m.keystoreManager.GetAccounts()
	if len(accounts) == 0 {
		logger.Warn().Msg("No accounts found in keystore")
		return nil
	}
	
	m.primaryAddress = accounts[0]
	logger.Info().
		Str("address", m.primaryAddress.Hex()).
		Msg("Using primary wallet address")
	
	return nil
}

// InitializeChains 初始化链连接（第二步：建立链连接）
func (m *Manager) InitializeChains(ctx context.Context, chains map[string]*config.ChainConfig) error {
	// 存储链配置
	m.mu.Lock()
	m.chainConfigs = chains
	m.mu.Unlock()
	
	// 创建 WebSocket 管理器
	m.wsManager = NewWebSocketManager(m)
	
	// 使用 WaitGroup 并发添加链配置
	var wg sync.WaitGroup
	for name, cfg := range chains {
		wg.Add(1)
		go func(chainName string, chainCfg *config.ChainConfig) {
			defer wg.Done()
			
			chainConfig := &ChainConfig{
				Name:           chainName,
				ChainID:        chainCfg.ChainID,
				RPC:            chainCfg.RPCURL,
				MaxConnections: 3,
				Timeout:        10 * time.Second,
			}
			
			if err := m.clientManager.AddChain(chainConfig); err != nil {
				logger.Warn().
					Str("chain", chainName).
					Err(err).
					Msg("Failed to add chain")
				// 继续添加其他链
			}
			
			// 添加 WebSocket 支持
			if chainCfg.WSSURL != "" {
				checkInterval := chainCfg.CheckInterval
				if checkInterval == 0 {
					checkInterval = 30 * time.Second // 默认值
				}
				if err := m.wsManager.AddChain(chainName, chainCfg.WSSURL, checkInterval); err != nil {
					logger.Warn().
						Str("chain", chainName).
						Err(err).
						Msg("Failed to add WebSocket client")
				}
			}
		}(name, cfg)
	}
	
	// 等待所有链添加完成
	wg.Wait()

	// 启动客户端管理器
	m.clientManager.Start(ctx)

	return nil
}

// StartMonitor 启动余额监控（第三步：在链连接建立后启动监控）
func (m *Manager) StartMonitor(ctx context.Context, chainConfigs map[string]*config.ChainConfig) {
	if m.config != nil {
		m.monitorCtx, m.monitorCancel = context.WithCancel(ctx)
		// 使用 V2 版本的监控器，支持每条链独立的检查间隔
		m.monitor = NewWalletMonitorV2(m, chainConfigs)
		m.monitor.Start(m.monitorCtx)
	}
}

// InitializePrivateKeySigner 初始化私钥签名器（用于24小时自动化运行）
func (m *Manager) InitializePrivateKeySigner(address common.Address, password string) error {
	// 从 keystore 导出私钥
	privateKey, err := m.keystoreManager.ExportPrivateKey(address, password)
	if err != nil {
		return fmt.Errorf("failed to export private key: %w", err)
	}
	
	// 创建私钥签名器
	signer, err := NewPrivateKeySigner(privateKey)
	if err != nil {
		return fmt.Errorf("failed to create private key signer: %w", err)
	}
	
	// 添加到签名管理器
	if err := m.signerManager.AddSigner(signer); err != nil {
		return fmt.Errorf("failed to add signer: %w", err)
	}
	
	// 设置为主地址
	m.mu.Lock()
	m.primaryAddress = address
	m.mu.Unlock()
	
	logger.Info().
		Str("address", address.Hex()).
		Msg("Private key signer initialized for 24/7 operation")
	
	return nil
}

// Stop 停止钱包管理器
func (m *Manager) Stop() {
	// 停止监控
	if m.monitorCancel != nil {
		m.monitorCancel()
	}
	
	// 停止 WebSocket 管理器
	if m.wsManager != nil {
		m.wsManager.Stop()
	}

	// 停止缓存清理
	if m.cache != nil {
		m.cache.Stop()
	}
	
	// 清除所有签名器（安全考虑）
	if m.signerManager != nil {
		m.signerManager.Clear()
	}

	// 停止客户端管理器
	m.clientManager.Stop()

	logger.Info().Msg("Wallet manager stopped")
}

// GetPrimaryAddress 获取主钱包地址
func (m *Manager) GetPrimaryAddress() common.Address {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.primaryAddress
}

// SetPrimaryAddress 设置主钱包地址
func (m *Manager) SetPrimaryAddress(address common.Address) error {
	// 验证地址存在于keystore中
	accounts := m.keystoreManager.GetAccounts()
	found := false
	for _, acc := range accounts {
		if acc == address {
			found = true
			break
		}
	}
	
	if !found {
		return fmt.Errorf("address %s not found in keystore", address.Hex())
	}

	m.mu.Lock()
	m.primaryAddress = address
	m.mu.Unlock()

	logger.Info().
		Str("address", address.Hex()).
		Msg("Primary wallet address updated")

	return nil
}

// CreateAccount 创建新账户
func (m *Manager) CreateAccount(password string) (common.Address, error) {
	return m.keystoreManager.CreateAccount(password)
}

// ImportPrivateKey 导入私钥
func (m *Manager) ImportPrivateKey(privateKey *ecdsa.PrivateKey, password string) (common.Address, error) {
	return m.keystoreManager.ImportPrivateKey(privateKey, password)
}

// UnlockAccount 解锁账户
func (m *Manager) UnlockAccount(address common.Address, password string) error {
	return m.keystoreManager.UnlockAccount(address, password)
}

// GetBalance 获取余额
func (m *Manager) GetBalance(ctx context.Context, chain string, address common.Address, tokenAddress string) (*Balance, error) {
	// Check cache first
	if balance, found := m.cache.GetBalance(chain, address.Hex(), tokenAddress); found {
		logger.Debug().
			Str("chain", chain).
			Str("address", address.Hex()).
			Str("token", tokenAddress).
			Msg("Balance retrieved from cache")
		return balance, nil
	}
	
	// Get old balance for comparison (might not exist)
	var oldBalance *big.Int
	if oldBal, found := m.cache.GetBalance(chain, address.Hex(), tokenAddress); found {
		oldBalance = oldBal.Amount
	} else {
		oldBalance = big.NewInt(0)
	}
	
	// Fetch from chain
	balance, err := m.balanceQuery.GetBalance(ctx, chain, address.Hex(), tokenAddress)
	if err != nil {
		return nil, err
	}
	
	// Cache the result
	m.cache.SetBalance(chain, address.Hex(), tokenAddress, balance)
	
	// Publish balance update event if balance changed
	if m.eventBus != nil && oldBalance.Cmp(balance.Amount) != 0 {
		m.publishBalanceUpdate(ctx, chain, tokenAddress, address.Hex(), oldBalance, balance.Amount, "balance_query")
	}
	
	return balance, nil
}

// GetMultiChainBalances 获取多链余额
func (m *Manager) GetMultiChainBalances(ctx context.Context, address common.Address, tokenAddress string) (map[string]*Balance, error) {
	chains := m.clientManager.GetAllChains()
	balances := make(map[string]*Balance)
	
	var wg sync.WaitGroup
	var mu sync.Mutex
	
	for chainName := range chains {
		wg.Add(1)
		go func(chain string) {
			defer wg.Done()
			
			balance, err := m.GetBalance(ctx, chain, address, tokenAddress)
			if err != nil {
				logger.Warn().
					Str("chain", chain).
					Err(err).
					Msg("Failed to get balance")
				return
			}
			
			mu.Lock()
			balances[chain] = balance
			mu.Unlock()
		}(chainName)
	}
	
	wg.Wait()
	return balances, nil
}

// EstimateGas 估算Gas
func (m *Manager) EstimateGas(ctx context.Context, chain string, msg ethereum.CallMsg) (uint64, error) {
	return m.gasEstimator.EstimateGas(ctx, chain, msg)
}

// GetGasPrice 获取Gas价格
func (m *Manager) GetGasPrice(ctx context.Context, chain string, strategy GasStrategy) (*GasPrice, error) {
	// Check cache first
	if price, found := m.cache.GetGasPrice(chain, strategy); found {
		logger.Debug().
			Str("chain", chain).
			Str("strategy", string(strategy)).
			Msg("Gas price retrieved from cache")
		return price, nil
	}
	
	// Fetch current gas price
	price, err := m.gasEstimator.GetGasPrice(ctx, chain, strategy)
	if err != nil {
		return nil, err
	}
	
	// Cache the result
	m.cache.SetGasPrice(chain, strategy, price)
	
	return price, nil
}

// SendTransaction 发送交易
func (m *Manager) SendTransaction(ctx context.Context, chain string, to common.Address, value *big.Int, data []byte, gasStrategy GasStrategy) (*types.Transaction, error) {
	// 获取主地址
	from := m.GetPrimaryAddress()
	if from == (common.Address{}) {
		return nil, errors.New("no primary wallet address set")
	}

	// 构建交易
	tx, err := m.gasEstimator.BuildTransaction(ctx, chain, from, to, value, data, gasStrategy)
	if err != nil {
		return nil, fmt.Errorf("failed to build transaction: %w", err)
	}

	// 获取链ID
	chainID, err := m.clientManager.GetChainID(chain)
	if err != nil {
		return nil, fmt.Errorf("failed to get chain ID: %w", err)
	}

	// 签名交易
	var signedTx *types.Transaction
	
	// 优先使用签名管理器（用于24/7运行）
	signer, err := m.signerManager.GetSigner(from)
	if err == nil {
		signedTx, err = signer.SignTransaction(tx, chainID)
		if err != nil {
			return nil, fmt.Errorf("failed to sign transaction with signer: %w", err)
		}
	} else {
		// 回退到 keystore 签名（需要账户解锁）
		if !m.keystoreManager.IsUnlocked(from) {
			return nil, fmt.Errorf("account %s is locked and no signer available", from.Hex())
		}
		signedTx, err = m.keystoreManager.SignTransaction(from, tx, chainID)
		if err != nil {
			return nil, fmt.Errorf("failed to sign transaction with keystore: %w", err)
		}
	}

	// 发送交易
	client, err := m.clientManager.GetClient(chain)
	if err != nil {
		return nil, fmt.Errorf("failed to get client: %w", err)
	}

	if err := client.SendTransaction(ctx, signedTx); err != nil {
		return nil, fmt.Errorf("failed to send transaction: %w", err)
	}

	// Invalidate nonce cache after successful transaction
	m.cache.InvalidateNonce(chain, from.Hex())

	logger.Info().
		Str("chain", chain).
		Str("hash", signedTx.Hash().Hex()).
		Str("from", from.Hex()).
		Str("to", to.Hex()).
		Str("value", value.String()).
		Msg("Transaction sent")

	return signedTx, nil
}

// WaitForTransaction 等待交易确认
func (m *Manager) WaitForTransaction(ctx context.Context, chain string, txHash common.Hash) (*types.Receipt, error) {
	client, err := m.clientManager.GetClient(chain)
	if err != nil {
		return nil, fmt.Errorf("failed to get client: %w", err)
	}

	// 等待交易被挖矿
	ticker := time.NewTicker(3 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		case <-ticker.C:
			receipt, err := client.TransactionReceipt(ctx, txHash)
			if err == nil {
				return receipt, nil
			}
			// 继续等待
		}
	}
}

// AddTokenToWhitelist 添加代币到白名单
func (m *Manager) AddTokenToWhitelist(chain string, tokenAddress string) {
	m.balanceQuery.AddTokenWhitelist(chain, tokenAddress)
}

// GetConnectionStats 获取连接统计
func (m *Manager) GetConnectionStats() map[string]ConnectionStats {
	return m.clientManager.GetConnectionStats()
}

// GetKeystoreInfo 获取keystore信息
func (m *Manager) GetKeystoreInfo() []KeystoreInfo {
	return m.keystoreManager.GetKeystoreInfo()
}

// GetGasPriceHistory 获取Gas价格历史
func (m *Manager) GetGasPriceHistory(chain string, duration time.Duration) []GasPrice {
	return m.gasEstimator.GetPriceHistory(chain, duration)
}

// BackupKeystore 备份keystore
func (m *Manager) BackupKeystore(backupPath string) error {
	return m.keystoreManager.BackupKeystore(backupPath)
}

// RestoreKeystore 恢复keystore
func (m *Manager) RestoreKeystore(backupPath string) error {
	return m.keystoreManager.RestoreKeystore(backupPath)
}

// GetMonitorStatus 获取监控状态
func (m *Manager) GetMonitorStatus() *MonitorStatus {
	if m.monitor == nil {
		return nil
	}
	return m.monitor.GetStatus()
}

// GetClient 获取指定链的客户端
func (m *Manager) GetClient(chain string) (*ethclient.Client, error) {
	return m.clientManager.GetClient(chain)
}

// GetAccount 获取主账户
func (m *Manager) GetAccount(chain string) (*Account, error) {
	m.mu.RLock()
	address := m.primaryAddress
	m.mu.RUnlock()
	
	if address == (common.Address{}) {
		return nil, errors.New("no primary address set")
	}
	
	// 返回账户信息（不包含私钥）
	return &Account{
		Address:    address,
		privateKey: nil, // 私钥由 keystore 管理
	}, nil
}

// ERC20 返回 ERC20 管理器
func (m *Manager) ERC20() *ERC20Manager {
	return m.erc20Manager
}

// InvalidateBalanceCache invalidates balance cache for a specific address/token
func (m *Manager) InvalidateBalanceCache(chain, address, token string) {
	m.cache.InvalidateBalance(chain, address, token)
}

// InvalidateChainCache invalidates all cache entries for a specific chain
func (m *Manager) InvalidateChainCache(chain string) {
	m.cache.InvalidateChain(chain)
}

// GetCacheMetrics returns cache performance metrics
func (m *Manager) GetCacheMetrics() CacheMetrics {
	return m.cache.GetMetrics()
}

// WarmupBalanceCache pre-loads balance into cache
func (m *Manager) WarmupBalanceCache(ctx context.Context, chain string, address common.Address, tokens []string) error {
	for _, token := range tokens {
		err := m.cache.WarmupBalance(ctx, chain, address, token, func() (*Balance, error) {
			return m.balanceQuery.GetBalance(ctx, chain, address.Hex(), token)
		})
		if err != nil {
			logger.Warn().
				Str("chain", chain).
				Str("address", address.Hex()).
				Str("token", token).
				Err(err).
				Msg("Failed to warmup balance cache")
		}
	}
	return nil
}

// UpdateBalanceWithEvent updates balance in cache and publishes event if changed
func (m *Manager) UpdateBalanceWithEvent(ctx context.Context, chain, address, tokenAddress string, newBalance *Balance) {
	// Get old balance for comparison
	var oldAmount *big.Int
	if oldBal, found := m.cache.GetBalance(chain, address, tokenAddress); found {
		oldAmount = oldBal.Amount
	} else {
		oldAmount = big.NewInt(0)
	}
	
	// Update cache
	m.cache.SetBalance(chain, address, tokenAddress, newBalance)
	
	// Publish event if balance changed
	if m.eventBus != nil && oldAmount.Cmp(newBalance.Amount) != 0 {
		// Get token symbol
		tokenSymbol := ""
		if newBalance.Token.Symbol != "" {
			tokenSymbol = newBalance.Token.Symbol
		} else if tokenAddress == "" {
			// Native token
			switch chain {
			case "ethereum", "arbitrum", "optimism":
				tokenSymbol = "ETH"
			case "bsc":
				tokenSymbol = "BNB"
			case "polygon":
				tokenSymbol = "MATIC"
			case "avalanche":
				tokenSymbol = "AVAX"
			case "fantom":
				tokenSymbol = "FTM"
			case "metis":
				tokenSymbol = "METIS"
			default:
				tokenSymbol = "NATIVE"
			}
		}
		
		m.publishBalanceUpdate(ctx, chain, tokenSymbol, address, oldAmount, newBalance.Amount, "monitor_update")
	}
}

// publishBalanceUpdate publishes a balance update event
func (m *Manager) publishBalanceUpdate(ctx context.Context, chain, token, address string, oldBalance, newBalance *big.Int, reason string) {
	event := events.NewBalanceUpdatedEvent(chain, token, address)
	event.OldBalance = oldBalance
	event.NewBalance = newBalance
	event.Reason = reason
	
	if err := m.eventBus.Publish(ctx, event); err != nil {
		logger.Error().
			Err(err).
			Str("chain", chain).
			Str("token", token).
			Str("address", address).
			Msg("Failed to publish balance update event")
	} else {
		logger.Info().
			Str("chain", chain).
			Str("token", token).
			Str("address", address).
			Str("old_balance", oldBalance.String()).
			Str("new_balance", newBalance.String()).
			Str("reason", reason).
			Msg("Published balance update event")
	}
}
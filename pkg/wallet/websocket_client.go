package wallet

import (
	"context"
	"fmt"
	"sync"
	"time"

	"stargate/pkg/logger"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/ethclient"
	"github.com/ethereum/go-ethereum/rpc"
)

// WebSocketManager manages WebSocket connections for all chains
type WebSocketManager struct {
	clients   map[string]*WebSocketClient
	mu        sync.RWMutex
	manager   *Manager
	stopCh    chan struct{}
	wg        sync.WaitGroup
}

// WebSocketClient represents a WebSocket connection to a single chain
type WebSocketClient struct {
	chain          string
	wssURL         string
	rpcClient      *rpc.Client
	ethClient      *ethclient.Client
	manager        *Manager
	checkInterval  time.Duration
	reconnectDelay time.Duration
	subscriptions  map[string]*subscription
	mu             sync.RWMutex
	stopCh         chan struct{}
	connected      bool
}

type subscription struct {
	subTo    ethereum.Subscription // TO subscription (incoming transfers)
	subFrom  ethereum.Subscription // FROM subscription (outgoing transfers)
	address  common.Address
	token    string
	filterID string
}

// NewWebSocketManager creates a new WebSocket manager
func NewWebSocketManager(manager *Manager) *WebSocketManager {
	return &WebSocketManager{
		clients: make(map[string]*WebSocketClient),
		manager: manager,
		stopCh:  make(chan struct{}),
	}
}

// AddChain adds a WebSocket client for a chain if WebSocket URL is configured
func (wsm *WebSocketManager) AddChain(chain string, wssURL string, checkInterval time.Duration) error {
	if wssURL == "" {
		logger.Debug().Str("chain", chain).Msg("No WebSocket URL configured for chain")
		return nil
	}

	wsm.mu.Lock()
	defer wsm.mu.Unlock()

	if _, exists := wsm.clients[chain]; exists {
		return fmt.Errorf("WebSocket client already exists for chain %s", chain)
	}

	client := &WebSocketClient{
		chain:          chain,
		wssURL:         wssURL,
		manager:        wsm.manager,
		checkInterval:  checkInterval,
		reconnectDelay: 5 * time.Second,
		subscriptions:  make(map[string]*subscription),
		stopCh:         make(chan struct{}),
	}

	wsm.clients[chain] = client
	
	logger.Info().Str("chain", chain).Str("wss_url", wssURL).Msg("WebSocket client created for chain")
	return nil
}

// Start starts all WebSocket clients
func (wsm *WebSocketManager) Start() {
	wsm.mu.RLock()
	defer wsm.mu.RUnlock()
	
	for chain, client := range wsm.clients {
		wsm.wg.Add(1)
		go func(c string, cl *WebSocketClient) {
			defer wsm.wg.Done()
			logger.Info().Str("chain", c).Msg("Starting WebSocket client")
			cl.run()
		}(chain, client)
	}
	
	logger.Info().Int("clients", len(wsm.clients)).Msg("WebSocket manager started")
}

// Stop stops all WebSocket clients
func (wsm *WebSocketManager) Stop() {
	close(wsm.stopCh)
	
	wsm.mu.RLock()
	for _, client := range wsm.clients {
		client.stop()
	}
	wsm.mu.RUnlock()
	
	wsm.wg.Wait()
	logger.Info().Msg("WebSocket manager stopped")
}

// IsConnected checks if WebSocket is connected for a chain
func (wsm *WebSocketManager) IsConnected(chain string) bool {
	wsm.mu.RLock()
	defer wsm.mu.RUnlock()
	
	client, exists := wsm.clients[chain]
	if !exists {
		return false
	}
	
	return client.isConnected()
}

// run is the main loop for a WebSocket client
func (wsc *WebSocketClient) run() {
	for {
		select {
		case <-wsc.stopCh:
			wsc.disconnect()
			return
		default:
			if !wsc.isConnected() {
				if err := wsc.connect(); err != nil {
					logger.Error().Err(err).Str("chain", wsc.chain).Msg("Failed to connect WebSocket")
					time.Sleep(wsc.reconnectDelay)
					continue
				}
				
				// Subscribe to events only after successful connection
				wsc.subscribeToAddresses()
			}
			
			// Wait for reconnect or stop
			select {
			case <-wsc.stopCh:
				wsc.disconnect()
				return
			case <-time.After(wsc.checkInterval):
				// Periodic health check
				if !wsc.isHealthy() {
					logger.Warn().Str("chain", wsc.chain).Msg("WebSocket connection unhealthy, reconnecting")
					wsc.disconnect()
				}
			}
		}
	}
}

// connect establishes WebSocket connection
func (wsc *WebSocketClient) connect() error {
	wsc.mu.Lock()
	defer wsc.mu.Unlock()
	
	if wsc.connected {
		return nil
	}
	
	logger.Info().
		Str("chain", wsc.chain).
		Str("wss_url", wsc.wssURL).
		Msg("Attempting WebSocket connection")
	
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	rpcClient, err := rpc.DialContext(ctx, wsc.wssURL)
	if err != nil {
		return fmt.Errorf("failed to dial WebSocket: %w", err)
	}
	
	ethClient := ethclient.NewClient(rpcClient)
	
	// Test connection
	chainID, err := ethClient.ChainID(ctx)
	if err != nil {
		rpcClient.Close()
		return fmt.Errorf("failed to get chain ID: %w", err)
	}
	
	wsc.rpcClient = rpcClient
	wsc.ethClient = ethClient
	wsc.connected = true
	
	logger.Info().
		Str("chain", wsc.chain).
		Str("chain_id", chainID.String()).
		Msg("WebSocket connected successfully")
	return nil
}

// disconnect closes WebSocket connection
func (wsc *WebSocketClient) disconnect() {
	wsc.mu.Lock()
	defer wsc.mu.Unlock()
	
	if !wsc.connected {
		return
	}
	
	// Unsubscribe all
	for _, sub := range wsc.subscriptions {
		if sub.subTo != nil {
			sub.subTo.Unsubscribe()
		}
		if sub.subFrom != nil {
			sub.subFrom.Unsubscribe()
		}
	}
	wsc.subscriptions = make(map[string]*subscription)
	
	if wsc.rpcClient != nil {
		wsc.rpcClient.Close()
	}
	
	wsc.connected = false
	logger.Info().Str("chain", wsc.chain).Msg("WebSocket disconnected")
}

// isConnected checks if WebSocket is connected
func (wsc *WebSocketClient) isConnected() bool {
	wsc.mu.RLock()
	defer wsc.mu.RUnlock()
	return wsc.connected
}

// isHealthy checks if the connection is healthy
func (wsc *WebSocketClient) isHealthy() bool {
	wsc.mu.RLock()
	defer wsc.mu.RUnlock()
	
	// Check if we have valid connections
	if !wsc.connected || wsc.rpcClient == nil || wsc.ethClient == nil {
		return false
	}
	
	// The rpc.Client doesn't expose connection state directly,
	// so we just check if the clients are not nil
	// If the connection is actually broken, it will fail on next use
	// and trigger reconnection
	return true
}

// subscribeToAddresses subscribes to events for monitored addresses
func (wsc *WebSocketClient) subscribeToAddresses() {
	// Get monitored addresses from manager
	addresses := wsc.manager.GetMonitoredAddresses()
	
	if len(addresses) == 0 {
		logger.Debug().
			Str("chain", wsc.chain).
			Msg("No addresses to monitor")
		return
	}
	
	// Get monitored tokens for this chain
	tokens := wsc.manager.GetMonitoredTokens(wsc.chain)
	
	logger.Info().
		Str("chain", wsc.chain).
		Int("addresses", len(addresses)).
		Int("tokens", len(tokens)).
		Msg("Setting up event subscriptions")
	
	for _, addr := range addresses {
		// Skip ETH transfers monitoring, only monitor ERC20 tokens
		for _, token := range tokens {
			wsc.subscribeToAddress(addr, token)
		}
	}
}

// subscribeToAddress subscribes to Transfer events for an address
func (wsc *WebSocketClient) subscribeToAddress(address common.Address, tokenAddress string) bool {
	key := fmt.Sprintf("%s-%s", address.Hex(), tokenAddress)
	
	wsc.mu.Lock()
	defer wsc.mu.Unlock()
	
	// Check if already subscribed
	if _, exists := wsc.subscriptions[key]; exists {
		return false
	}
	
	// Create subscription
	sub := &subscription{
		address: address,
		token:   tokenAddress,
	}
	
	if tokenAddress == "" {
		// Skip ETH transfers monitoring
		return false
	}
	
	// Subscribe to ERC20 Transfer events
	go wsc.subscribeToTokenTransfers(sub, common.HexToAddress(tokenAddress))
	
	wsc.subscriptions[key] = sub
	return true
}

// subscribeToETHTransfers subscribes to ETH balance changes
// NOTE: Commented out as we don't need to monitor native ETH balance for Stargate arbitrage
/*
func (wsc *WebSocketClient) subscribeToETHTransfers(sub *subscription) {
	headers := make(chan *types.Header)
	
	ctx := context.Background()
	subscription, err := wsc.ethClient.SubscribeNewHead(ctx, headers)
	if err != nil {
		logger.Error().Err(err).Str("chain", wsc.chain).Msg("Failed to subscribe to new blocks")
		return
	}
	
	sub.sub = subscription
	logger.Info().
		Str("chain", wsc.chain).
		Str("address", sub.address.Hex()).
		Msg("Subscribed to ETH balance updates via new blocks")
	
	for {
		select {
		case err := <-subscription.Err():
			if err != nil {
				logger.Error().Err(err).Str("chain", wsc.chain).Msg("ETH subscription error")
			}
			return
		case header := <-headers:
			// Check balance on new block
			logger.Debug().
				Str("chain", wsc.chain).
				Str("block", header.Number.String()).
				Msg("New block received, checking ETH balance")
			wsc.checkETHBalance(sub.address, header.Number)
		case <-wsc.stopCh:
			subscription.Unsubscribe()
			return
		}
	}
}
*/

// subscribeToTokenTransfers subscribes to ERC20 Transfer events
func (wsc *WebSocketClient) subscribeToTokenTransfers(sub *subscription, tokenAddress common.Address) {
	// Try to get token symbol from cache first
	tokenSymbol := tokenAddress.Hex()
	cacheKey := fmt.Sprintf("%s:%s", wsc.chain, tokenAddress.Hex())
	if cached, ok := wsc.manager.balanceQuery.tokenCache.Load(cacheKey); ok {
		tokenInfo := cached.(TokenInfo)
		if tokenInfo.Symbol != "" {
			tokenSymbol = tokenInfo.Symbol
		}
	}
	// Create filter for Transfer events TO our address
	queryTo := ethereum.FilterQuery{
		Addresses: []common.Address{tokenAddress},
		Topics: [][]common.Hash{
			{common.HexToHash("0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef")}, // Transfer event signature
			{}, // from any address
			{common.BytesToHash(sub.address.Bytes())}, // TO our address
		},
	}
	
	logs := make(chan types.Log)
	
	ctx := context.Background()
	subscriptionTo, err := wsc.ethClient.SubscribeFilterLogs(ctx, queryTo, logs)
	if err != nil {
		logger.Error().
			Err(err).
			Str("chain", wsc.chain).
			Str("address", sub.address.Hex()).
			Str("token", tokenSymbol).
			Msg("Failed to subscribe to Transfer TO events")
		return
	}
	sub.subTo = subscriptionTo
	
	// Also subscribe to transfers FROM our address
	queryFrom := ethereum.FilterQuery{
		Addresses: []common.Address{tokenAddress},
		Topics: [][]common.Hash{
			{common.HexToHash("0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef")}, // Transfer event signature
			{common.BytesToHash(sub.address.Bytes())}, // FROM our address
			{}, // to any address
		},
	}
	
	var subscriptionFrom ethereum.Subscription
	subscriptionFrom, err = wsc.ethClient.SubscribeFilterLogs(ctx, queryFrom, logs)
	if err != nil {
		logger.Error().
			Err(err).
			Str("chain", wsc.chain).
			Str("address", sub.address.Hex()).
			Str("token", tokenSymbol).
			Msg("Failed to subscribe to Transfer FROM events")
		sub.subFrom = nil
	} else {
		sub.subFrom = subscriptionFrom
	}

	logger.Info().
		Str("chain", wsc.chain).
		Str("address", sub.address.Hex()).
		Str("token", tokenSymbol).
		Msg("Subscribed to ERC20 Transfer events")
	
	for {
		select {
		case err := <-subscriptionTo.Err():
			if err != nil {
				logger.Error().Err(err).Str("chain", wsc.chain).Msg("Token TO subscription error")
			}
			return
		case err := <-func() <-chan error {
			if subscriptionFrom != nil {
				return subscriptionFrom.Err()
			}
			// Return a channel that never sends
			return make(<-chan error)
		}():
			if err != nil {
				logger.Error().Err(err).Str("chain", wsc.chain).Msg("Token FROM subscription error")
			}
			return
		case vLog := <-logs:
			// Process Transfer event
			wsc.processTransferEvent(sub.address, tokenAddress, tokenSymbol, vLog)
		case <-wsc.stopCh:
			subscriptionTo.Unsubscribe()
			if subscriptionFrom != nil {
				subscriptionFrom.Unsubscribe()
			}
			return
		}
	}
}

// checkETHBalance checks and updates ETH balance
// NOTE: Commented out as we don't need to monitor native ETH balance for Stargate arbitrage
/*
func (wsc *WebSocketClient) checkETHBalance(address common.Address, blockNumber *big.Int) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	
	balance, err := wsc.ethClient.BalanceAt(ctx, address, blockNumber)
	if err != nil {
		logger.Error().Err(err).Str("chain", wsc.chain).Str("address", address.Hex()).Msg("Failed to get ETH balance")
		return
	}
	
	// Create native token info for ETH
	tokenInfo := TokenInfo{
		Address:  common.Address{}, // Zero address for native token
		Symbol:   "ETH",
		Name:     "Ether",
		Decimals: 18,
		IsNative: true,
	}
	
	// Update cache and publish event
	wsc.manager.UpdateBalanceWithEvent(context.Background(), wsc.chain, address.Hex(), "", &Balance{
		Token:    tokenInfo,
		Amount:   balance,
		Chain:    wsc.chain,
		UpdateAt: time.Now(),
	})
	
	logger.Debug().
		Str("chain", wsc.chain).
		Str("address", address.Hex()).
		Str("balance", balance.String()).
		Msg("ETH balance updated via WebSocket")
}
*/

// processTransferEvent processes a Transfer event
func (wsc *WebSocketClient) processTransferEvent(address common.Address, tokenAddress common.Address, tokenSymbol string, vLog types.Log) {
	logger.Info().
		Str("chain", wsc.chain).
		Str("address", address.Hex()).
		Str("token", tokenSymbol).
		Str("tx", vLog.TxHash.Hex()).
		Str("block", fmt.Sprintf("%d", vLog.BlockNumber)).
		Msg("Transfer event detected via WebSocket")
	
	// Process balance update asynchronously to avoid blocking event processing
	go func() {
		// Get token balance
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()
		
		balance, err := wsc.manager.balanceQuery.GetBalance(ctx, wsc.chain, address.Hex(), tokenAddress.Hex())
		if err != nil {
			logger.Error().Err(err).
				Str("chain", wsc.chain).
				Str("address", address.Hex()).
				Str("token", tokenSymbol).
				Msg("Failed to get token balance after Transfer event")
			return
		}
		
		// Update cache and publish event
		wsc.manager.UpdateBalanceWithEvent(context.Background(), wsc.chain, address.Hex(), tokenAddress.Hex(), balance)
				
		logger.Info().
			Str("chain", wsc.chain).
			Str("address", address.Hex()).
			Str("token", tokenSymbol).
			Str("balance", balance.Amount.String()).
			Str("tx", vLog.TxHash.Hex()).
			Msg("Token balance updated via WebSocket Transfer event")
	}()
}

// stop stops the WebSocket client
func (wsc *WebSocketClient) stop() {
	close(wsc.stopCh)
}

// GetMonitoredAddresses returns addresses being monitored
func (m *Manager) GetMonitoredAddresses() []common.Address {
	addresses := make([]common.Address, 0)
	
	// Get primary address
	primaryAddr := m.GetPrimaryAddress()
	if primaryAddr != (common.Address{}) {
		addresses = append(addresses, primaryAddr)
	}
	
	// Could also get addresses from keystore if needed
	// accounts := m.keystoreManager.GetAccounts()
	// addresses = append(addresses, accounts...)
	
	return addresses
}

// GetMonitoredTokens returns tokens being monitored for a chain
func (m *Manager) GetMonitoredTokens(chain string) []string {
	m.mu.RLock()
	defer m.mu.RUnlock()
	
	tokens := make([]string, 0)
	
	// Get tokens from chain configuration
	if chainConfig, exists := m.chainConfigs[chain]; exists {
		for _, tokenInfo := range chainConfig.Tokens {
			if tokenInfo.Address != "" {
				tokens = append(tokens, tokenInfo.Address)
			}
		}
	}
	
	return tokens
}
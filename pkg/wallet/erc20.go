package wallet

import (
	"context"
	"errors"
	"fmt"
	"math/big"
	"sync"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/ethclient"
	"stargate/pkg/logger"
	"stargate/pkg/wallet/contracts"
)

// ERC20Manager 管理 ERC20 代币操作
type ERC20Manager struct {
	manager     *Manager
	nonceMutex  sync.Mutex
	nonceMap    map[string]uint64 // chain:address -> nonce
}

// NewERC20Manager 创建 ERC20 管理器
func NewERC20Manager(manager *Manager) *ERC20Manager {
	return &ERC20Manager{
		manager:  manager,
		nonceMap: make(map[string]uint64),
	}
}

// Approve 授权 spender 使用代币
func (em *ERC20Manager) Approve(ctx context.Context, chain, tokenAddress, spender string, amount *big.Int) (*types.Transaction, error) {
	logger.Info().
		Str("chain", chain).
		Str("token", tokenAddress).
		Str("spender", spender).
		Str("amount", amount.String()).
		Msg("Approving token allowance")

	// 获取客户端
	client, err := em.manager.GetClient(chain)
	if err != nil {
		return nil, fmt.Errorf("failed to get client: %w", err)
	}

	// 获取账户
	account, err := em.manager.GetAccount(chain)
	if err != nil {
		return nil, fmt.Errorf("failed to get account: %w", err)
	}

	// 解析代币地址
	tokenAddr := common.HexToAddress(tokenAddress)
	spenderAddr := common.HexToAddress(spender)

	// 创建 ERC20 合约实例
	token, err := contracts.NewERC20(tokenAddr, client)
	if err != nil {
		return nil, fmt.Errorf("failed to create token contract: %w", err)
	}

	// 创建交易选项
	opts, err := em.createTransactOpts(ctx, chain, account)
	if err != nil {
		return nil, fmt.Errorf("failed to create transaction options: %w", err)
	}

	// 发送 approve 交易
	tx, err := token.Approve(opts, spenderAddr, amount)
	if err != nil {
		return nil, fmt.Errorf("failed to approve: %w", err)
	}

	logger.Info().
		Str("tx_hash", tx.Hash().Hex()).
		Str("chain", chain).
		Str("token", tokenAddress).
		Msg("Approve transaction sent")

	return tx, nil
}

// Transfer 转账代币
func (em *ERC20Manager) Transfer(ctx context.Context, chain, tokenAddress, to string, amount *big.Int) (*types.Transaction, error) {
	logger.Info().
		Str("chain", chain).
		Str("token", tokenAddress).
		Str("to", to).
		Str("amount", amount.String()).
		Msg("Transferring tokens")

	// 获取客户端
	client, err := em.manager.GetClient(chain)
	if err != nil {
		return nil, fmt.Errorf("failed to get client: %w", err)
	}

	// 获取账户
	account, err := em.manager.GetAccount(chain)
	if err != nil {
		return nil, fmt.Errorf("failed to get account: %w", err)
	}

	// 解析地址
	tokenAddr := common.HexToAddress(tokenAddress)
	toAddr := common.HexToAddress(to)

	// 创建 ERC20 合约实例
	token, err := contracts.NewERC20(tokenAddr, client)
	if err != nil {
		return nil, fmt.Errorf("failed to create token contract: %w", err)
	}

	// 创建交易选项
	opts, err := em.createTransactOpts(ctx, chain, account)
	if err != nil {
		return nil, fmt.Errorf("failed to create transaction options: %w", err)
	}

	// 发送 transfer 交易
	tx, err := token.Transfer(opts, toAddr, amount)
	if err != nil {
		return nil, fmt.Errorf("failed to transfer: %w", err)
	}

	logger.Info().
		Str("tx_hash", tx.Hash().Hex()).
		Str("chain", chain).
		Str("token", tokenAddress).
		Msg("Transfer transaction sent")

	return tx, nil
}

// Allowance 查询授权额度
func (em *ERC20Manager) Allowance(ctx context.Context, chain, tokenAddress, owner, spender string) (*big.Int, error) {
	// 获取客户端
	client, err := em.manager.GetClient(chain)
	if err != nil {
		return nil, fmt.Errorf("failed to get client: %w", err)
	}

	// 解析地址
	tokenAddr := common.HexToAddress(tokenAddress)
	ownerAddr := common.HexToAddress(owner)
	spenderAddr := common.HexToAddress(spender)

	// 创建 ERC20 合约实例
	token, err := contracts.NewERC20(tokenAddr, client)
	if err != nil {
		return nil, fmt.Errorf("failed to create token contract: %w", err)
	}

	// 查询 allowance
	allowance, err := token.Allowance(&bind.CallOpts{Context: ctx}, ownerAddr, spenderAddr)
	if err != nil {
		return nil, fmt.Errorf("failed to get allowance: %w", err)
	}

	return allowance, nil
}

// BalanceOf 查询代币余额
func (em *ERC20Manager) BalanceOf(ctx context.Context, chain, tokenAddress, account string) (*big.Int, error) {
	// 获取客户端
	client, err := em.manager.GetClient(chain)
	if err != nil {
		return nil, fmt.Errorf("failed to get client: %w", err)
	}

	// 解析地址
	tokenAddr := common.HexToAddress(tokenAddress)
	accountAddr := common.HexToAddress(account)

	// 创建 ERC20 合约实例
	token, err := contracts.NewERC20(tokenAddr, client)
	if err != nil {
		return nil, fmt.Errorf("failed to create token contract: %w", err)
	}

	// 查询余额
	balance, err := token.BalanceOf(&bind.CallOpts{Context: ctx}, accountAddr)
	if err != nil {
		return nil, fmt.Errorf("failed to get balance: %w", err)
	}

	return balance, nil
}

// WaitForTransaction 等待交易确认
func (em *ERC20Manager) WaitForTransaction(ctx context.Context, chain string, tx *types.Transaction) (*types.Receipt, error) {
	client, err := em.manager.GetClient(chain)
	if err != nil {
		return nil, fmt.Errorf("failed to get client: %w", err)
	}

	logger.Info().
		Str("tx_hash", tx.Hash().Hex()).
		Str("chain", chain).
		Msg("Waiting for transaction confirmation")

	// 等待交易被打包
	receipt, err := bind.WaitMined(ctx, client, tx)
	if err != nil {
		return nil, fmt.Errorf("failed to wait for transaction: %w", err)
	}

	if receipt.Status != types.ReceiptStatusSuccessful {
		return receipt, errors.New("transaction failed")
	}

	logger.Info().
		Str("tx_hash", tx.Hash().Hex()).
		Str("chain", chain).
		Uint64("block", receipt.BlockNumber.Uint64()).
		Uint64("gas_used", receipt.GasUsed).
		Msg("Transaction confirmed")

	return receipt, nil
}

// createTransactOpts 创建交易选项，包含 nonce 管理
func (em *ERC20Manager) createTransactOpts(ctx context.Context, chain string, account *Account) (*bind.TransactOpts, error) {
	client, err := em.manager.GetClient(chain)
	if err != nil {
		return nil, err
	}

	// 获取链 ID
	chainID, err := client.ChainID(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get chain ID: %w", err)
	}

	// 检查账户是否解锁
	if !em.manager.keystoreManager.IsUnlocked(account.Address) {
		return nil, fmt.Errorf("account %s is locked", account.Address.Hex())
	}

	// 使用 keystore 管理器获取 transactor
	acct, err := em.manager.keystoreManager.GetAccount(account.Address)
	if err != nil {
		return nil, fmt.Errorf("failed to get account: %w", err)
	}

	// 创建交易选项
	opts, err := bind.NewKeyStoreTransactorWithChainID(em.manager.keystoreManager.GetKeystore(), *acct, chainID)
	if err != nil {
		return nil, fmt.Errorf("failed to create transactor: %w", err)
	}

	// 设置 gas 价格
	gasPrice, err := client.SuggestGasPrice(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to suggest gas price: %w", err)
	}
	opts.GasPrice = gasPrice

	// 设置 nonce（使用 nonce 管理）
	nonce, err := em.getNextNonce(ctx, chain, account.Address.Hex(), client)
	if err != nil {
		return nil, fmt.Errorf("failed to get nonce: %w", err)
	}
	opts.Nonce = big.NewInt(int64(nonce))

	// 设置 context
	opts.Context = ctx

	return opts, nil
}

// getNextNonce 获取下一个可用的 nonce
func (em *ERC20Manager) getNextNonce(ctx context.Context, chain, address string, client *ethclient.Client) (uint64, error) {
	em.nonceMutex.Lock()
	defer em.nonceMutex.Unlock()

	key := fmt.Sprintf("%s:%s", chain, address)
	
	// 获取链上的 pending nonce
	addr := common.HexToAddress(address)
	pendingNonce, err := client.PendingNonceAt(ctx, addr)
	if err != nil {
		return 0, fmt.Errorf("failed to get pending nonce: %w", err)
	}

	// 如果本地没有记录，或者链上 nonce 更大，使用链上的
	localNonce, exists := em.nonceMap[key]
	if !exists || pendingNonce > localNonce {
		em.nonceMap[key] = pendingNonce
		return pendingNonce, nil
	}

	// 使用本地 nonce 并递增
	nonce := em.nonceMap[key]
	em.nonceMap[key] = nonce + 1
	return nonce, nil
}

// ResetNonce 重置 nonce（用于交易失败等情况）
func (em *ERC20Manager) ResetNonce(chain, address string) {
	em.nonceMutex.Lock()
	defer em.nonceMutex.Unlock()

	key := fmt.Sprintf("%s:%s", chain, address)
	delete(em.nonceMap, key)
	
	logger.Info().
		Str("chain", chain).
		Str("address", address).
		Msg("Nonce reset")
}

// GetTokenInfo 获取代币信息
func (em *ERC20Manager) GetTokenInfo(ctx context.Context, chain, tokenAddress string) (*TokenInfo, error) {
	client, err := em.manager.GetClient(chain)
	if err != nil {
		return nil, fmt.Errorf("failed to get client: %w", err)
	}

	tokenAddr := common.HexToAddress(tokenAddress)
	token, err := contracts.NewERC20(tokenAddr, client)
	if err != nil {
		return nil, fmt.Errorf("failed to create token contract: %w", err)
	}

	callOpts := &bind.CallOpts{Context: ctx}

	// 获取代币信息
	name, _ := token.Name(callOpts)
	symbol, _ := token.Symbol(callOpts)
	decimals, _ := token.Decimals(callOpts)

	return &TokenInfo{
		Address:  tokenAddr,
		Name:     name,
		Symbol:   symbol,
		Decimals: decimals,
		IsNative: false,
	}, nil
}

// EstimateGas 估算 gas 费用
func (em *ERC20Manager) EstimateGas(ctx context.Context, chain string, from, to common.Address, data []byte) (uint64, error) {
	client, err := em.manager.GetClient(chain)
	if err != nil {
		return 0, fmt.Errorf("failed to get client: %w", err)
	}

	msg := ethereum.CallMsg{
		From:  from,
		To:    &to,
		Data:  data,
	}

	gasLimit, err := client.EstimateGas(ctx, msg)
	if err != nil {
		return 0, fmt.Errorf("failed to estimate gas: %w", err)
	}

	// 添加 10% 的缓冲
	return gasLimit * 110 / 100, nil
}
package wallet

import (
	"context"
	"errors"
	"fmt"
	"math/big"
	"sync"
	"time"

	"stargate/pkg/logger"

	"github.com/ethereum/go-ethereum/ethclient"
)

// ChainConfig 链配置
type ChainConfig struct {
	Name    string `json:"name"`
	ChainID int64  `json:"chain_id"`
	RPC     string `json:"rpc"`
	// 可选配置
	MaxConnections int           `json:"max_connections,omitempty"`
	Timeout        time.Duration `json:"timeout,omitempty"`
}

// ClientPool 客户端连接池
type ClientPool struct {
	clients      []*ethclient.Client
	healthy      []bool
	currentIndex int
	mu           sync.RWMutex
}

// ClientManager 多链客户端管理器
type ClientManager struct {
	chains      map[string]*ChainConfig
	clientPools map[string]*ClientPool
	mu          sync.RWMutex
	
	// 健康检查
	healthCheckInterval time.Duration
	stopHealthCheck     chan struct{}
	wg                  sync.WaitGroup
}

// NewClientManager 创建新的客户端管理器
func NewClientManager(healthCheckInterval time.Duration) *ClientManager {
	if healthCheckInterval == 0 {
		healthCheckInterval = 30 * time.Second
	}

	return &ClientManager{
		chains:              make(map[string]*ChainConfig),
		clientPools:         make(map[string]*ClientPool),
		healthCheckInterval: healthCheckInterval,
		stopHealthCheck:     make(chan struct{}),
	}
}

// AddChain 添加新链
func (cm *ClientManager) AddChain(config *ChainConfig) error {
	if config == nil {
		return errors.New("chain config is nil")
	}
	if config.Name == "" {
		return errors.New("chain name is empty")
	}
	if config.RPC == "" {
		return errors.New("RPC URL is empty")
	}
	if config.ChainID <= 0 {
		return errors.New("invalid chain ID")
	}

	// 设置默认值
	if config.MaxConnections <= 0 {
		config.MaxConnections = 3
	}
	if config.Timeout == 0 {
		config.Timeout = 10 * time.Second
	}

	// 先检查是否需要关闭旧连接
	cm.mu.Lock()
	var oldPool *ClientPool
	if _, exists := cm.chains[config.Name]; exists {
		if pool, ok := cm.clientPools[config.Name]; ok {
			oldPool = pool
		}
	}
	cm.mu.Unlock()

	// 在锁外关闭旧连接
	if oldPool != nil {
		oldPool.Close()
	}

	// 在锁外创建连接池（耗时操作）
	pool, err := cm.createClientPool(config)
	if err != nil {
		return fmt.Errorf("failed to create client pool: %w", err)
	}

	// 只在更新map时持锁
	cm.mu.Lock()
	cm.chains[config.Name] = config
	cm.clientPools[config.Name] = pool
	cm.mu.Unlock()

	logger.Debug().
		Str("chain", config.Name).
		Int64("chain_id", config.ChainID).
		Int("connections", config.MaxConnections).
		Msg("Chain added to client manager")

	return nil
}

// RemoveChain 移除链
func (cm *ClientManager) RemoveChain(chainName string) error {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	pool, exists := cm.clientPools[chainName]
	if !exists {
		return fmt.Errorf("chain %s not found", chainName)
	}

	// 关闭所有连接
	pool.Close()

	delete(cm.clientPools, chainName)
	delete(cm.chains, chainName)

	logger.Info().
		Str("chain", chainName).
		Msg("Chain removed from client manager")

	return nil
}

// GetClient 获取健康的客户端
func (cm *ClientManager) GetClient(chainName string) (*ethclient.Client, error) {
	cm.mu.RLock()
	pool, exists := cm.clientPools[chainName]
	cm.mu.RUnlock()

	if !exists {
		return nil, fmt.Errorf("chain %s not found", chainName)
	}

	return pool.GetHealthyClient()
}

// GetChainID 获取链ID
func (cm *ClientManager) GetChainID(chainName string) (*big.Int, error) {
	cm.mu.RLock()
	config, exists := cm.chains[chainName]
	cm.mu.RUnlock()

	if !exists {
		return nil, fmt.Errorf("chain %s not found", chainName)
	}

	return big.NewInt(config.ChainID), nil
}

// Start 启动客户端管理器
func (cm *ClientManager) Start(ctx context.Context) {
	cm.wg.Add(1)
	go cm.healthCheckLoop(ctx)
}

// Stop 停止客户端管理器
func (cm *ClientManager) Stop() {
	// 防止重复关闭
	select {
	case <-cm.stopHealthCheck:
		return // 已经关闭
	default:
		close(cm.stopHealthCheck)
	}
	
	cm.wg.Wait()

	// 关闭所有连接池
	cm.mu.Lock()
	defer cm.mu.Unlock()

	for _, pool := range cm.clientPools {
		pool.Close()
	}
	logger.Warn().Msg("Client manager stopped")
}

// createClientPool 创建客户端连接池
func (cm *ClientManager) createClientPool(config *ChainConfig) (*ClientPool, error) {
	pool := &ClientPool{
		clients: make([]*ethclient.Client, 0, config.MaxConnections),
		healthy: make([]bool, 0, config.MaxConnections),
	}

	// 设置超时时间
	timeout := config.Timeout
	if timeout == 0 {
		timeout = 10 * time.Second
	}

	// 创建多个连接
	for i := 0; i < config.MaxConnections; i++ {
		// 使用带超时的context
		dialCtx, dialCancel := context.WithTimeout(context.Background(), timeout)
		client, err := ethclient.DialContext(dialCtx, config.RPC)
		dialCancel()
		
		if err != nil {
			// 至少需要一个连接成功
			if i == 0 {
				return nil, fmt.Errorf("failed to connect to RPC: %w", err)
			}
			logger.Warn().
				Str("chain", config.Name).
				Int("connection", i).
				Err(err).
				Msg("Failed to create connection")
			continue
		}

		// 验证链ID
		verifyCtx, verifyCancel := context.WithTimeout(context.Background(), timeout)
		chainID, err := client.ChainID(verifyCtx)
		verifyCancel()

		if err != nil {
			client.Close()
			if i == 0 {
				return nil, fmt.Errorf("failed to get chain ID: %w", err)
			}
			continue
		}

		if chainID.Int64() != config.ChainID {
			client.Close()
			if i == 0 {
				return nil, fmt.Errorf("chain ID mismatch: expected %d, got %d", config.ChainID, chainID.Int64())
			}
			continue
		}

		pool.clients = append(pool.clients, client)
		pool.healthy = append(pool.healthy, true)
	}

	if len(pool.clients) == 0 {
		return nil, errors.New("failed to create any connections")
	}

	return pool, nil
}

// healthCheckLoop 健康检查循环
func (cm *ClientManager) healthCheckLoop(ctx context.Context) {
	defer cm.wg.Done()

	ticker := time.NewTicker(cm.healthCheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-cm.stopHealthCheck:
			return
		case <-ticker.C:
			cm.performHealthCheck()
		}
	}
}

// performHealthCheck 执行健康检查
func (cm *ClientManager) performHealthCheck() {
	cm.mu.RLock()
	pools := make(map[string]*ClientPool)
	configs := make(map[string]*ChainConfig)
	for name, pool := range cm.clientPools {
		pools[name] = pool
		configs[name] = cm.chains[name]
	}
	cm.mu.RUnlock()

	for name, pool := range pools {
		config := configs[name]
		pool.HealthCheck(config.Timeout)
	}
}

// GetHealthyClient 获取健康的客户端（负载均衡）
func (p *ClientPool) GetHealthyClient() (*ethclient.Client, error) {
	p.mu.RLock()
	defer p.mu.RUnlock()

	if len(p.clients) == 0 {
		return nil, errors.New("no clients in pool")
	}

	// 寻找健康的客户端
	attempts := len(p.clients)
	for i := 0; i < attempts; i++ {
		index := (p.currentIndex + i) % len(p.clients)
		if p.healthy[index] {
			// 更新索引用于负载均衡
			p.currentIndex = (index + 1) % len(p.clients)
			return p.clients[index], nil
		}
	}

	return nil, errors.New("no healthy clients available")
}

// HealthCheck 检查连接池中所有客户端的健康状态
func (p *ClientPool) HealthCheck(timeout time.Duration) {
	p.mu.Lock()
	defer p.mu.Unlock()

	for i, client := range p.clients {
		ctx, cancel := context.WithTimeout(context.Background(), timeout)
		_, err := client.BlockNumber(ctx)
		cancel()

		oldHealth := p.healthy[i]
		p.healthy[i] = err == nil

		// 记录健康状态变化
		if oldHealth != p.healthy[i] {
			if p.healthy[i] {
				logger.Info().
					Int("connection", i).
					Msg("Connection recovered")
			} else {
				logger.Warn().
					Int("connection", i).
					Err(err).
					Msg("Connection unhealthy")
			}
		}
	}
}

// Close 关闭连接池
func (p *ClientPool) Close() {
	p.mu.Lock()
	defer p.mu.Unlock()

	for _, client := range p.clients {
		if client != nil {
			client.Close()
		}
	}
	p.clients = nil
	p.healthy = nil
}

// GetAllChains 获取所有链配置
func (cm *ClientManager) GetAllChains() map[string]*ChainConfig {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	chains := make(map[string]*ChainConfig)
	for name, config := range cm.chains {
		// 深拷贝配置
		chainCopy := *config
		chains[name] = &chainCopy
	}
	return chains
}

// GetConnectionStats 获取连接统计信息
func (cm *ClientManager) GetConnectionStats() map[string]ConnectionStats {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	stats := make(map[string]ConnectionStats)
	for name, pool := range cm.clientPools {
		stats[name] = pool.GetStats()
	}
	return stats
}

// ConnectionStats 连接统计信息
type ConnectionStats struct {
	TotalConnections   int
	HealthyConnections int
	UnhealthyConnections int
}

// GetStats 获取连接池统计信息
func (p *ClientPool) GetStats() ConnectionStats {
	p.mu.RLock()
	defer p.mu.RUnlock()

	stats := ConnectionStats{
		TotalConnections: len(p.clients),
	}

	for _, healthy := range p.healthy {
		if healthy {
			stats.HealthyConnections++
		} else {
			stats.UnhealthyConnections++
		}
	}

	return stats
}
package wallet

import (
	"context"
	"math/big"
	"sync"
	"time"

	"stargate/internal/config"
	"stargate/pkg/logger"

	"github.com/ethereum/go-ethereum/common"
)

// WalletMonitorV2 钱包监控器V2版本，支持每条链独立的检查间隔
type WalletMonitorV2 struct {
	manager         *Manager
	chainConfigs    map[string]*config.ChainConfig
	
	// 监控数据
	alerts          []BalanceAlert
	transactions    []TransactionEvent
	
	// 统计数据
	txCount         map[string]int      // chain -> count
	gasUsage        map[string]*big.Int // chain -> total gas used
	successCount    int
	failureCount    int
	
	// 阈值配置
	lowBalanceThresholds map[string]*big.Int // chain -> threshold for native token
	
	// 每条链的监控器
	chainMonitors   map[string]*chainMonitor
	
	mu              sync.RWMutex
	running         bool
	lastCheckTime   time.Time
	stopCh          chan struct{}
	wg              sync.WaitGroup
}

// chainMonitor 单链监控器
type chainMonitor struct {
	chain         string
	checkInterval time.Duration
	monitor       *WalletMonitorV2
	stopCh        chan struct{}
	lastCheck     time.Time
	mu            sync.RWMutex
}

// NewWalletMonitorV2 创建钱包监控器V2
func NewWalletMonitorV2(manager *Manager, chainConfigs map[string]*config.ChainConfig) *WalletMonitorV2 {
	wm := &WalletMonitorV2{
		manager:       manager,
		chainConfigs:  chainConfigs,
		alerts:        make([]BalanceAlert, 0),
		transactions:  make([]TransactionEvent, 0),
		txCount:       make(map[string]int),
		gasUsage:      make(map[string]*big.Int),
		lowBalanceThresholds: getDefaultLowBalanceThresholds(),
		chainMonitors: make(map[string]*chainMonitor),
		stopCh:        make(chan struct{}),
	}

	// 为每条链创建独立的监控器
	for chainName, chainCfg := range chainConfigs {
		interval := chainCfg.CheckInterval
		if interval == 0 {
			interval = 30 * time.Second // 默认值
		}
		
		cm := &chainMonitor{
			chain:         chainName,
			checkInterval: interval,
			monitor:       wm,
			stopCh:        make(chan struct{}),
		}
		wm.chainMonitors[chainName] = cm
	}

	return wm
}

// Start 启动监控
func (wm *WalletMonitorV2) Start(ctx context.Context) {
	wm.mu.Lock()
	if wm.running {
		wm.mu.Unlock()
		return
	}
	wm.running = true
	wm.mu.Unlock()

	// 启动 WebSocket 管理器（如果存在）
	if wm.manager.wsManager != nil {
		wm.manager.wsManager.Start()
	}

	logger.Info().
		Int("chains", len(wm.chainMonitors)).
		Msg("Wallet monitor V2 started")

	// 启动每条链的监控器
	for chainName, cm := range wm.chainMonitors {
		wm.wg.Add(1)
		go func(name string, monitor *chainMonitor) {
			defer wm.wg.Done()
			monitor.run(ctx)
		}(chainName, cm)
		
		logger.Debug().
			Str("chain", chainName).
			Dur("interval", cm.checkInterval).
			Msg("Chain monitor started")
	}
}

// Stop 停止监控
func (wm *WalletMonitorV2) Stop() {
	wm.mu.Lock()
	if !wm.running {
		wm.mu.Unlock()
		return
	}
	wm.running = false
	wm.mu.Unlock()

	// 关闭所有链监控器
	close(wm.stopCh)
	for _, cm := range wm.chainMonitors {
		close(cm.stopCh)
	}
	
	// 等待所有监控器停止
	wm.wg.Wait()

	logger.Info().Msg("Wallet monitor V2 stopped")
}

// run 单链监控循环
func (cm *chainMonitor) run(ctx context.Context) {
	// 立即执行一次检查
	cm.performCheck(ctx)
	
	// 创建定时器
	ticker := time.NewTicker(cm.checkInterval)
	defer ticker.Stop()
	
	// WebSocket 降级轮询的时间间隔（当 WebSocket 活跃时，降低 HTTP 轮询频率）
	wsActiveCheckInterval := cm.checkInterval * 6 // 例如：5s -> 30s
	lastWSCheck := time.Now()

	for {
		select {
		case <-ctx.Done():
			return
		case <-cm.monitor.stopCh:
			return
		case <-cm.stopCh:
			return
		case <-ticker.C:
			// 检查 WebSocket 连接状态
			wsConnected := false
			if cm.monitor.manager.wsManager != nil {
				wsConnected = cm.monitor.manager.wsManager.IsConnected(cm.chain)
			}
			
			if wsConnected {
				// WebSocket 连接活跃时的逻辑
				now := time.Now()
				if now.Sub(lastWSCheck) >= wsActiveCheckInterval {
					// 即使 WebSocket 活跃，也定期进行一次完整检查
					logger.Debug().
						Str("chain", cm.chain).
						Dur("interval", wsActiveCheckInterval).
						Msg("Performing periodic check while WebSocket is active")
					cm.performCheck(ctx)
					lastWSCheck = now
				} else {
					// 跳过这次轮询
					logger.Debug().
						Str("chain", cm.chain).
						Dur("until_next_check", wsActiveCheckInterval-now.Sub(lastWSCheck)).
						Msg("Skipping HTTP poll, WebSocket is active")
				}
			} else {
				// WebSocket 未连接，使用正常的 HTTP 轮询间隔
				logger.Debug().
					Str("chain", cm.chain).
					Msg("WebSocket not connected, performing HTTP poll")
				cm.performCheck(ctx)
				lastWSCheck = time.Now()
			}
		}
	}
}

// performCheck 执行单链检查
func (cm *chainMonitor) performCheck(ctx context.Context) {
	cm.mu.Lock()
	cm.lastCheck = time.Now()
	cm.mu.Unlock()

	// 获取主地址
	primaryAddr := cm.monitor.manager.GetPrimaryAddress()
	if primaryAddr == (common.Address{}) {
		logger.Warn().Msg("No primary address set, skipping monitor check")
		return
	}

	// 检查原生币余额
	cm.monitor.checkBalance(ctx, cm.chain, primaryAddr, "")
	
	// 检查配置的代币余额
	tokens := cm.monitor.manager.GetMonitoredTokens(cm.chain)
	for _, token := range tokens {
		cm.monitor.checkBalance(ctx, cm.chain, primaryAddr, token)
	}

	logger.Debug().
		Str("chain", cm.chain).
		Time("last_check", cm.lastCheck).
		Msg("Chain monitor check completed")
}

// checkBalance 检查余额
func (wm *WalletMonitorV2) checkBalance(ctx context.Context, chain string, address common.Address, tokenAddress string) {
	// 直接从链上查询最新余额，不使用缓存
	balance, err := wm.manager.balanceQuery.GetBalance(ctx, chain, address.Hex(), tokenAddress)
	if err != nil {
		logger.Error().
			Str("chain", chain).
			Str("address", address.Hex()).
			Str("token", tokenAddress).
			Err(err).
			Msg("Failed to get balance from chain")
		return
	}
	
	// 检查缓存中的余额是否发生变化
	cachedBalance, found := wm.manager.cache.GetBalance(chain, address.Hex(), tokenAddress)
	if found && cachedBalance != nil && cachedBalance.Amount != nil && balance.Amount != nil {
		// 如果余额没有变化，不需要更新缓存
		if cachedBalance.Amount.Cmp(balance.Amount) == 0 {
			return
		}
	}
	
	// 更新 manager 的缓存并发布事件
	wm.manager.UpdateBalanceWithEvent(context.Background(), chain, address.Hex(), tokenAddress, balance)
	
	logger.Debug().
		Str("chain", chain).
		Str("address", address.Hex()).
		Str("token", tokenAddress).
		Str("balance", balance.Amount.String()).
		Msg("Balance updated via HTTP polling")
}

// RecordTransaction 记录交易
func (wm *WalletMonitorV2) RecordTransaction(event TransactionEvent) {
	wm.mu.Lock()
	defer wm.mu.Unlock()

	wm.transactions = append(wm.transactions, event)
	
	// 更新统计
	wm.txCount[event.Chain]++
	
	if wm.gasUsage[event.Chain] == nil {
		wm.gasUsage[event.Chain] = big.NewInt(0)
	}
	wm.gasUsage[event.Chain].Add(wm.gasUsage[event.Chain], big.NewInt(int64(event.GasUsed)))
	
	if event.Status == 1 {
		wm.successCount++
	} else {
		wm.failureCount++
	}
	
	// 限制历史记录长度
	if len(wm.transactions) > 1000 {
		wm.transactions = wm.transactions[len(wm.transactions)-1000:]
	}
}

// GetStatus 获取监控状态
func (wm *WalletMonitorV2) GetStatus() *MonitorStatus {
	wm.mu.RLock()
	defer wm.mu.RUnlock()

	successRate := float64(0)
	total := wm.successCount + wm.failureCount
	if total > 0 {
		successRate = float64(wm.successCount) / float64(total) * 100
	}

	// 复制数据避免外部修改
	alerts := make([]BalanceAlert, len(wm.alerts))
	copy(alerts, wm.alerts)
	
	txCount := make(map[string]int)
	for k, v := range wm.txCount {
		txCount[k] = v
	}
	
	gasUsage := make(map[string]*big.Int)
	for k, v := range wm.gasUsage {
		gasUsage[k] = new(big.Int).Set(v)
	}

	return &MonitorStatus{
		Running:          wm.running,
		LastCheckTime:    wm.lastCheckTime,
		BalanceAlerts:    alerts,
		TransactionCount: txCount,
		GasUsage:         gasUsage,
		ErrorCount:       wm.failureCount,
		SuccessRate:      successRate,
	}
}

// SetLowBalanceThreshold 设置低余额阈值
func (wm *WalletMonitorV2) SetLowBalanceThreshold(chain string, threshold *big.Int) {
	wm.mu.Lock()
	defer wm.mu.Unlock()
	
	wm.lowBalanceThresholds[chain] = threshold
}

// GetChainStatus 获取特定链的监控状态
func (wm *WalletMonitorV2) GetChainStatus(chain string) (time.Time, bool) {
	cm, exists := wm.chainMonitors[chain]
	if !exists {
		return time.Time{}, false
	}
	
	cm.mu.RLock()
	defer cm.mu.RUnlock()
	
	// 检查 WebSocket 状态
	wsActive := false
	if wm.manager.wsManager != nil {
		wsActive = wm.manager.wsManager.IsConnected(chain)
	}
	
	return cm.lastCheck, wsActive
}

// GetWebSocketStatus 获取所有链的 WebSocket 连接状态
func (wm *WalletMonitorV2) GetWebSocketStatus() map[string]bool {
	status := make(map[string]bool)
	
	if wm.manager.wsManager == nil {
		return status
	}
	
	for chain := range wm.chainMonitors {
		status[chain] = wm.manager.wsManager.IsConnected(chain)
	}
	
	return status
}
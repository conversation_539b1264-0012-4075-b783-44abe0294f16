package contracts

import (
	"fmt"
	"strings"

	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/ethclient"
)

// Multicall3 represents the Multicall3 contract
type Multicall3 struct {
	client   *ethclient.Client
	contract *bind.BoundContract
	address  common.Address
}

// Call represents a call to be made in a multicall
type Call struct {
	Target   common.Address
	CallData []byte
}

// Result represents the result of a multicall
type Result struct {
	Success    bool
	ReturnData []byte
}

// NewMulticall3 creates a new Multicall3 instance
func NewMulticall3(client *ethclient.Client) (*Multicall3, error) {
	address := common.HexToAddress("******************************************")
	
	// Load and parse the ABI
	data, err := abiFiles.ReadFile("abi/multicall3.json")
	if err != nil {
		return nil, fmt.Errorf("failed to read Multicall3 ABI: %v", err)
	}
	
	parsedABI, err := abi.JSON(strings.NewReader(string(data)))
	if err != nil {
		return nil, fmt.Errorf("failed to parse Multicall3 ABI: %v", err)
	}

	// Create the bound contract
	contract := bind.NewBoundContract(address, parsedABI, client, client, client)

	return &Multicall3{
		client:   client,
		contract: contract,
		address:  address,
	}, nil
}

// Aggregate3 performs multiple calls in a single transaction
func (m *Multicall3) Aggregate3(calls []Call) ([]Result, error) {
	type Call3 struct {
		Target       common.Address
		AllowFailure bool
		CallData     []byte
	}

	// Convert calls to Call3 format
	call3s := make([]Call3, len(calls))
	for i, call := range calls {
		call3s[i] = Call3{
			Target:       call.Target,
			AllowFailure: true, // We allow failures and handle them in the results
			CallData:     call.CallData,
		}
	}

	// Prepare result variable
	var out []interface{}
	err := m.contract.Call(nil, &out, "aggregate3", call3s)
	if err != nil {
		return nil, fmt.Errorf("failed to execute aggregate3: %v", err)
	}

	if len(out) != 1 {
		return nil, fmt.Errorf("unexpected output length: %d", len(out))
	}

	// Convert the output to []Result
	rawResults, ok := out[0].([]struct {
		Success    bool   `json:"success"`
		ReturnData []byte `json:"returnData"`
	})
	if !ok {
		return nil, fmt.Errorf("failed to convert output to results array: %T %+v", out[0], out[0])
	}

	// Convert raw results to Result format
	results := make([]Result, len(rawResults))
	for i, raw := range rawResults {
		results[i] = Result{
			Success:    raw.Success,
			ReturnData: raw.ReturnData,
		}
	}

	return results, nil
}

package contracts

import (
	"fmt"
	"math/big"
	"stargate/internal/config"
	"strings"

	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/accounts/abi/bind"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/ethclient"
)

// MulticallData represents a call to be made in a multicall
type MulticallData struct {
	Address common.Address
	Method  string
	Args    []interface{}
}

// StargateClient manages connections to Stargate contracts
type StargateClient struct {
	rpcPool  *RPCPool // RPC connection pool
	feeLips  map[int64]map[string]*FeeLib
	pools    map[int64]map[string]*Pool
	chains   map[string]config.ChainConfig
	poolABI  abi.ABI // Pre-parsed Pool ABI
	feeLibABI abi.ABI // Pre-parsed FeeLib ABI
}

// NewStargateClient creates a new StargateClient
func NewStargateClient(chains map[string]config.ChainConfig) (*StargateClient, error) {
	// Create RPC pool with max 10 connections per RPC
	rpcPool := NewRPCPool(50)
	feeLips := make(map[int64]map[string]*FeeLib)
	pools := make(map[int64]map[string]*Pool)

	// Initialize RPC pools and contracts for each chain
	for chainName, chain := range chains {
		// Set chain name
		chain.ChainName = chainName

		// Add all RPC URLs to the pool
		if err := rpcPool.AddChain(chain.ChainID, chain.RPCURLs); err != nil {
			return nil, fmt.Errorf("failed to add RPCs for chain %s (%d): %v", chainName, chain.ChainID, err)
		}

		// Get a client for initializing contracts
		client, err := rpcPool.GetHealthyClient(chain.ChainID)
		if err != nil {
			return nil, fmt.Errorf("failed to get client for chain %d: %v", chain.ChainID, err)
		}

		// Initialize fee libraries (now stateless)
		feeLips[chain.ChainID] = make(map[string]*FeeLib)
		for token, address := range chain.Stargate.FeeLibs {
			feeLib := NewFeeLib(common.HexToAddress(address), chain.ChainID)
			feeLips[chain.ChainID][token] = feeLib
		}

		// Initialize pools (now stateless)
		pools[chain.ChainID] = make(map[string]*Pool)
		for token, address := range chain.Stargate.Pools {
			pool := NewPool(common.HexToAddress(address), chain.ChainID)
			pools[chain.ChainID][token] = pool
		}
		
		// Release the client after initializing contracts
		rpcPool.ReleaseClient(chain.ChainID, client)
	}

	// Parse ABIs once
	poolABIStr, err := LoadPoolABI()
	if err != nil {
		return nil, fmt.Errorf("failed to load pool ABI: %v", err)
	}
	poolABI, err := abi.JSON(strings.NewReader(poolABIStr))
	if err != nil {
		return nil, fmt.Errorf("failed to parse pool ABI: %v", err)
	}

	feeLibABIStr, err := LoadFeeLibABI()
	if err != nil {
		return nil, fmt.Errorf("failed to load fee lib ABI: %v", err)
	}
	feeLibABI, err := abi.JSON(strings.NewReader(feeLibABIStr))
	if err != nil {
		return nil, fmt.Errorf("failed to parse fee lib ABI: %v", err)
	}

	return &StargateClient{
		rpcPool:   rpcPool,
		feeLips:   feeLips,
		pools:     pools,
		chains:    chains,
		poolABI:   poolABI,
		feeLibABI: feeLibABI,
	}, nil
}

// Close closes all clients
func (c *StargateClient) Close() {
	if c.rpcPool != nil {
		c.rpcPool.Close()
	}
}

// GetChains returns the chain configurations
func (c *StargateClient) GetChains() map[string]config.ChainConfig {
	return c.chains
}

// GetChainID returns the chain ID for a given chain name
func (c *StargateClient) GetChainID(chainName string) int64 {
	if chain, exists := c.chains[chainName]; exists {
		return chain.ChainID
	}
	return 0
}

// GetFeeLib returns the fee library for a given chain and token
func (c *StargateClient) GetFeeLib(chainID int64, token string) (*FeeLib, error) {
	chainFeeLips, ok := c.feeLips[chainID]
	if !ok {
		return nil, fmt.Errorf("chain %d not found", chainID)
	}

	feeLib, ok := chainFeeLips[token]
	if !ok {
		return nil, fmt.Errorf("token %s not found for chain %d", token, chainID)
	}

	return feeLib, nil
}

// GetPool returns the pool for a given chain and token
func (c *StargateClient) GetPool(chainID int64, token string) (*Pool, error) {
	chainPools, ok := c.pools[chainID]
	if !ok {
		return nil, fmt.Errorf("chain %d not found", chainID)
	}

	pool, ok := chainPools[token]
	if !ok {
		return nil, fmt.Errorf("token %s not found for chain %d", token, chainID)
	}

	return pool, nil
}

// GetRPCPool returns the RPC pool
func (c *StargateClient) GetRPCPool() *RPCPool {
	return c.rpcPool
}

// ExecutePoolCall executes a pool call with failover
func (c *StargateClient) ExecutePoolCall(chainID int64, fn func(*ethclient.Client) error) error {
	return c.rpcPool.ExecuteWithFailover(chainID, fn)
}

// GetHealthyClient returns a healthy client for the chain
func (c *StargateClient) GetHealthyClient(chainID int64) (*ethclient.Client, error) {
	return c.rpcPool.GetHealthyClient(chainID)
}

// ReleaseClient releases a client back to the pool
func (c *StargateClient) ReleaseClient(chainID int64, client *ethclient.Client) {
	c.rpcPool.ReleaseClient(chainID, client)
}

// CallContract executes a contract call with automatic load balancing
func (c *StargateClient) CallContract(chainID int64, address common.Address, contractType string, method string, args ...interface{}) ([]interface{}, error) {
	var results []interface{}
	var contractABI abi.ABI

	// Select the appropriate ABI
	switch contractType {
	case "Pool":
		contractABI = c.poolABI
	case "FeeLib":
		contractABI = c.feeLibABI
	default:
		return nil, fmt.Errorf("unknown contract type: %s", contractType)
	}

	err := c.rpcPool.ExecuteWithFailover(chainID, func(client *ethclient.Client) error {
		contract := bind.NewBoundContract(address, contractABI, client, nil, nil)
		return contract.Call(nil, &results, method, args...)
	})

	return results, err
}

// Multicall executes multiple contract calls in a single request
func (c *StargateClient) Multicall(chainID int64, contractType string, calls []MulticallData) ([]interface{}, error) {
	var results []interface{}
	var contractABI abi.ABI

	// Select the appropriate ABI
	switch contractType {
	case "Pool":
		contractABI = c.poolABI
	case "FeeLib":
		contractABI = c.feeLibABI
	default:
		return nil, fmt.Errorf("unknown contract type: %s", contractType)
	}

	err := c.rpcPool.ExecuteWithFailover(chainID, func(client *ethclient.Client) error {
		multicall, err := NewMulticall3(client)
		if err != nil {
			return fmt.Errorf("failed to create multicall3: %v", err)
		}

		// Prepare calls
		var multicallCalls []Call
		for _, call := range calls {
			callData, err := contractABI.Pack(call.Method, call.Args...)
			if err != nil {
				return fmt.Errorf("failed to pack call %s: %v", call.Method, err)
			}
			multicallCalls = append(multicallCalls, Call{
				Target:   call.Address,
				CallData: callData,
			})
		}

		// Execute multicall
		multicallResults, err := multicall.Aggregate3(multicallCalls)
		if err != nil {
			return fmt.Errorf("failed to execute multicall: %v", err)
		}

		// Unpack results
		for i, result := range multicallResults {
			if !result.Success {
				return fmt.Errorf("call %d failed", i)
			}

			unpacked, err := contractABI.Unpack(calls[i].Method, result.ReturnData)
			if err != nil {
				return fmt.Errorf("failed to unpack result %d: %v", i, err)
			}

			// Handle multiple return values
			for _, item := range unpacked {
				// Convert *big.Int to uint64 for our use case
				if bigInt, ok := item.(*big.Int); ok {
					results = append(results, bigInt.Uint64())
				} else {
					results = append(results, item)
				}
			}
		}

		return nil
	})

	return results, err
}

// CalculatePoolDeficit is a convenience method to calculate pool deficit
func (c *StargateClient) CalculatePoolDeficit(chainID int64, token string) (uint64, error) {
	pool, err := c.GetPool(chainID, token)
	if err != nil {
		return 0, err
	}

	return pool.CalculateDeficit(c)
}

// BatchGetFeeConfigs 批量获取多个目标链的费用配置
func (c *StargateClient) BatchGetFeeConfigs(chainID int64, feeLibAddress common.Address, endpointIDs []uint32) (map[uint32]FeeConfig, error) {
	if len(endpointIDs) == 0 {
		return make(map[uint32]FeeConfig), nil
	}

	// 准备批量调用
	calls := make([]MulticallData, len(endpointIDs))
	for i, eid := range endpointIDs {
		calls[i] = MulticallData{
			Address: feeLibAddress,
			Method:  "feeConfigs",
			Args:    []interface{}{eid},
		}
	}

	// 执行批量调用
	results, err := c.Multicall(chainID, "FeeLib", calls)
	if err != nil {
		return nil, fmt.Errorf("batch get fee configs failed: %v", err)
	}

	// 解析结果 - feeConfigs 返回一个结构体，被展开为多个返回值
	configs := make(map[uint32]FeeConfig)
	expectedFieldsPerCall := 7 // feeConfigs 返回 7 个字段
	
	for i, eid := range endpointIDs {
		baseIdx := i * expectedFieldsPerCall
		if baseIdx+expectedFieldsPerCall > len(results) {
			return nil, fmt.Errorf("unexpected number of results for endpoint %d", eid)
		}

		config := FeeConfig{
			Paused:            results[baseIdx].(bool),
			Zone1UpperBound:   results[baseIdx+1].(uint64),
			Zone2UpperBound:   results[baseIdx+2].(uint64),
			Zone1FeeMillionth: big.NewInt(int64(results[baseIdx+3].(uint64))),
			Zone2FeeMillionth: big.NewInt(int64(results[baseIdx+4].(uint64))),
			Zone3FeeMillionth: big.NewInt(int64(results[baseIdx+5].(uint64))),
			RewardMillionth:   big.NewInt(int64(results[baseIdx+6].(uint64))),
		}
		configs[eid] = config
	}

	return configs, nil
}

// BatchGetPathCredits 批量获取多个路径的 credit
func (c *StargateClient) BatchGetPathCredits(chainID int64, poolAddress common.Address, endpointIDs []uint32) (map[uint32]uint64, error) {
	if len(endpointIDs) == 0 {
		return make(map[uint32]uint64), nil
	}

	// 准备批量调用
	calls := make([]MulticallData, len(endpointIDs))
	for i, eid := range endpointIDs {
		calls[i] = MulticallData{
			Address: poolAddress,
			Method:  "paths",
			Args:    []interface{}{eid},
		}
	}

	// 执行批量调用
	results, err := c.Multicall(chainID, "Pool", calls)
	if err != nil {
		return nil, fmt.Errorf("batch get path credits failed: %v", err)
	}

	// 解析结果
	credits := make(map[uint32]uint64)
	for i, eid := range endpointIDs {
		if i >= len(results) {
			return nil, fmt.Errorf("missing result for endpoint %d", eid)
		}
		credit, ok := results[i].(uint64)
		if !ok {
			return nil, fmt.Errorf("invalid result type for endpoint %d: %T", eid, results[i])
		}
		credits[eid] = credit
	}

	return credits, nil
}

package contracts

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"stargate/pkg/logger"

	"github.com/ethereum/go-ethereum/ethclient"
)

// RPCPool manages multiple RPC connections per chain
type RPCPool struct {
	pools      map[int64][]*PooledClient // chainID -> clients
	mu         sync.RWMutex
	maxPerRPC  int // Maximum concurrent connections per RPC
}

// PooledClient represents a pooled RPC client
type PooledClient struct {
	client      *ethclient.Client
	url         string
	healthy     bool
	activeConns int32 // Atomic counter for active connections
	lastError   time.Time
	lastCheck   time.Time
	mu          sync.Mutex
}

// NewRPCPool creates a new RPC connection pool
func NewRPCPool(maxPerRPC int) *RPCPool {
	if maxPerRPC <= 0 {
		maxPerRPC = 10 // Default max connections per RPC
	}
	return &RPCPool{
		pools:     make(map[int64][]*PooledClient),
		maxPerRPC: maxPerRPC,
	}
}

// <PERSON>d<PERSON><PERSON><PERSON> adds all RPC endpoints for a chain
func (p *RPCPool) AddChain(chainID int64, rpcURLs []string) error {
	p.mu.Lock()
	defer p.mu.Unlock()

	var clients []*PooledClient
	var successCount int

	for _, url := range rpcURLs {
		client, err := ethclient.Dial(url)
		if err != nil {
			// Log error but continue with other RPCs
			logger.Warn().Err(err).Str("url", url).Msg("Failed to connect to RPC")
			continue
		}

		pooledClient := &PooledClient{
			client:    client,
			url:       url,
			healthy:   true,
			lastCheck: time.Now(),
		}
		clients = append(clients, pooledClient)
		successCount++
	}

	if successCount == 0 {
		return fmt.Errorf("failed to connect to any RPC for chain %d", chainID)
	}

	p.pools[chainID] = clients
	logger.Debug().
		Int("connected", successCount).
		Int("total", len(rpcURLs)).
		Int64("chain_id", chainID).
		Msg("Added RPC endpoints")
	return nil
}

// GetHealthyClient returns a healthy client for the chain with load balancing
func (p *RPCPool) GetHealthyClient(chainID int64) (*ethclient.Client, error) {
	p.mu.RLock()
	clients, exists := p.pools[chainID]
	p.mu.RUnlock()

	if !exists || len(clients) == 0 {
		return nil, fmt.Errorf("no RPC clients for chain %d", chainID)
	}

	// Find healthy clients with capacity
	var availableClients []*PooledClient
	for _, pc := range clients {
		pc.mu.Lock()
		if pc.healthy && atomic.LoadInt32(&pc.activeConns) < int32(p.maxPerRPC) {
			availableClients = append(availableClients, pc)
		}
		pc.mu.Unlock()
	}

	if len(availableClients) == 0 {
		// If no healthy clients, try to use any client
		for _, pc := range clients {
			if atomic.LoadInt32(&pc.activeConns) < int32(p.maxPerRPC) {
				availableClients = append(availableClients, pc)
			}
		}
		if len(availableClients) == 0 {
			return nil, fmt.Errorf("no available RPC clients for chain %d (all at capacity)", chainID)
		}
	}

	// Select client with least connections
	var selected *PooledClient
	minConns := int32(p.maxPerRPC + 1)
	for _, pc := range availableClients {
		conns := atomic.LoadInt32(&pc.activeConns)
		if conns < minConns {
			selected = pc
			minConns = conns
		}
	}

	if selected == nil {
		return nil, fmt.Errorf("no suitable RPC client found for chain %d", chainID)
	}

	// Increment active connections
	atomic.AddInt32(&selected.activeConns, 1)
	return selected.client, nil
}

// ReleaseClient decrements the active connection count
func (p *RPCPool) ReleaseClient(chainID int64, client *ethclient.Client) {
	p.mu.RLock()
	clients, exists := p.pools[chainID]
	p.mu.RUnlock()

	if !exists {
		return
	}

	for _, pc := range clients {
		if pc.client == client {
			atomic.AddInt32(&pc.activeConns, -1)
			return
		}
	}
}

// ExecuteWithFailover executes a function with automatic failover
func (p *RPCPool) ExecuteWithFailover(chainID int64, fn func(*ethclient.Client) error) error {
	p.mu.RLock()
	clients, exists := p.pools[chainID]
	p.mu.RUnlock()

	if !exists || len(clients) == 0 {
		return fmt.Errorf("no RPC clients for chain %d", chainID)
	}

	// Shuffle clients to distribute load
	shuffled := make([]*PooledClient, len(clients))
	copy(shuffled, clients)
	rand.Shuffle(len(shuffled), func(i, j int) {
		shuffled[i], shuffled[j] = shuffled[j], shuffled[i]
	})

	var lastErr error
	for _, pc := range shuffled {
		// Skip if at capacity
		if atomic.LoadInt32(&pc.activeConns) >= int32(p.maxPerRPC) {
			continue
		}

		// Try to execute
		atomic.AddInt32(&pc.activeConns, 1)
		err := fn(pc.client)
		atomic.AddInt32(&pc.activeConns, -1)

		if err == nil {
			// Success - mark as healthy
			pc.mu.Lock()
			pc.healthy = true
			pc.mu.Unlock()
			return nil
		}

		// Handle error
		lastErr = err
		if isRateLimitError(err) || isConnectionError(err) {
			pc.mu.Lock()
			pc.healthy = false
			pc.lastError = time.Now()
			pc.mu.Unlock()
		}
	}

	if lastErr != nil {
		return fmt.Errorf("all RPC clients failed: %v", lastErr)
	}
	return errors.New("no available RPC clients")
}

// HealthCheck runs periodic health checks on all clients
func (p *RPCPool) HealthCheck(ctx context.Context) {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			p.performHealthCheck()
		}
	}
}

func (p *RPCPool) performHealthCheck() {
	p.mu.RLock()
	defer p.mu.RUnlock()

	for chainID, clients := range p.pools {
		for _, pc := range clients {
			go p.checkClientHealth(pc, chainID)
		}
	}
}

func (p *RPCPool) checkClientHealth(pc *PooledClient, chainID int64) {
	pc.mu.Lock()
	defer pc.mu.Unlock()

	// Skip if recently checked
	if time.Since(pc.lastCheck) < 15*time.Second {
		return
	}

	pc.lastCheck = time.Now()

	// Try a simple call
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err := pc.client.ChainID(ctx)
	if err != nil {
		pc.healthy = false
		pc.lastError = time.Now()
		logger.Warn().
			Err(err).
			Int64("chain_id", chainID).
			Str("url", pc.url).
			Msg("RPC health check failed")
	} else {
		// If was unhealthy and has been down for a while, mark healthy again
		if !pc.healthy && time.Since(pc.lastError) > 30*time.Second {
			pc.healthy = true
			logger.Info().
				Int64("chain_id", chainID).
				Str("url", pc.url).
				Msg("RPC recovered")
		}
	}
}

// Close closes all RPC connections
func (p *RPCPool) Close() {
	p.mu.Lock()
	defer p.mu.Unlock()

	for _, clients := range p.pools {
		for _, pc := range clients {
			pc.client.Close()
		}
	}
	p.pools = make(map[int64][]*PooledClient)
}

// Helper functions
func isRateLimitError(err error) bool {
	if err == nil {
		return false
	}
	errStr := err.Error()
	return strings.Contains(errStr, "429") || 
		strings.Contains(errStr, "Too Many Requests") ||
		strings.Contains(errStr, "too many requests") ||
		strings.Contains(errStr, "rate limit")
}

func isConnectionError(err error) bool {
	if err == nil {
		return false
	}
	errStr := err.Error()
	return strings.Contains(errStr, "connection refused") ||
		strings.Contains(errStr, "EOF") ||
		strings.Contains(errStr, "timeout") ||
		strings.Contains(errStr, "no such host")
}
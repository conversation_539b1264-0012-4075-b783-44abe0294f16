package contracts

import (
	"fmt"

	"github.com/ethereum/go-ethereum/common"
)

// Pool represents a Stargate Pool contract (stateless)
type Pool struct {
	Address common.Address
	ChainID int64
}

// NewPool creates a new Pool instance
func NewPool(address common.Address, chainID int64) *Pool {
	return &Pool{
		Address: address,
		ChainID: chainID,
	}
}

// Paths returns the credit amount for a given endpoint ID
func (p *Pool) Paths(client *StargateClient, eid uint32) (uint64, error) {
	results, err := client.CallContract(p.ChainID, p.Address, "Pool", "paths", eid)
	if err != nil {
		return 0, fmt.Errorf("failed to get path credit: %v", err)
	}

	if len(results) == 0 {
		return 0, fmt.Errorf("no results returned")
	}

	credit, ok := results[0].(uint64)
	if !ok {
		return 0, fmt.Errorf("invalid result type")
	}

	return credit, nil
}

// GetPoolBalance returns the pool balance
func (p *Pool) GetPoolBalance(client *StargateClient) (uint64, error) {
	results, err := client.CallContract(p.ChainID, p.Address, "Pool", "poolBalance")
	if err != nil {
		return 0, fmt.Errorf("failed to get pool balance: %v", err)
	}

	if len(results) == 0 {
		return 0, fmt.Errorf("no results returned")
	}

	balance, ok := results[0].(uint64)
	if !ok {
		return 0, fmt.Errorf("invalid result type")
	}

	return balance, nil
}

// GetTVL returns the total value locked
func (p *Pool) GetTVL(client *StargateClient) (uint64, error) {
	results, err := client.CallContract(p.ChainID, p.Address, "Pool", "tvl")
	if err != nil {
		return 0, fmt.Errorf("failed to get tvl: %v", err)
	}

	if len(results) == 0 {
		return 0, fmt.Errorf("no results returned")
	}

	tvl, ok := results[0].(uint64)
	if !ok {
		return 0, fmt.Errorf("invalid result type")
	}

	return tvl, nil
}

// GetDeficitOffset returns the deficit offset
func (p *Pool) GetDeficitOffset(client *StargateClient) (uint64, error) {
	results, err := client.CallContract(p.ChainID, p.Address, "Pool", "deficitOffset")
	if err != nil {
		return 0, fmt.Errorf("failed to get deficit offset: %v", err)
	}

	if len(results) == 0 {
		return 0, fmt.Errorf("no results returned")
	}

	offset, ok := results[0].(uint64)
	if !ok {
		return 0, fmt.Errorf("invalid result type")
	}

	return offset, nil
}

// CalculateDeficit calculates the deficit based on poolBalance, tvl and deficitOffset
func (p *Pool) CalculateDeficit(client *StargateClient) (uint64, error) {
	// 使用Multicall批量获取数据
	calls := []MulticallData{
		{Address: p.Address, Method: "poolBalance", Args: []interface{}{}},
		{Address: p.Address, Method: "tvl", Args: []interface{}{}},
		{Address: p.Address, Method: "deficitOffset", Args: []interface{}{}},
	}

	results, err := client.Multicall(p.ChainID, "Pool", calls)
	if err != nil {
		return 0, fmt.Errorf("failed to execute multicall: %v", err)
	}

	if len(results) != 3 {
		return 0, fmt.Errorf("unexpected number of results: %d", len(results))
	}

	// 解析结果
	poolBalance, ok := results[0].(uint64)
	if !ok {
		return 0, fmt.Errorf("failed to parse poolBalance")
	}

	tvl, ok := results[1].(uint64)
	if !ok {
		return 0, fmt.Errorf("failed to parse tvl")
	}

	deficitOffset, ok := results[2].(uint64)
	if !ok {
		return 0, fmt.Errorf("failed to parse deficitOffset")
	}

	// 计算deficit
	t := tvl + deficitOffset
	if t > poolBalance {
		return t - poolBalance, nil
	}
	return 0, nil
}

// BatchPaths 批量获取多个端点的路径 credit
func (p *Pool) BatchPaths(client *StargateClient, endpointIDs []uint32) (map[uint32]uint64, error) {
	return client.BatchGetPathCredits(p.ChainID, p.Address, endpointIDs)
}

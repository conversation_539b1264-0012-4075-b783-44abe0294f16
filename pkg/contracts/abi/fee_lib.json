[{"inputs": [{"internalType": "address", "name": "_stargate", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "FeeLib_InvalidFeeConfiguration", "type": "error"}, {"inputs": [], "name": "FeeLib_Paused", "type": "error"}, {"inputs": [], "name": "FeeLib_Unauthorized", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint32", "name": "eid", "type": "uint32"}, {"components": [{"internalType": "bool", "name": "paused", "type": "bool"}, {"internalType": "uint64", "name": "zone1UpperBound", "type": "uint64"}, {"internalType": "uint64", "name": "zone2UpperBound", "type": "uint64"}, {"internalType": "uint24", "name": "zone1FeeMillionth", "type": "uint24"}, {"internalType": "uint24", "name": "zone2FeeMillionth", "type": "uint24"}, {"internalType": "uint24", "name": "zone3FeeMillionth", "type": "uint24"}, {"internalType": "uint24", "name": "rewardMillionth", "type": "uint24"}], "indexed": false, "internalType": "struct FeeConfig", "name": "config", "type": "tuple"}], "name": "FeeConfigSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint32", "name": "eid", "type": "uint32"}, {"indexed": false, "internalType": "bool", "name": "isPaused", "type": "bool"}], "name": "PausedSet", "type": "event"}, {"inputs": [{"components": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint32", "name": "dstEid", "type": "uint32"}, {"internalType": "uint64", "name": "amountInSD", "type": "uint64"}, {"internalType": "uint64", "name": "deficitSD", "type": "uint64"}, {"internalType": "bool", "name": "toOFT", "type": "bool"}, {"internalType": "bool", "name": "isTaxi", "type": "bool"}], "internalType": "struct FeeP<PERSON>ms", "name": "_params", "type": "tuple"}], "name": "applyFee", "outputs": [{"internalType": "uint64", "name": "amountOutSD", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"components": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint32", "name": "dstEid", "type": "uint32"}, {"internalType": "uint64", "name": "amountInSD", "type": "uint64"}, {"internalType": "uint64", "name": "deficitSD", "type": "uint64"}, {"internalType": "bool", "name": "toOFT", "type": "bool"}, {"internalType": "bool", "name": "isTaxi", "type": "bool"}], "internalType": "struct FeeP<PERSON>ms", "name": "_params", "type": "tuple"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "uint64", "name": "amountOutSD", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint32", "name": "eid", "type": "uint32"}], "name": "feeConfigs", "outputs": [{"internalType": "bool", "name": "paused", "type": "bool"}, {"internalType": "uint64", "name": "zone1UpperBound", "type": "uint64"}, {"internalType": "uint64", "name": "zone2UpperBound", "type": "uint64"}, {"internalType": "uint24", "name": "zone1FeeMillionth", "type": "uint24"}, {"internalType": "uint24", "name": "zone2FeeMillionth", "type": "uint24"}, {"internalType": "uint24", "name": "zone3FeeMillionth", "type": "uint24"}, {"internalType": "uint24", "name": "rewardMillionth", "type": "uint24"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint32", "name": "_dstEid", "type": "uint32"}, {"internalType": "uint64", "name": "_zone1UpperBound", "type": "uint64"}, {"internalType": "uint64", "name": "_zone2UpperBound", "type": "uint64"}, {"internalType": "uint24", "name": "_zone1FeeMillionth", "type": "uint24"}, {"internalType": "uint24", "name": "_zone2FeeMillionth", "type": "uint24"}, {"internalType": "uint24", "name": "_zone3FeeMillionth", "type": "uint24"}, {"internalType": "uint24", "name": "_rewardMillionth", "type": "uint24"}], "name": "setFeeConfig", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint32", "name": "_dstEid", "type": "uint32"}, {"internalType": "bool", "name": "_isPaused", "type": "bool"}], "name": "setPaused", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "stargate", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "stargateType", "outputs": [{"internalType": "enum StargateType", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]
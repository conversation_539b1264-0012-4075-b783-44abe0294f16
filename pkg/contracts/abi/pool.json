[{"inputs": [{"internalType": "string", "name": "_lpTokenName", "type": "string"}, {"internalType": "string", "name": "_lpTokenSymbol", "type": "string"}, {"internalType": "address", "name": "_token", "type": "address"}, {"internalType": "uint8", "name": "_tokenDecimals", "type": "uint8"}, {"internalType": "uint8", "name": "_sharedDecimals", "type": "uint8"}, {"internalType": "address", "name": "_endpoint", "type": "address"}, {"internalType": "address", "name": "_owner", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "InvalidLocalDecimals", "type": "error"}, {"inputs": [], "name": "Path_AlreadyHasCredit", "type": "error"}, {"inputs": [], "name": "Path_InsufficientCredit", "type": "error"}, {"inputs": [], "name": "Path_UnlimitedCredit", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "amountLD", "type": "uint256"}, {"internalType": "uint256", "name": "minAmountLD", "type": "uint256"}], "name": "SlippageExceeded", "type": "error"}, {"inputs": [], "name": "StargatePoolUSDC_BurnAmountExceedsBalance", "type": "error"}, {"inputs": [], "name": "Stargate_InsufficientFare", "type": "error"}, {"inputs": [], "name": "Stargate_InvalidAmount", "type": "error"}, {"inputs": [], "name": "Stargate_InvalidPath", "type": "error"}, {"inputs": [], "name": "Stargate_InvalidTokenDecimals", "type": "error"}, {"inputs": [], "name": "Stargate_LzTokenUnavailable", "type": "error"}, {"inputs": [], "name": "Stargate_OnlyTaxi", "type": "error"}, {"inputs": [], "name": "Stargate_OutflowFailed", "type": "error"}, {"inputs": [], "name": "Stargate_Paused", "type": "error"}, {"inputs": [], "name": "Stargate_RecoverTokenUnsupported", "type": "error"}, {"inputs": [], "name": "Stargate_ReentrantCall", "type": "error"}, {"inputs": [], "name": "Stargate_SlippageTooHigh", "type": "error"}, {"inputs": [], "name": "Stargate_Unauthorized", "type": "error"}, {"inputs": [], "name": "Stargate_UnreceivedTokenNotFound", "type": "error"}, {"inputs": [], "name": "Transfer_ApproveFailed", "type": "error"}, {"inputs": [], "name": "Transfer_TransferFailed", "type": "error"}, {"anonymous": false, "inputs": [{"components": [{"internalType": "address", "name": "feeLib", "type": "address"}, {"internalType": "address", "name": "planner", "type": "address"}, {"internalType": "address", "name": "treasurer", "type": "address"}, {"internalType": "address", "name": "tokenMessaging", "type": "address"}, {"internalType": "address", "name": "creditMessaging", "type": "address"}, {"internalType": "address", "name": "lzToken", "type": "address"}], "indexed": false, "internalType": "struct StargateBase.AddressConfig", "name": "config", "type": "tuple"}], "name": "AddressConfigSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint32", "name": "srcEid", "type": "uint32"}, {"components": [{"internalType": "uint32", "name": "srcEid", "type": "uint32"}, {"internalType": "uint64", "name": "amount", "type": "uint64"}], "indexed": false, "internalType": "struct Credit[]", "name": "credits", "type": "tuple[]"}], "name": "CreditsReceived", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint32", "name": "dstEid", "type": "uint32"}, {"components": [{"internalType": "uint32", "name": "srcEid", "type": "uint32"}, {"internalType": "uint64", "name": "amount", "type": "uint64"}], "indexed": false, "internalType": "struct Credit[]", "name": "credits", "type": "tuple[]"}], "name": "CreditsSent", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "payer", "type": "address"}, {"indexed": true, "internalType": "address", "name": "receiver", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amountLD", "type": "uint256"}], "name": "Deposited", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint32", "name": "dstEid", "type": "uint32"}, {"indexed": false, "internalType": "bool", "name": "oft", "type": "bool"}], "name": "OFTPathSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "guid", "type": "bytes32"}, {"indexed": false, "internalType": "uint32", "name": "srcEid", "type": "uint32"}, {"indexed": true, "internalType": "address", "name": "to<PERSON><PERSON><PERSON>", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amountReceivedLD", "type": "uint256"}], "name": "OFTReceived", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "guid", "type": "bytes32"}, {"indexed": false, "internalType": "uint32", "name": "dstEid", "type": "uint32"}, {"indexed": true, "internalType": "address", "name": "fromAddress", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amountSentLD", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amountReceivedLD", "type": "uint256"}], "name": "OFTSent", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "amountSD", "type": "uint64"}], "name": "Path_CreditBurned", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "bool", "name": "paused", "type": "bool"}], "name": "PauseSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "PlannerFeeWithdrawn", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "payer", "type": "address"}, {"indexed": true, "internalType": "address", "name": "receiver", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amountLD", "type": "uint256"}], "name": "Redeemed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint64", "name": "amountSD", "type": "uint64"}], "name": "TreasuryFeeAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint64", "name": "amountSD", "type": "uint64"}], "name": "TreasuryFeeWithdrawn", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "bytes32", "name": "guid", "type": "bytes32"}, {"indexed": false, "internalType": "uint8", "name": "index", "type": "uint8"}, {"indexed": false, "internalType": "uint32", "name": "srcEid", "type": "uint32"}, {"indexed": false, "internalType": "address", "name": "receiver", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amountLD", "type": "uint256"}, {"indexed": false, "internalType": "bytes", "name": "composeMsg", "type": "bytes"}], "name": "UnreceivedTokenCached", "type": "event"}, {"inputs": [{"internalType": "uint256", "name": "_amountLD", "type": "uint256"}], "name": "addTreasuryFee", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_burnAdmin", "type": "address"}, {"internalType": "uint64", "name": "_burnAllowanceSD", "type": "uint64"}], "name": "allowBurn", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "approvalRequired", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "burnAllowanceSD", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "burnLockedUSDC", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "deficitOffset", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_receiver", "type": "address"}, {"internalType": "uint256", "name": "_amountLD", "type": "uint256"}], "name": "deposit", "outputs": [{"internalType": "uint256", "name": "amountLD", "type": "uint256"}], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "endpoint", "outputs": [{"internalType": "contract ILayerZeroEndpointV2", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getAddressConfig", "outputs": [{"components": [{"internalType": "address", "name": "feeLib", "type": "address"}, {"internalType": "address", "name": "planner", "type": "address"}, {"internalType": "address", "name": "treasurer", "type": "address"}, {"internalType": "address", "name": "tokenMessaging", "type": "address"}, {"internalType": "address", "name": "creditMessaging", "type": "address"}, {"internalType": "address", "name": "lzToken", "type": "address"}], "internalType": "struct StargateBase.AddressConfig", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getTransferGasLimit", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "localEid", "outputs": [{"internalType": "uint32", "name": "", "type": "uint32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "lpToken", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "oftVersion", "outputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}, {"internalType": "uint64", "name": "version", "type": "uint64"}], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint32", "name": "eid", "type": "uint32"}], "name": "paths", "outputs": [{"internalType": "uint64", "name": "credit", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "planner<PERSON>ee", "outputs": [{"internalType": "uint256", "name": "available", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "poolBalance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"components": [{"internalType": "uint32", "name": "dstEid", "type": "uint32"}, {"internalType": "bytes32", "name": "to", "type": "bytes32"}, {"internalType": "uint256", "name": "amountLD", "type": "uint256"}, {"internalType": "uint256", "name": "minAmountLD", "type": "uint256"}, {"internalType": "bytes", "name": "extraOptions", "type": "bytes"}, {"internalType": "bytes", "name": "composeMsg", "type": "bytes"}, {"internalType": "bytes", "name": "oftCmd", "type": "bytes"}], "internalType": "struct SendP<PERSON><PERSON>", "name": "_sendParam", "type": "tuple"}], "name": "quoteOFT", "outputs": [{"components": [{"internalType": "uint256", "name": "minAmountLD", "type": "uint256"}, {"internalType": "uint256", "name": "maxAmountLD", "type": "uint256"}], "internalType": "struct OFTLimit", "name": "limit", "type": "tuple"}, {"components": [{"internalType": "int256", "name": "feeAmountLD", "type": "int256"}, {"internalType": "string", "name": "description", "type": "string"}], "internalType": "struct OFTFeeDetail[]", "name": "oftFeeDetails", "type": "tuple[]"}, {"components": [{"internalType": "uint256", "name": "amountSentLD", "type": "uint256"}, {"internalType": "uint256", "name": "amountReceivedLD", "type": "uint256"}], "internalType": "struct OFTReceipt", "name": "receipt", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"components": [{"internalType": "uint32", "name": "dstEid", "type": "uint32"}, {"internalType": "bytes32", "name": "to", "type": "bytes32"}, {"internalType": "uint256", "name": "amountLD", "type": "uint256"}, {"internalType": "uint256", "name": "minAmountLD", "type": "uint256"}, {"internalType": "bytes", "name": "extraOptions", "type": "bytes"}, {"internalType": "bytes", "name": "composeMsg", "type": "bytes"}, {"internalType": "bytes", "name": "oftCmd", "type": "bytes"}], "internalType": "struct SendP<PERSON><PERSON>", "name": "_sendParam", "type": "tuple"}, {"internalType": "bool", "name": "_payInLzToken", "type": "bool"}], "name": "quoteRedeemSend", "outputs": [{"components": [{"internalType": "uint256", "name": "nativeFee", "type": "uint256"}, {"internalType": "uint256", "name": "lzTokenFee", "type": "uint256"}], "internalType": "struct MessagingFee", "name": "fee", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"components": [{"internalType": "uint32", "name": "dstEid", "type": "uint32"}, {"internalType": "bytes32", "name": "to", "type": "bytes32"}, {"internalType": "uint256", "name": "amountLD", "type": "uint256"}, {"internalType": "uint256", "name": "minAmountLD", "type": "uint256"}, {"internalType": "bytes", "name": "extraOptions", "type": "bytes"}, {"internalType": "bytes", "name": "composeMsg", "type": "bytes"}, {"internalType": "bytes", "name": "oftCmd", "type": "bytes"}], "internalType": "struct SendP<PERSON><PERSON>", "name": "_sendParam", "type": "tuple"}, {"internalType": "bool", "name": "_payInLzToken", "type": "bool"}], "name": "quoteSend", "outputs": [{"components": [{"internalType": "uint256", "name": "nativeFee", "type": "uint256"}, {"internalType": "uint256", "name": "lzTokenFee", "type": "uint256"}], "internalType": "struct MessagingFee", "name": "fee", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint32", "name": "_srcEid", "type": "uint32"}, {"components": [{"internalType": "uint32", "name": "srcEid", "type": "uint32"}, {"internalType": "uint64", "name": "amount", "type": "uint64"}], "internalType": "struct Credit[]", "name": "_credits", "type": "tuple[]"}], "name": "receiveCredits", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"components": [{"internalType": "uint32", "name": "srcEid", "type": "uint32"}, {"internalType": "bytes32", "name": "sender", "type": "bytes32"}, {"internalType": "uint64", "name": "nonce", "type": "uint64"}], "internalType": "struct Origin", "name": "_origin", "type": "tuple"}, {"internalType": "bytes32", "name": "_guid", "type": "bytes32"}, {"internalType": "uint8", "name": "_seatNumber", "type": "uint8"}, {"internalType": "address", "name": "_receiver", "type": "address"}, {"internalType": "uint64", "name": "_amountSD", "type": "uint64"}], "name": "receiveTokenBus", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"components": [{"internalType": "uint32", "name": "srcEid", "type": "uint32"}, {"internalType": "bytes32", "name": "sender", "type": "bytes32"}, {"internalType": "uint64", "name": "nonce", "type": "uint64"}], "internalType": "struct Origin", "name": "_origin", "type": "tuple"}, {"internalType": "bytes32", "name": "_guid", "type": "bytes32"}, {"internalType": "address", "name": "_receiver", "type": "address"}, {"internalType": "uint64", "name": "_amountSD", "type": "uint64"}, {"internalType": "bytes", "name": "_composeMsg", "type": "bytes"}], "name": "receiveTokenTaxi", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_token", "type": "address"}, {"internalType": "address", "name": "_to", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "recoverToken", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_amountLD", "type": "uint256"}, {"internalType": "address", "name": "_receiver", "type": "address"}], "name": "redeem", "outputs": [{"internalType": "uint256", "name": "amountLD", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"components": [{"internalType": "uint32", "name": "dstEid", "type": "uint32"}, {"internalType": "bytes32", "name": "to", "type": "bytes32"}, {"internalType": "uint256", "name": "amountLD", "type": "uint256"}, {"internalType": "uint256", "name": "minAmountLD", "type": "uint256"}, {"internalType": "bytes", "name": "extraOptions", "type": "bytes"}, {"internalType": "bytes", "name": "composeMsg", "type": "bytes"}, {"internalType": "bytes", "name": "oftCmd", "type": "bytes"}], "internalType": "struct SendP<PERSON><PERSON>", "name": "_sendParam", "type": "tuple"}, {"components": [{"internalType": "uint256", "name": "nativeFee", "type": "uint256"}, {"internalType": "uint256", "name": "lzTokenFee", "type": "uint256"}], "internalType": "struct MessagingFee", "name": "_fee", "type": "tuple"}, {"internalType": "address", "name": "_refundAddress", "type": "address"}], "name": "redeemSend", "outputs": [{"components": [{"internalType": "bytes32", "name": "guid", "type": "bytes32"}, {"internalType": "uint64", "name": "nonce", "type": "uint64"}, {"components": [{"internalType": "uint256", "name": "nativeFee", "type": "uint256"}, {"internalType": "uint256", "name": "lzTokenFee", "type": "uint256"}], "internalType": "struct MessagingFee", "name": "fee", "type": "tuple"}], "internalType": "struct MessagingReceipt", "name": "msgReceipt", "type": "tuple"}, {"components": [{"internalType": "uint256", "name": "amountSentLD", "type": "uint256"}, {"internalType": "uint256", "name": "amountReceivedLD", "type": "uint256"}], "internalType": "struct OFTReceipt", "name": "oftReceipt", "type": "tuple"}], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_owner", "type": "address"}], "name": "redeemable", "outputs": [{"internalType": "uint256", "name": "amountLD", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "_guid", "type": "bytes32"}, {"internalType": "uint8", "name": "_index", "type": "uint8"}, {"internalType": "uint32", "name": "_srcEid", "type": "uint32"}, {"internalType": "address", "name": "_receiver", "type": "address"}, {"internalType": "uint256", "name": "_amountLD", "type": "uint256"}, {"internalType": "bytes", "name": "_composeMsg", "type": "bytes"}], "name": "retryReceiveToken", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"components": [{"internalType": "uint32", "name": "dstEid", "type": "uint32"}, {"internalType": "bytes32", "name": "to", "type": "bytes32"}, {"internalType": "uint256", "name": "amountLD", "type": "uint256"}, {"internalType": "uint256", "name": "minAmountLD", "type": "uint256"}, {"internalType": "bytes", "name": "extraOptions", "type": "bytes"}, {"internalType": "bytes", "name": "composeMsg", "type": "bytes"}, {"internalType": "bytes", "name": "oftCmd", "type": "bytes"}], "internalType": "struct SendP<PERSON><PERSON>", "name": "_sendParam", "type": "tuple"}, {"components": [{"internalType": "uint256", "name": "nativeFee", "type": "uint256"}, {"internalType": "uint256", "name": "lzTokenFee", "type": "uint256"}], "internalType": "struct MessagingFee", "name": "_fee", "type": "tuple"}, {"internalType": "address", "name": "_refundAddress", "type": "address"}], "name": "send", "outputs": [{"components": [{"internalType": "bytes32", "name": "guid", "type": "bytes32"}, {"internalType": "uint64", "name": "nonce", "type": "uint64"}, {"components": [{"internalType": "uint256", "name": "nativeFee", "type": "uint256"}, {"internalType": "uint256", "name": "lzTokenFee", "type": "uint256"}], "internalType": "struct MessagingFee", "name": "fee", "type": "tuple"}], "internalType": "struct MessagingReceipt", "name": "msgReceipt", "type": "tuple"}, {"components": [{"internalType": "uint256", "name": "amountSentLD", "type": "uint256"}, {"internalType": "uint256", "name": "amountReceivedLD", "type": "uint256"}], "internalType": "struct OFTReceipt", "name": "oftReceipt", "type": "tuple"}], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint32", "name": "_dstEid", "type": "uint32"}, {"components": [{"internalType": "uint32", "name": "srcEid", "type": "uint32"}, {"internalType": "uint64", "name": "amount", "type": "uint64"}, {"internalType": "uint64", "name": "minAmount", "type": "uint64"}], "internalType": "struct TargetCredit[]", "name": "_credits", "type": "tuple[]"}], "name": "sendCredits", "outputs": [{"components": [{"internalType": "uint32", "name": "srcEid", "type": "uint32"}, {"internalType": "uint64", "name": "amount", "type": "uint64"}], "internalType": "struct Credit[]", "name": "", "type": "tuple[]"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"components": [{"internalType": "uint32", "name": "dstEid", "type": "uint32"}, {"internalType": "bytes32", "name": "to", "type": "bytes32"}, {"internalType": "uint256", "name": "amountLD", "type": "uint256"}, {"internalType": "uint256", "name": "minAmountLD", "type": "uint256"}, {"internalType": "bytes", "name": "extraOptions", "type": "bytes"}, {"internalType": "bytes", "name": "composeMsg", "type": "bytes"}, {"internalType": "bytes", "name": "oftCmd", "type": "bytes"}], "internalType": "struct SendP<PERSON><PERSON>", "name": "_sendParam", "type": "tuple"}, {"components": [{"internalType": "uint256", "name": "nativeFee", "type": "uint256"}, {"internalType": "uint256", "name": "lzTokenFee", "type": "uint256"}], "internalType": "struct MessagingFee", "name": "_fee", "type": "tuple"}, {"internalType": "address", "name": "_refundAddress", "type": "address"}], "name": "sendToken", "outputs": [{"components": [{"internalType": "bytes32", "name": "guid", "type": "bytes32"}, {"internalType": "uint64", "name": "nonce", "type": "uint64"}, {"components": [{"internalType": "uint256", "name": "nativeFee", "type": "uint256"}, {"internalType": "uint256", "name": "lzTokenFee", "type": "uint256"}], "internalType": "struct MessagingFee", "name": "fee", "type": "tuple"}], "internalType": "struct MessagingReceipt", "name": "msgReceipt", "type": "tuple"}, {"components": [{"internalType": "uint256", "name": "amountSentLD", "type": "uint256"}, {"internalType": "uint256", "name": "amountReceivedLD", "type": "uint256"}], "internalType": "struct OFTReceipt", "name": "oftReceipt", "type": "tuple"}, {"components": [{"internalType": "uint72", "name": "ticketId", "type": "uint72"}, {"internalType": "bytes", "name": "passengerBytes", "type": "bytes"}], "internalType": "struct Ticket", "name": "ticket", "type": "tuple"}], "stateMutability": "payable", "type": "function"}, {"inputs": [{"components": [{"internalType": "address", "name": "feeLib", "type": "address"}, {"internalType": "address", "name": "planner", "type": "address"}, {"internalType": "address", "name": "treasurer", "type": "address"}, {"internalType": "address", "name": "tokenMessaging", "type": "address"}, {"internalType": "address", "name": "creditMessaging", "type": "address"}, {"internalType": "address", "name": "lzToken", "type": "address"}], "internalType": "struct StargateBase.AddressConfig", "name": "_config", "type": "tuple"}], "name": "setAddressConfig", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_deficitOffsetLD", "type": "uint256"}], "name": "setDeficitOffset", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint32", "name": "_dstEid", "type": "uint32"}, {"internalType": "bool", "name": "_oft", "type": "bool"}], "name": "setOFTPath", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bool", "name": "_paused", "type": "bool"}], "name": "setPause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_gasLimit", "type": "uint256"}], "name": "setTransferGasLimit", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "sharedDecimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "stargateType", "outputs": [{"internalType": "enum StargateType", "name": "", "type": "uint8"}], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "status", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "token", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "treasuryFee", "outputs": [{"internalType": "uint64", "name": "", "type": "uint64"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "tvl", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "guid", "type": "bytes32"}, {"internalType": "uint8", "name": "index", "type": "uint8"}], "name": "unreceivedTokens", "outputs": [{"internalType": "bytes32", "name": "hash", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "withdrawPlanner<PERSON>ee", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_to", "type": "address"}, {"internalType": "uint64", "name": "_amountSD", "type": "uint64"}], "name": "withdrawTreasuryFee", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]
package contracts

import (
	"embed"
)

//go:embed abi/*.json
var abiFiles embed.FS

// LoadPoolABI loads the Pool contract ABI
func LoadPoolABI() (string, error) {
	data, err := abiFiles.ReadFile("abi/pool.json")
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// LoadFeeLibABI loads the FeeLib contract ABI
func LoadFeeLibABI() (string, error) {
	data, err := abiFiles.ReadFile("abi/fee_lib.json")
	if err != nil {
		return "", err
	}
	return string(data), nil
}

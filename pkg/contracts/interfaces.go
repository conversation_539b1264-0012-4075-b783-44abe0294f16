package contracts

import (
	"math/big"
)

// IPool represents the interface for a Stargate pool
type IPool interface {
	GetAmountOut(chainID *big.Int, amountIn *big.Int) (*big.Int, error)
	GetChainPath(chainID *big.Int) (bool, *big.Int, error)
}

// IFeeLib represents the interface for a Stargate fee library
type IFeeLib interface {
	GetFees(dstChainID *big.Int, functionType uint8, payloadLength *big.Int, airdropAmount *big.Int) (*big.Int, *big.Int, error)
}

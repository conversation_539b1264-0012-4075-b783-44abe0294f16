package contracts

import (
	"fmt"
	"math/big"

	"github.com/ethereum/go-ethereum/common"
)

// FeeConfig represents the fee configuration structure from the contract
type FeeConfig struct {
	Paused            bool
	Zone1UpperBound   uint64
	Zone2UpperBound   uint64
	Zone1FeeMillionth *big.Int
	Zone2FeeMillionth *big.Int
	Zone3FeeMillionth *big.Int
	RewardMillionth   *big.Int
}

// FeeLib represents a Stargate FeeLib contract (stateless)
type FeeLib struct {
	Address common.Address
	ChainID int64
}

// NewFeeLib creates a new FeeLib instance
func NewFeeLib(address common.Address, chainID int64) *FeeLib {
	return &FeeLib{
		Address: address,
		ChainID: chainID,
	}
}

// FeeConfigs returns the fee configuration for a given endpoint ID
func (f *FeeLib) FeeConfigs(client *StargateClient, dstEndpointID uint32) (FeeConfig, error) {
	results, err := client.CallContract(f.ChainID, f.Address, "FeeL<PERSON>", "feeConfigs", dstEndpointID)
	if err != nil {
		return FeeConfig{}, fmt.Errorf("failed to get fee config: %v", err)
	}

	if len(results) != 7 {
		return FeeConfig{}, fmt.Errorf("unexpected number of outputs: got %d, want 7", len(results))
	}

	config := FeeConfig{
		Paused:            results[0].(bool),
		Zone1UpperBound:   results[1].(uint64),
		Zone2UpperBound:   results[2].(uint64),
		Zone1FeeMillionth: results[3].(*big.Int),
		Zone2FeeMillionth: results[4].(*big.Int),
		Zone3FeeMillionth: results[5].(*big.Int),
		RewardMillionth:   results[6].(*big.Int),
	}

	return config, nil
}

// BatchFeeConfigs 批量获取多个端点的费用配置
func (f *FeeLib) BatchFeeConfigs(client *StargateClient, endpointIDs []uint32) (map[uint32]FeeConfig, error) {
	return client.BatchGetFeeConfigs(f.ChainID, f.Address, endpointIDs)
}

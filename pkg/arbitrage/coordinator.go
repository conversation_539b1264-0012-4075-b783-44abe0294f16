package arbitrage

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"errors"
	"fmt"
	"sync"
	"sync/atomic"
	"time"

	"stargate/pkg/events"
	"stargate/pkg/logger"
)

// Config 定义 ArbitrageCoordinator 的配置参数
type Config struct {
	// EventBus 事件总线
	EventBus events.EventBus

	// OpportunityBufferSize 机会缓冲区大小
	OpportunityBufferSize int `json:"opportunity_buffer_size"`

	// DeduplicationWindowMinutes 去重时间窗口（分钟）
	DeduplicationWindowMinutes int `json:"deduplication_window_minutes"`

	// MaxActiveOpportunities 最大同时处理的机会数
	MaxActiveOpportunities int `json:"max_active_opportunities"`

	// ProcessTimeout 单个机会的处理超时时间
	ProcessTimeout time.Duration `json:"process_timeout"`

	// PriorityQueueSize 优先级队列最大大小
	PriorityQueueSize int `json:"priority_queue_size"`

	// HandleOpportunityTimeout HandleOpportunity 方法的超时时间
	HandleOpportunityTimeout time.Duration `json:"handle_opportunity_timeout"`

	// FundingPreparationTimeout 资金准备的超时时间
	FundingPreparationTimeout time.Duration `json:"funding_preparation_timeout"`
}

// DefaultConfig 返回默认配置
func DefaultConfig(eventBus events.EventBus) *Config {
	return &Config{
		EventBus:                   eventBus,
		OpportunityBufferSize:      100,
		DeduplicationWindowMinutes: 5,
		MaxActiveOpportunities:     10,
		ProcessTimeout:             5 * time.Minute,
		PriorityQueueSize:          1000,
		HandleOpportunityTimeout:   5 * time.Second,
		FundingPreparationTimeout:  5 * time.Minute,
	}
}

func (c *Config) Validate() error {
	if c.EventBus == nil {
		return errors.New("event_bus is required")
	}
	if c.OpportunityBufferSize <= 0 {
		return errors.New("opportunity_buffer_size must be positive")
	}
	if c.DeduplicationWindowMinutes <= 0 {
		return errors.New("deduplication_window_minutes must be positive")
	}
	if c.MaxActiveOpportunities <= 0 {
		return errors.New("max_active_opportunities must be positive")
	}
	if c.ProcessTimeout <= 0 {
		return errors.New("process_timeout must be positive")
	}
	if c.PriorityQueueSize <= 0 {
		return errors.New("priority_queue_size must be positive")
	}
	if c.HandleOpportunityTimeout <= 0 {
		return errors.New("handle_opportunity_timeout must be positive")
	}
	if c.FundingPreparationTimeout <= 0 {
		return errors.New("funding_preparation_timeout must be positive")
	}
	return nil
}


// ArbitrageCoordinator 套利协调器，使用事件驱动架构
type ArbitrageCoordinator struct {
	// 配置
	config *Config

	// 事件订阅
	subscription events.Subscription
	activeOpportunities sync.Map

	// 生命周期管理
	ctx        context.Context
	cancel     context.CancelFunc
	wg         sync.WaitGroup
	started    atomic.Bool
	stopped    atomic.Bool

	// 统计信息
	receivedCount   atomic.Uint64
	duplicateCount  atomic.Uint64
	processedCount  atomic.Uint64
	failedCount     atomic.Uint64
}

// NewArbitrageCoordinator 创建新的事件驱动套利协调器
func NewArbitrageCoordinator(config *Config) (*ArbitrageCoordinator, error) {
	// 验证配置
	if config == nil {
		return nil, errors.New("config is required")
	}
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("invalid config: %w", err)
	}

	// 创建协调器
	ctx, cancel := context.WithCancel(context.Background())
	ac := &ArbitrageCoordinator{
		config:          config,
		ctx:             ctx,
		cancel:          cancel,
	}

	logger.Info().
		Int("buffer_size", config.OpportunityBufferSize).
		Int("dedup_window_minutes", config.DeduplicationWindowMinutes).
		Int("max_active", config.MaxActiveOpportunities).
		Msg("ArbitrageCoordinator created")

	return ac, nil
}

// handleArbitrageEvent 处理套利机会事件
func (ac *ArbitrageCoordinator) handleArbitrageEvent(ctx context.Context, event events.Event) error {
	// 检查是否已停止
	if ac.stopped.Load() {
		return errors.New("coordinator is stopped")
	}

	// 类型断言
	arbEvent, ok := event.(*events.StargateArbitrageEvent)
	if !ok {
		return fmt.Errorf("unexpected event type: %T", event)
	}

	// 更新接收计数
	ac.receivedCount.Add(1)

	// 记录收到的事件
	logger.Info().
		Str("event_id", arbEvent.ID()).
		Str("pool_key", arbEvent.PoolKey()).
		Str("source_chain", arbEvent.SourceChain).
		Str("token", arbEvent.TokenSymbol).
		Uint64("total_allocated", arbEvent.TotalAllocatedAmt).
		Int("active_paths", arbEvent.ActivePaths).
		Msg("Received Stargate arbitrage event")


	// 验证机会（去重、有效性检查等）
	if !ac.validateOpportunity(arbEvent) {
		ac.duplicateCount.Add(1)
		logger.Debug().
			Str("event_id", arbEvent.ID()).
			Msg("Opportunity validation failed or duplicate")
		return nil
	}

	// 发布验证通过的机会事件，直接从StargateArbitrageEvent创建
	validatedEvent := events.NewValidatedOpportunityEventFromArbitrage(arbEvent)

	if err := ac.config.EventBus.Publish(ctx, validatedEvent); err != nil {
		logger.Error().
			Err(err).
			Str("opportunity_id", arbEvent.ID()).
			Msg("Failed to publish validated opportunity event")
		return fmt.Errorf("failed to publish validated opportunity: %w", err)
	}

	// 更新处理计数
	ac.processedCount.Add(1)

	logger.Info().
		Str("opportunity_id", arbEvent.ID()).
		Str("source_chain", validatedEvent.SourceChain).
		Str("token", validatedEvent.TokenSymbol).
		Uint64("total_allocated", validatedEvent.TotalAllocatedAmt).
		Int("active_paths", validatedEvent.ActivePaths).
		Msg("Published validated opportunity event")

	return nil
}

// validateOpportunity 验证机会的有效性和去重
func (ac *ArbitrageCoordinator) validateOpportunity(arbEvent *events.StargateArbitrageEvent) bool {
	// 生成机会的唯一标识
	oppHash := ac.generateOpportunityHash(arbEvent)
	
	// 检查是否已经在处理中（去重）
	if _, exists := ac.activeOpportunities.LoadOrStore(oppHash, arbEvent); exists {
		logger.Debug().
			Str("event_id", arbEvent.ID()).
			Str("hash", oppHash).
			Msg("Duplicate opportunity detected")
		return false
	}

	// 设置过期时间
	go func() {
		time.Sleep(time.Duration(ac.config.DeduplicationWindowMinutes) * time.Minute)
		ac.activeOpportunities.Delete(oppHash)
	}()

	// 检查有效期
	if !arbEvent.IsValid() {
		logger.Debug().
			Str("event_id", arbEvent.ID()).
			Msg("Opportunity expired")
		return false
	}

	return true
}

// generateOpportunityHash 生成机会的唯一标识
func (ac *ArbitrageCoordinator) generateOpportunityHash(arbEvent *events.StargateArbitrageEvent) string {
	// 使用池标识和TotalAllocatedAmt生成哈希
	data := fmt.Sprintf("%s:%d", 
		arbEvent.PoolKey(),
		arbEvent.TotalAllocatedAmt,
	)
	hash := sha256.Sum256([]byte(data))
	return hex.EncodeToString(hash[:])
}

// Start 启动协调器
func (ac *ArbitrageCoordinator) Start() error {
	if ac.started.Load() {
		return errors.New("coordinator already started")
	}

	// 订阅套利机会事件
	sub, err := ac.config.EventBus.Subscribe(
		events.EventTypeStargateArbitrage,
		ac.handleArbitrageEvent,
	)
	if err != nil {
		return fmt.Errorf("failed to subscribe to arbitrage events: %w", err)
	}
	ac.subscription = sub

	ac.started.Store(true)

	logger.Info().Msg("ArbitrageCoordinator started with event subscription")
	return nil
}

// Stop 停止协调器
func (ac *ArbitrageCoordinator) Stop() error {
	if !ac.started.Load() || ac.stopped.Load() {
		return errors.New("coordinator not running")
	}

	ac.stopped.Store(true)

	// 取消订阅
	if ac.subscription != nil {
		if err := ac.subscription.Unsubscribe(); err != nil {
			logger.Error().Err(err).Msg("Failed to unsubscribe from events")
		}
	}

	// 取消 context
	ac.cancel()

	// 等待所有 goroutine 退出
	ac.wg.Wait()

	logger.Info().
		Uint64("total_received", ac.receivedCount.Load()).
		Uint64("total_duplicate", ac.duplicateCount.Load()).
		Uint64("total_processed", ac.processedCount.Load()).
		Uint64("total_failed", ac.failedCount.Load()).
		Msg("ArbitrageCoordinator stopped")

	return nil
}

// GetStats 获取统计信息
func (ac *ArbitrageCoordinator) GetStats() map[string]uint64 {
	return map[string]uint64{
		"received":  ac.receivedCount.Load(),
		"duplicate": ac.duplicateCount.Load(),
		"processed": ac.processedCount.Load(),
		"failed":    ac.failedCount.Load(),
	}
}

// IsRunning 返回协调器是否正在运行
func (ac *ArbitrageCoordinator) IsRunning() bool {
	return ac.started.Load() && !ac.stopped.Load()
}

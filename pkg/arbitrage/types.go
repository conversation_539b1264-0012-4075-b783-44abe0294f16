package arbitrage

import (
	"encoding/json"
	"fmt"
	"math/big"
	"time"

	"stargate/pkg/monitor"
	"stargate/pkg/types"
)

// TradeStatus 表示交易的状态
type TradeStatus string

const (
	// StatusPending 待处理状态
	StatusPending TradeStatus = "pending"
	// StatusExecuting 执行中状态
	StatusExecuting TradeStatus = "executing"
	// StatusCompleted 已完成状态
	StatusCompleted TradeStatus = "completed"
	// StatusFailed 失败状态
	StatusFailed TradeStatus = "failed"
	// StatusCancelled 已取消状态
	StatusCancelled TradeStatus = "cancelled"
)

// IsTerminal 返回状态是否为终止状态
func (s TradeStatus) IsTerminal() bool {
	return s == StatusCompleted || s == StatusFailed || s == StatusCancelled
}

// TradeOpportunity extends the shared types.TradeOpportunity with additional arbitrage-specific fields
type TradeOpportunity struct {
	types.TradeOpportunity

	// CheckResult 监控系统发现的原始结果
	CheckResult monitor.CheckResult `json:"check_result"`

	// Priority 优先级（数值越大优先级越高）
	Priority int `json:"priority"`

	// Status 当前状态
	Status TradeStatus `json:"status"`

	// ActualProfit 实际利润（执行后填充）
	ActualProfit *big.Int `json:"actual_profit,omitempty"`

	// ExecutionStartTime 执行开始时间
	ExecutionStartTime *time.Time `json:"execution_start_time,omitempty"`

	// ExecutionEndTime 执行结束时间
	ExecutionEndTime *time.Time `json:"execution_end_time,omitempty"`

	// FailureReason 失败原因
	FailureReason string `json:"failure_reason,omitempty"`
	
	// Timestamp 时间戳
	Timestamp time.Time `json:"timestamp"`
}

// NewTradeOpportunity 创建一个新的交易机会
func NewTradeOpportunity(result monitor.CheckResult) *TradeOpportunity {
	// 计算预估利润（基于 reward）
	profitFloat := result.Reward * 1e6 // 转换为最小单位
	profit := new(big.Int).SetInt64(int64(profitFloat))

	opportunity := &TradeOpportunity{
		CheckResult:     result,
		Priority:        calculatePriority(result),
		Status:          StatusPending,
	}
	
	// Set base fields
	opportunity.ID = generateOpportunityID(result)
	opportunity.SourceChain = result.SrcChain
	opportunity.DestChain = result.DstChain
	opportunity.Token = result.Token
	opportunity.Amount = new(big.Int).SetUint64(result.RewardableAmount)
	opportunity.EstimatedProfit = profit
	opportunity.ProfitUSD = result.Reward
	opportunity.CreatedAt = time.Now()
	opportunity.ExpiresAt = time.Now().Add(5 * time.Minute) // Default expiry
	opportunity.Timestamp = time.Now()
	opportunity.Metadata = map[string]interface{}{
		// Pool IDs can be added here when available
	}
	
	return opportunity
}

// generateOpportunityID 生成机会ID
func generateOpportunityID(result monitor.CheckResult) string {
	return fmt.Sprintf("%s-%s-%s-%d",
		result.SrcChain,
		result.DstChain,
		result.Token,
		time.Now().UnixNano())
}

// calculatePriority 计算优先级
func calculatePriority(result monitor.CheckResult) int {
	// 基于奖励金额计算优先级
	// 可以根据需要调整算法
	priority := int(result.Reward)
	
	// 特殊链路加权
	if result.DstChain == "ethereum" || result.SrcChain == "ethereum" {
		priority += 10 // ETH 链路成本高，优先级提升
	}
	
	return priority
}

// FundingPlan 表示资金计划
type FundingPlan struct {
	// SourceChain 源链
	SourceChain string `json:"source_chain"`

	// DestChain 目标链
	DestChain string `json:"dest_chain"`

	// Token 代币符号
	Token string `json:"token"`

	// TokenAmount 需要的代币数量
	TokenAmount *big.Int `json:"token_amount"`

	// RequiresBinanceWithdraw 是否需要从币安提现
	RequiresBinanceWithdraw bool `json:"requires_binance_withdraw"`

	// BinanceWithdrawAmount 币安提现金额（如果需要）
	BinanceWithdrawAmount *big.Int `json:"binance_withdraw_amount,omitempty"`

	// GasEstimate Gas 费用估算
	GasEstimate *big.Int `json:"gas_estimate"`

	// CurrentBalance 当前链上余额
	CurrentBalance *big.Int `json:"current_balance"`

	// RequiredBalance 需要的总余额（包含 gas）
	RequiredBalance *big.Int `json:"required_balance"`
}

// RiskLevel 风险等级
type RiskLevel string

const (
	RiskLevelLow    RiskLevel = "low"
	RiskLevelMedium RiskLevel = "medium"
	RiskLevelHigh   RiskLevel = "high"
)

// RiskAssessment 表示风险评估结果
type RiskAssessment struct {
	// Level 风险等级
	Level RiskLevel `json:"level"`

	// Score 风险分数（0-100，越高风险越大）
	Score int `json:"score"`

	// Factors 风险因素
	Factors []RiskFactor `json:"factors"`

	// Approved 是否批准执行
	Approved bool `json:"approved"`

	// Reason 批准/拒绝的原因
	Reason string `json:"reason"`

	// Timestamp 评估时间
	Timestamp time.Time `json:"timestamp"`
}

// RiskFactor 风险因素
type RiskFactor struct {
	// Type 因素类型
	Type string `json:"type"`

	// Description 描述
	Description string `json:"description"`

	// Impact 影响程度（0-10）
	Impact int `json:"impact"`
}

// TradeRequest extends the shared types.TradeRequest with additional arbitrage-specific fields
type TradeRequest struct {
	types.TradeRequest

	// FundingPlan 资金计划
	FundingPlan interface{} `json:"funding_plan"` // 使用 interface{} 以支持 funding.FundingPlan

	// RiskAssessment 风险评估
	RiskAssessment *RiskAssessment `json:"risk_assessment"`

	// CreatedAt 创建时间
	CreatedAt time.Time `json:"created_at"`

	// Retries 重试次数
	Retries int `json:"retries"`

	// MaxRetries 最大重试次数
	MaxRetries int `json:"max_retries"`
	
	// Status 当前状态
	Status TradeStatus `json:"status"`
}

// NewTradeRequest 创建新的交易请求
func NewTradeRequest(opportunity *TradeOpportunity, fundingPlan interface{}, riskAssessment *RiskAssessment) *TradeRequest {
	req := &TradeRequest{
		FundingPlan:    fundingPlan,
		RiskAssessment: riskAssessment,
		CreatedAt:      time.Now(),
		MaxRetries:     3,
		Status:         opportunity.Status,
	}
	
	// Convert arbitrage opportunity to types.TradeOpportunity
	baseOpp := &types.TradeOpportunity{
		ID:              opportunity.ID,
		SourceChain:     opportunity.SourceChain,
		DestChain:       opportunity.DestChain,
		Token:           opportunity.Token,
		Amount:          opportunity.Amount,
		EstimatedProfit: opportunity.EstimatedProfit,
		ProfitUSD:       opportunity.ProfitUSD,
		CreatedAt:       opportunity.CreatedAt,
		ExpiresAt:       opportunity.ExpiresAt,
		Metadata:        opportunity.Metadata,
	}
	
	// Set base TradeRequest fields
	req.ID = fmt.Sprintf("req-%s", opportunity.ID)
	req.Opportunity = baseOpp
	req.SourceChain = opportunity.SourceChain
	req.DestChain = opportunity.DestChain
	req.TokenSymbol = opportunity.Token
	req.TokenAmount = opportunity.Amount
	req.Priority = opportunity.Priority
	
	return req
}

// CanRetry 检查是否可以重试
func (tr *TradeRequest) CanRetry() bool {
	return tr.Retries < tr.MaxRetries && 
		tr.Status != StatusCompleted &&
		tr.Status != StatusCancelled
}

// IncrementRetries 增加重试次数
func (tr *TradeRequest) IncrementRetries() {
	tr.Retries++
}

// MarshalJSON 自定义 JSON 序列化以处理 big.Int
func (to *TradeOpportunity) MarshalJSON() ([]byte, error) {
	type Alias TradeOpportunity
	return json.Marshal(&struct {
		EstimatedProfit string `json:"estimated_profit"`
		ActualProfit    string `json:"actual_profit,omitempty"`
		*Alias
	}{
		EstimatedProfit: to.EstimatedProfit.String(),
		ActualProfit:    bigIntToString(to.ActualProfit),
		Alias:           (*Alias)(to),
	})
}

// MarshalJSON 自定义 JSON 序列化以处理 big.Int
func (fp *FundingPlan) MarshalJSON() ([]byte, error) {
	type Alias FundingPlan
	return json.Marshal(&struct {
		TokenAmount           string `json:"token_amount"`
		BinanceWithdrawAmount string `json:"binance_withdraw_amount,omitempty"`
		GasEstimate          string `json:"gas_estimate"`
		CurrentBalance       string `json:"current_balance"`
		RequiredBalance      string `json:"required_balance"`
		*Alias
	}{
		TokenAmount:           fp.TokenAmount.String(),
		BinanceWithdrawAmount: bigIntToString(fp.BinanceWithdrawAmount),
		GasEstimate:          fp.GasEstimate.String(),
		CurrentBalance:       fp.CurrentBalance.String(),
		RequiredBalance:      fp.RequiredBalance.String(),
		Alias:                (*Alias)(fp),
	})
}

// bigIntToString 安全地将 *big.Int 转换为字符串
func bigIntToString(b *big.Int) string {
	if b == nil {
		return ""
	}
	return b.String()
}

// UnmarshalJSON 自定义 JSON 反序列化以处理 big.Int
func (to *TradeOpportunity) UnmarshalJSON(data []byte) error {
	type Alias TradeOpportunity
	aux := &struct {
		EstimatedProfit string `json:"estimated_profit"`
		ActualProfit    string `json:"actual_profit,omitempty"`
		*Alias
	}{
		Alias: (*Alias)(to),
	}

	if err := json.Unmarshal(data, &aux); err != nil {
		return err
	}

	// 解析 EstimatedProfit
	if aux.EstimatedProfit != "" {
		profit := new(big.Int)
		if _, ok := profit.SetString(aux.EstimatedProfit, 10); !ok {
			return fmt.Errorf("invalid estimated_profit value: %s", aux.EstimatedProfit)
		}
		to.EstimatedProfit = profit
	}

	// 解析 ActualProfit
	if aux.ActualProfit != "" {
		profit := new(big.Int)
		if _, ok := profit.SetString(aux.ActualProfit, 10); !ok {
			return fmt.Errorf("invalid actual_profit value: %s", aux.ActualProfit)
		}
		to.ActualProfit = profit
	}

	return nil
}

// UnmarshalJSON 自定义 JSON 反序列化以处理 big.Int
func (fp *FundingPlan) UnmarshalJSON(data []byte) error {
	type Alias FundingPlan
	aux := &struct {
		TokenAmount           string `json:"token_amount"`
		BinanceWithdrawAmount string `json:"binance_withdraw_amount,omitempty"`
		GasEstimate          string `json:"gas_estimate"`
		CurrentBalance       string `json:"current_balance"`
		RequiredBalance      string `json:"required_balance"`
		*Alias
	}{
		Alias: (*Alias)(fp),
	}

	if err := json.Unmarshal(data, &aux); err != nil {
		return err
	}

	// 解析所有 big.Int 字段
	if aux.TokenAmount != "" {
		amount := new(big.Int)
		if _, ok := amount.SetString(aux.TokenAmount, 10); !ok {
			return fmt.Errorf("invalid token_amount value: %s", aux.TokenAmount)
		}
		fp.TokenAmount = amount
	}

	if aux.BinanceWithdrawAmount != "" {
		amount := new(big.Int)
		if _, ok := amount.SetString(aux.BinanceWithdrawAmount, 10); !ok {
			return fmt.Errorf("invalid binance_withdraw_amount value: %s", aux.BinanceWithdrawAmount)
		}
		fp.BinanceWithdrawAmount = amount
	}

	if aux.GasEstimate != "" {
		amount := new(big.Int)
		if _, ok := amount.SetString(aux.GasEstimate, 10); !ok {
			return fmt.Errorf("invalid gas_estimate value: %s", aux.GasEstimate)
		}
		fp.GasEstimate = amount
	}

	if aux.CurrentBalance != "" {
		amount := new(big.Int)
		if _, ok := amount.SetString(aux.CurrentBalance, 10); !ok {
			return fmt.Errorf("invalid current_balance value: %s", aux.CurrentBalance)
		}
		fp.CurrentBalance = amount
	}

	if aux.RequiredBalance != "" {
		amount := new(big.Int)
		if _, ok := amount.SetString(aux.RequiredBalance, 10); !ok {
			return fmt.Errorf("invalid required_balance value: %s", aux.RequiredBalance)
		}
		fp.RequiredBalance = amount
	}

	return nil
}
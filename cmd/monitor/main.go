package main

import (
	"context"
	"flag"
	"os/signal"
	"syscall"
	"time"

	"stargate/internal/config"
	"stargate/pkg/contracts"
	"stargate/pkg/events"
	"stargate/pkg/logger"
	"stargate/pkg/monitor"
	"stargate/pkg/telegram"
)

func main() {
	// Parse command line flags
	configFile := flag.String("config", "configs/config.yaml", "Path to the configuration file")
	logLevel := flag.String("log-level", "info", "Log level (debug, info, warn, error)")
	logFormat := flag.String("log-format", "console", "Log format (console, json)")
	flag.Parse()

	// Initialize logger
	logger.Init(logger.Config{
		Level:     *logLevel,
		Format:    *logFormat,
		AddCaller: true,
	})

	// Load configuration
	configManager := config.GetManager()
	if err := configManager.Load(*configFile); err != nil {
		logger.Fatal().Err(err).Msg("Failed to load configuration")
	}

	// Get configuration
	cfg := config.GetConfig()

	// Create Stargate client
	client, err := contracts.NewStargateClient(cfg.Chains)
	if err != nil {
		logger.Fatal().Err(err).Msg("Failed to create Stargate client")
	}
	defer client.Close()

	// Create event bus
	eventBus := events.NewMemoryBus(1000)
	defer func() {
		shutdownCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		if err := eventBus.Shutdown(shutdownCtx); err != nil {
			logger.Error().Err(err).Msg("Failed to shutdown event bus")
		}
	}()

	// Create Telegram client if configured
	var telegramClient *telegram.Client
	if cfg.Monitor.Telegram.Enabled && cfg.Monitor.Telegram.BotToken != "" && cfg.Monitor.Telegram.ChatID != "" {
		telegramClient = telegram.NewClient(cfg.Monitor.Telegram.BotToken, cfg.Monitor.Telegram.ChatID)
		logger.Info().Msg("Telegram notifications enabled")
	} else {
		logger.Info().Msg("Telegram notifications disabled")
	}

	// Create monitor configuration
	monitorConfig := &monitor.Config{
		Client:          client,
		EventBus:        eventBus,
		TelegramClient:  telegramClient,
		CheckInterval:   cfg.Monitor.CheckInterval,
		MinRewardAmount: 10.0, // TODO: Add to config file
		MinRewardMillionthByChain: map[string]uint64{
			"metis":   800,
			"default": 100,
		}, // TODO: Add to config file
	}

	// Create monitor
	monitor := monitor.NewMonitor(monitorConfig)

	// Subscribe to Stargate arbitrage events (example subscriber)
	sub, err := eventBus.Subscribe(events.EventTypeStargateArbitrage, func(ctx context.Context, event events.Event) error {
		arbEvent, ok := event.(*events.StargateArbitrageEvent)
		if !ok {
			return nil
		}

		logger.Info().
			Str("event_id", arbEvent.ID()).
			Str("pool_key", arbEvent.PoolKey()).
			Int64("version", arbEvent.Version).
			Uint64("pool_deficit", arbEvent.PoolDeficit).
			Uint64("total_allocated", arbEvent.TotalAllocatedAmt).
			Int("active_paths", arbEvent.ActivePaths).
			Int("total_paths", arbEvent.TotalPaths).
			Msg("Stargate arbitrage event received")

		// Log each path
		for _, path := range arbEvent.ArbPaths {
			logger.Info().
				Str("path", path.PathKey()).
				Float64("reward", path.RewardAmount).
				Uint64("allocated", path.AllocatedAmt).
				Int("priority", path.Priority).
				Msg("Arbitrage path in event")
		}

		// Here you would typically trigger the ArbitrageCoordinator
		// For now, we just log the event

		return nil
	})
	if err != nil {
		logger.Fatal().Err(err).Msg("Failed to subscribe to Stargate arbitrage events")
	}
	defer sub.Unsubscribe()

	// Create context that listens for the interrupt signal from the OS
	ctx, stop := signal.NotifyContext(context.Background(), syscall.SIGINT, syscall.SIGTERM)
	defer stop()

	// Start RPC health check in background
	go client.GetRPCPool().HealthCheck(ctx)

	// Start monitor
	logger.Info().
		Dur("check_interval", cfg.Monitor.CheckInterval).
		Msg("Starting event-driven monitor")
	
	go func() {
		if err := monitor.Start(ctx); err != nil && err != context.Canceled {
			logger.Error().Err(err).Msg("Monitor error")
		}
	}()

	// Wait for interrupt signal
	<-ctx.Done()
	logger.Info().Msg("Shutting down gracefully...")

	// Stop monitor
	monitor.Stop()

	// Wait a bit for graceful shutdown
	time.Sleep(2 * time.Second)
}

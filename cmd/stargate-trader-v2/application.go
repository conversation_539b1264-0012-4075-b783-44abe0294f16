package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"sync"
	"syscall"
	"time"
	
	"stargate/internal/config"
	"stargate/pkg/arbitrage"
	"stargate/pkg/contracts"
	"stargate/pkg/events"
	"stargate/pkg/exchange"
	"stargate/pkg/logger"
	"stargate/pkg/monitor"
	"stargate/pkg/risk"
	"stargate/pkg/execution"
	"stargate/pkg/wallet"
	
	"github.com/ethereum/go-ethereum/common"
	"golang.org/x/term"
)

// Application represents the main application with clear layer separation
type Application struct {
	// Configuration
	config *config.Config
	
	// Infrastructure Layer - External connections and low-level services
	infrastructure *InfrastructureLayer
	
	// Service Layer - Business services and managers
	services *ServiceLayer
	
	// Business Layer - Core business logic and coordination
	business *BusinessLayer
	
	// API Layer - External interfaces (health checks, metrics, etc.)
	api *APILayer
	
	// Lifecycle management
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
}

// InfrastructureLayer contains all external connections and low-level services
type InfrastructureLayer struct {
	// Event bus for internal communication
	eventBus events.EventBus
	
	// Blockchain connections
	walletManager  *wallet.Manager
	stargateClient *contracts.StargateClient
	
	// External services
	exchangeClient *exchange.ExchangeClient
}

// ServiceLayer contains business services and managers
type ServiceLayer struct {
	// Financial services
	riskManager *risk.Manager
	
	// Execution services
	executionEngine  *execution.ExecutionEngine
	stargateProtocol *execution.StargateProtocol
}

// BusinessLayer contains core business logic
type BusinessLayer struct {
	// Monitoring and opportunity discovery
	monitor *monitor.Monitor
	
	// Arbitrage coordination
	coordinator *arbitrage.ArbitrageCoordinator
}

// APILayer contains external interfaces
type APILayer struct {
	// Health check server
	healthServer *http.Server
	
	// Metrics endpoint (future)
	// metricsServer *http.Server
	
	// Admin API (future)
	// adminServer *http.Server
}

// NewApplication creates a new application instance
func NewApplication(ctx context.Context, cfg *config.Config) (*Application, error) {
	ctx, cancel := context.WithCancel(ctx)
	
	app := &Application{
		config: cfg,
		ctx:    ctx,
		cancel: cancel,
	}
	
	// Initialize layers in order
	if err := app.initInfrastructure(ctx); err != nil {
		cancel()
		return nil, err
	}
	
	if err := app.initServices(ctx); err != nil {
		cancel()
		return nil, err
	}
	
	if err := app.initBusiness(ctx); err != nil {
		cancel()
		return nil, err
	}
	
	if err := app.initAPI(ctx); err != nil {
		cancel()
		return nil, err
	}
	
	return app, nil
}

// Start starts all application components
func (app *Application) Start() error {
	// Start infrastructure layer
	if err := app.startInfrastructure(); err != nil {
		return err
	}
	
	// Register event subscriptions
	if err := app.registerEventSubscriptions(); err != nil {
		return err
	}
	
	// Start service layer components
	if err := app.startServiceLayer(); err != nil {
		return err
	}
	
	// Start business layer
	if err := app.startBusinessLayer(); err != nil {
		return err
	}
	
	// Start API layer
	if err := app.startAPILayer(); err != nil {
		return err
	}
	
	return nil
}

// Shutdown gracefully shuts down the application
func (app *Application) Shutdown() {
	// Cancel context
	app.cancel()
	
	// Stop layers in reverse order
	app.stopAPILayer()
	app.stopBusinessLayer()
	app.stopServices()
	app.stopInfrastructure()
	
	// Wait for all goroutines
	app.wg.Wait()
}

// initInfrastructure initializes the infrastructure layer
func (app *Application) initInfrastructure(ctx context.Context) error {
	app.infrastructure = &InfrastructureLayer{}
	
	// Initialize event bus
	app.infrastructure.eventBus = events.NewMemoryBus(1000)
	
	// Initialize wallet manager
	walletManager, err := wallet.NewManager(&app.config.Wallet, app.infrastructure.eventBus)
	if err != nil {
		return fmt.Errorf("failed to create wallet manager: %w", err)
	}
	app.infrastructure.walletManager = walletManager
	
	// Load wallet accounts
	if err := app.infrastructure.walletManager.LoadWallet(); err != nil {
		return fmt.Errorf("failed to load wallet: %w", err)
	}
	
	// Initialize private key signer if configured
	if err := app.initializePrivateKeySigner(); err != nil {
		// Log warning but don't fail - fallback to keystore unlocking
		logger.Warn().Err(err).Msg("Failed to initialize private key signer, will use keystore unlocking")
	}
	
	// Initialize chain connections
	chainConfigs := make(map[string]*config.ChainConfig)
	for name, chainCfg := range app.config.Chains {
		cfg := chainCfg
		chainConfigs[name] = &cfg
	}
	
	if err := app.infrastructure.walletManager.InitializeChains(ctx, chainConfigs); err != nil {
		return fmt.Errorf("failed to initialize chains: %w", err)
	}
	
	// Initialize Stargate client
	stargateClient, err := contracts.NewStargateClient(app.config.Chains)
	if err != nil {
		return fmt.Errorf("failed to create Stargate client: %w", err)
	}
	app.infrastructure.stargateClient = stargateClient
	
	// Initialize exchange client (optional)
	if app.config.Exchange.Enabled && app.config.Exchange.Type != "" && app.config.Exchange.APIKey != "" {
		exchangeConfig := &exchange.ExchangeConfig{
			Type:      exchange.ExchangeType(app.config.Exchange.Type),
			APIKey:    app.config.Exchange.APIKey,
			APISecret: app.config.Exchange.APISecret,
			TestNet:   app.config.Exchange.TestNet,
			EventBus:  app.infrastructure.eventBus,
		}
		
		exchangeClient, err := exchange.NewExchangeClient(exchangeConfig)
		if err != nil {
			logger.Warn().Err(err).Msg("Failed to create exchange client, continuing without CEX support")
		} else {
			app.infrastructure.exchangeClient = exchangeClient
		}
	}
	
	logger.Info().Msg("Infrastructure layer initialized")
	return nil
}

// initServices initializes the service layer
func (app *Application) initServices(ctx context.Context) error {
	app.services = &ServiceLayer{}
	
	// Initialize risk manager with direct wallet and exchange access
	app.services.riskManager = risk.NewManager(
		&app.config.Risk,
		app.infrastructure.eventBus,
		app.infrastructure.walletManager,
		app.infrastructure.exchangeClient,
	)
	
	// Initialize StargateProtocol
	stargateProtocol, err := execution.NewStargateProtocol(
		app.infrastructure.walletManager,
		app.infrastructure.stargateClient,
	)
	if err != nil {
		return fmt.Errorf("failed to create stargate protocol: %w", err)
	}
	app.services.stargateProtocol = stargateProtocol
	
	// Initialize ExecutionEngine
	app.services.executionEngine = execution.NewExecutionEngine(
		app.infrastructure.eventBus,
		app.services.stargateProtocol,
		2,                // confirmationBlocks
		10*time.Minute,   // executionTimeout
	)
	
	logger.Info().Msg("Service layer initialized")
	return nil
}

// initBusiness initializes the business layer
func (app *Application) initBusiness(ctx context.Context) error {
	app.business = &BusinessLayer{}
	
	// Initialize monitor
	monitorConfig := &monitor.Config{
		Client:          app.infrastructure.stargateClient,
		EventBus:        app.infrastructure.eventBus,
		TelegramClient:  nil, // TODO: implement telegram client
		CheckInterval:   app.config.Monitor.CheckInterval,
		MinRewardAmount: 10.0, // TODO: Add to config
		MinRewardMillionthByChain: map[string]uint64{
			"metis":   800,
			"default": 100,
		}, // TODO: Add to config
	}
	app.business.monitor = monitor.NewMonitor(monitorConfig)
	
	// Initialize arbitrage coordinator
	coordinatorConfig := &arbitrage.Config{
		EventBus:                   app.infrastructure.eventBus,
		OpportunityBufferSize:      app.config.Arbitrage.OpportunityBufferSize,
		DeduplicationWindowMinutes: app.config.Arbitrage.DeduplicationWindowMinutes,
		MaxActiveOpportunities:     app.config.Arbitrage.MaxActiveOpportunities,
		ProcessTimeout:             app.config.Arbitrage.ProcessTimeout,
		PriorityQueueSize:          app.config.Arbitrage.PriorityQueueSize,
		HandleOpportunityTimeout:   app.config.Arbitrage.HandleOpportunityTimeout,
		FundingPreparationTimeout:  5 * time.Minute, // TODO: Add to config
	}
	
	coordinator, err := arbitrage.NewArbitrageCoordinator(coordinatorConfig)
	if err != nil {
		return fmt.Errorf("failed to create arbitrage coordinator: %w", err)
	}
	app.business.coordinator = coordinator
	
	logger.Info().Msg("Business layer initialized")
	return nil
}

// initAPI initializes the API layer
func (app *Application) initAPI(ctx context.Context) error {
	app.api = &APILayer{}
	
	// Initialize health server if enabled
	if app.config.Health.Enabled {
		mux := http.NewServeMux()
		
		// Health endpoint
		mux.HandleFunc(app.config.Health.Path, app.handleHealth)
		
		// Stats endpoint
		mux.HandleFunc("/stats", app.handleStats)
		
		app.api.healthServer = &http.Server{
			Addr:    fmt.Sprintf(":%d", app.config.Health.Port),
			Handler: mux,
		}
	}
	
	logger.Info().Msg("API layer initialized")
	return nil
}

// registerEventSubscriptions registers all event subscriptions for the event-driven architecture
func (app *Application) registerEventSubscriptions() error {
	// 1. ArbitrageCoordinator already subscribes to StargateArbitrageEvent in its Start method
	// No need to register here
	
	// 2. RiskManager subscribes to ValidatedOpportunityEvent
	sub, err := app.infrastructure.eventBus.Subscribe(
		events.EventTypeValidatedOpportunity,
		app.services.riskManager.HandleValidatedOpportunity,
	)
	if err != nil {
		return fmt.Errorf("failed to subscribe RiskManager to ValidatedOpportunity events: %w", err)
	}
	// Store subscription in RiskManager for cleanup
	app.services.riskManager.SetEventSubscription(sub)
	
	// 2b. RiskManager also subscribes to WithdrawCompletedEvent to re-evaluate pending opportunities
	_, err = app.infrastructure.eventBus.Subscribe(
		events.EventTypeWithdrawCompleted,
		app.services.riskManager.HandleWithdrawCompleted,
	)
	if err != nil {
		return fmt.Errorf("failed to subscribe RiskManager to WithdrawCompleted events: %w", err)
	}
	
	// 2c. RiskManager subscribes to BalanceUpdatedEvent for real-time opportunity processing
	_, err = app.infrastructure.eventBus.Subscribe(
		events.EventTypeBalanceUpdated,
		app.services.riskManager.HandleBalanceUpdated,
	)
	if err != nil {
		return fmt.Errorf("failed to subscribe RiskManager to BalanceUpdated events: %w", err)
	}
	// TODO: Add a method to store multiple subscriptions in RiskManager for proper cleanup
	
	// 3. ExecutionEngine subscribes to StargateExecuteEvent in its Start method
	// 4. ExchangeClient subscribes to CexWithdrawEvent if exchange is available
	if app.infrastructure.exchangeClient != nil {
		_, err = app.infrastructure.eventBus.Subscribe(
			events.EventTypeCexWithdraw,
			app.infrastructure.exchangeClient.HandleCexWithdraw,
		)
		if err != nil {
			return fmt.Errorf("failed to subscribe ExchangeClient to CexWithdraw events: %w", err)
		}
	}
	
	logger.Info().Msg("Event subscriptions registered")
	return nil
}

// initializePrivateKeySigner initializes the private key signer for the wallet
func (app *Application) initializePrivateKeySigner() error {
	primaryAddress := app.infrastructure.walletManager.GetPrimaryAddress()
	if primaryAddress == (common.Address{}) {
		return fmt.Errorf("no primary wallet address found")
	}
	
	// Try to get wallet password from environment
	password := os.Getenv("WALLET_PASSWORD")
	
	// If not in environment, check if we should prompt for it
	if password == "" && app.config.Wallet.EnablePasswordPrompt {
		fmt.Printf("Enter wallet password for %s: ", primaryAddress.Hex())
		passwordBytes, err := term.ReadPassword(int(syscall.Stdin))
		fmt.Println() // New line after password input
		if err != nil {
			return fmt.Errorf("failed to read password: %w", err)
		}
		password = string(passwordBytes)
	}
	
	// If we have a password, initialize private key signer
	if password != "" {
		if err := app.infrastructure.walletManager.InitializePrivateKeySigner(primaryAddress, password); err != nil {
			return err
		}
		logger.Info().
			Str("address", primaryAddress.Hex()).
			Msg("Private key signer initialized for 24/7 operation")
		// Clear password from memory
		password = ""
	} else {
		logger.Info().Msg("No wallet password provided, using keystore unlocking mode")
	}
	
	return nil
}


// startInfrastructure starts the infrastructure layer components
func (app *Application) startInfrastructure() error {
	// Start wallet monitor
	chainConfigs := make(map[string]*config.ChainConfig)
	for name, chainCfg := range app.config.Chains {
		cfg := chainCfg
		chainConfigs[name] = &cfg
	}
	app.infrastructure.walletManager.StartMonitor(app.ctx, chainConfigs)
	
	logger.Info().Msg("Infrastructure layer started")
	return nil
}

// startServiceLayer starts the service layer components
func (app *Application) startServiceLayer() error {
	// Start ExecutionEngine - it will subscribe to StargateExecuteEvent
	if err := app.services.executionEngine.Start(app.ctx); err != nil {
		return fmt.Errorf("failed to start execution engine: %w", err)
	}
	
	// No additional service layer components to start
	
	logger.Info().Msg("Service layer started")
	return nil
}

// startBusinessLayer starts the business layer components
func (app *Application) startBusinessLayer() error {
	// Start arbitrage coordinator
	app.wg.Add(1)
	go func() {
		defer app.wg.Done()
		if err := app.business.coordinator.Start(); err != nil {
			logger.Error().Err(err).Msg("Arbitrage coordinator error")
		}
	}()
	
	// Start monitor
	app.wg.Add(1)
	go func() {
		defer app.wg.Done()
		if err := app.business.monitor.Start(app.ctx); err != nil {
			logger.Error().Err(err).Msg("Monitor error")
		}
	}()
	
	logger.Info().Msg("Business layer started")
	return nil
}

// startAPILayer starts the API layer components
func (app *Application) startAPILayer() error {
	// Start health server if configured
	if app.api.healthServer != nil {
		go func() {
			logger.Info().
				Str("addr", app.api.healthServer.Addr).
				Msg("Health check server started")
			
			if err := app.api.healthServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
				logger.Error().Err(err).Msg("Health check server error")
			}
		}()
	}
	
	logger.Info().Msg("API layer started")
	return nil
}

// stopAPILayer stops the API layer components
func (app *Application) stopAPILayer() {
	if app.api.healthServer != nil {
		shutdownCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		
		if err := app.api.healthServer.Shutdown(shutdownCtx); err != nil {
			logger.Error().Err(err).Msg("Failed to shutdown health server")
		}
	}
	
	logger.Info().Msg("API layer stopped")
}

// stopBusinessLayer stops the business layer components
func (app *Application) stopBusinessLayer() {
	// Stop monitor
	if app.business.monitor != nil {
		app.business.monitor.Stop()
	}
	
	// Stop coordinator
	if app.business.coordinator != nil {
		if err := app.business.coordinator.Stop(); err != nil {
			logger.Warn().Err(err).Msg("Error stopping coordinator")
		}
	}
	
	logger.Info().Msg("Business layer stopped")
}

// stopServices stops the service layer components
func (app *Application) stopServices() {
	// Stop ExecutionEngine
	if app.services.executionEngine != nil {
		if err := app.services.executionEngine.Stop(); err != nil {
			logger.Warn().Err(err).Msg("Error stopping execution engine")
		}
	}
	
	// No additional service layer components to stop
	
	// Stop RiskManager
	if app.services.riskManager != nil {
		if err := app.services.riskManager.Stop(); err != nil {
			logger.Warn().Err(err).Msg("Error stopping risk manager")
		}
	}
	
	logger.Info().Msg("Service layer stopped")
}

// stopInfrastructure stops the infrastructure layer components
func (app *Application) stopInfrastructure() {
	// Stop wallet manager
	if app.infrastructure.walletManager != nil {
		app.infrastructure.walletManager.Stop()
	}
	
	// Shutdown event bus
	if app.infrastructure.eventBus != nil {
		busCtx, busCancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer busCancel()
		if err := app.infrastructure.eventBus.Shutdown(busCtx); err != nil {
			logger.Error().Err(err).Msg("Failed to shutdown event bus")
		}
	}
	
	logger.Info().Msg("Infrastructure layer stopped")
}

// handleHealth handles health check requests
func (app *Application) handleHealth(w http.ResponseWriter, r *http.Request) {
	health := app.checkHealth()
	
	if health.Healthy {
		w.WriteHeader(http.StatusOK)
	} else {
		w.WriteHeader(http.StatusServiceUnavailable)
	}
	
	w.Header().Set("Content-Type", "application/json")
	fmt.Fprintf(w, `{"healthy":%t,"message":"%s","components":%s}`,
		health.Healthy, health.Message, health.ComponentsJSON())
}

// handleStats handles statistics requests
func (app *Application) handleStats(w http.ResponseWriter, r *http.Request) {
	stats := app.business.coordinator.GetStats()
	w.Header().Set("Content-Type", "application/json")
	fmt.Fprintf(w, `{"received":%d,"duplicate":%d,"processed":%d,"failed":%d,"queue_size":%d}`,
		stats["received"], stats["duplicate"], stats["processed"], stats["failed"], stats["queue_size"])
}

// checkHealth checks system health status
func (app *Application) checkHealth() healthStatus {
	status := healthStatus{
		Healthy:    true,
		Message:    "All systems operational",
		Components: make(map[string]bool),
	}
	
	// Check coordinator
	if app.business.coordinator == nil || !app.business.coordinator.IsRunning() {
		status.Healthy = false
		status.Components["coordinator"] = false
		status.Message = "Arbitrage coordinator is not running"
	} else {
		status.Components["coordinator"] = true
		
		// Check queue health
		stats := app.business.coordinator.GetStats()
		if stats["queue_size"] > 900 {
			status.Components["queue"] = false
			status.Message = "Queue is nearly full"
		} else {
			status.Components["queue"] = true
		}
	}
	
	// TODO: Check other components
	// - Database connection
	// - RPC connections
	// - Exchange API connection
	// - Wallet status
	// - Event bus status
	
	return status
}

// Helper functions


// healthStatus represents system health
type healthStatus struct {
	Healthy    bool
	Message    string
	Components map[string]bool
}

func (h *healthStatus) ComponentsJSON() string {
	result := "{"
	first := true
	for name, healthy := range h.Components {
		if !first {
			result += ","
		}
		result += fmt.Sprintf(`"%s":%t`, name, healthy)
		first = false
	}
	result += "}"
	return result
}
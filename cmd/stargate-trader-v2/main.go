package main

import (
	"context"
	"flag"
	"os/signal"
	"syscall"

	"stargate/internal/config"
	"stargate/pkg/logger"
)

// Remove old Application struct - now using the one from application.go

func main() {
	// Parse command line arguments
	args := parseArgs()
	
	// Initialize logging
	initLogger(args)
	
	logger.Info().
		Str("config", args.configFile).
		Msg("Starting Stargate Trader V2 with event-driven architecture...")

	// Load configuration
	cfg := loadConfig(args.configFile)

	// Create context for graceful shutdown
	ctx, stop := signal.NotifyContext(context.Background(), syscall.SIGINT, syscall.SIGTERM)
	defer stop()

	// Initialize application
	app, err := NewApplication(ctx, cfg)
	if err != nil {
		logger.Fatal().Err(err).Msg("Failed to initialize application")
	}

	// Start all components
	if err := app.Start(); err != nil {
		logger.Fatal().Err(err).Msg("Failed to start application")
	}

	// Wait for shutdown signal
	<-ctx.Done()
	logger.Info().Msg("Shutting down Stargate Trader V2...")

	// Graceful shutdown
	app.Shutdown()
	
	logger.Info().Msg("Stargate Trader V2 shutdown complete")
}

// commandLineArgs holds parsed command line arguments
type commandLineArgs struct {
	configFile string
	logLevel   string
	logFormat  string
}

// parseArgs parses command line arguments
func parseArgs() commandLineArgs {
	var args commandLineArgs
	flag.StringVar(&args.configFile, "config", "configs/config.yaml", "Path to the configuration file")
	flag.StringVar(&args.logLevel, "log-level", "info", "Log level (debug, info, warn, error)")
	flag.StringVar(&args.logFormat, "log-format", "console", "Log format (console, json)")
	flag.Parse()
	return args
}

// initLogger initializes the logger with given configuration
func initLogger(args commandLineArgs) {
	logger.Init(logger.Config{
		Level:     args.logLevel,
		Format:    args.logFormat,
		AddCaller: true,
	})
}

// loadConfig loads and returns the configuration
func loadConfig(configFile string) *config.Config {
	configManager := config.GetManager()
	if err := configManager.Load(configFile); err != nil {
		logger.Fatal().Err(err).Msg("Failed to load trader configuration")
	}
	return config.GetConfig()
}

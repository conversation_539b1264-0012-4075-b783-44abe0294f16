package main

import (
	"crypto/ecdsa"
	"flag"
	"fmt"
	"os"
	"path/filepath"
	"syscall"

	"github.com/ethereum/go-ethereum/accounts"
	"github.com/ethereum/go-ethereum/accounts/keystore"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/crypto"
	"golang.org/x/term"
)

func main() {
	var (
		keystorePath = flag.String("keystore", "./keystore", "Path to keystore directory")
		importKey    = flag.String("import", "", "Import private key (hex format without 0x)")
		showAddress  = flag.Bool("show", false, "Show existing addresses in keystore")
	)
	flag.Parse()

	// 确保 keystore 目录存在
	if err := os.MkdirAll(*keystorePath, 0700); err != nil {
		fmt.Printf("Failed to create keystore directory: %v\n", err)
		os.Exit(1)
	}

	// 创建 keystore
	ks := keystore.NewKeyStore(*keystorePath, keystore.StandardScryptN, keystore.StandardScryptP)

	// 如果是显示地址
	if *showAddress {
		showAccounts(ks)
		return
	}

	// 提示输入密码
	fmt.Print("Enter password for new account: ")
	password, err := term.ReadPassword(int(syscall.Stdin))
	fmt.Println()
	if err != nil {
		fmt.Printf("Failed to read password: %v\n", err)
		os.Exit(1)
	}

	// 确认密码
	fmt.Print("Confirm password: ")
	confirmPassword, err := term.ReadPassword(int(syscall.Stdin))
	fmt.Println()
	if err != nil {
		fmt.Printf("Failed to read password: %v\n", err)
		os.Exit(1)
	}

	if string(password) != string(confirmPassword) {
		fmt.Println("Passwords do not match!")
		os.Exit(1)
	}

	// 检查密码强度
	if len(password) < 8 {
		fmt.Println("Password must be at least 8 characters long!")
		os.Exit(1)
	}

	var account accounts.Account

	if *importKey != "" {
		// 导入私钥
		privateKey, err := crypto.HexToECDSA(*importKey)
		if err != nil {
			fmt.Printf("Invalid private key: %v\n", err)
			os.Exit(1)
		}
		
		account, err = ks.ImportECDSA(privateKey, string(password))
		if err != nil {
			fmt.Printf("Failed to import private key: %v\n", err)
			os.Exit(1)
		}
		
		fmt.Println("Private key imported successfully!")
	} else {
		// 创建新账户
		account, err = ks.NewAccount(string(password))
		if err != nil {
			fmt.Printf("Failed to create account: %v\n", err)
			os.Exit(1)
		}
		
		fmt.Println("New account created successfully!")
	}

	// 显示账户信息
	fmt.Printf("\nAddress: %s\n", account.Address.Hex())
	fmt.Printf("Keystore file: %s\n", account.URL.Path)

	// 提示备份
	fmt.Println("\n⚠️  IMPORTANT:")
	fmt.Println("1. Backup your keystore file and password!")
	fmt.Println("2. Never share your keystore file or password!")
	fmt.Println("3. If you lose either, your funds will be lost forever!")
}

func showAccounts(ks *keystore.KeyStore) {
	accounts := ks.Accounts()
	if len(accounts) == 0 {
		fmt.Println("No accounts found in keystore")
		return
	}

	fmt.Println("Accounts in keystore:")
	for i, account := range accounts {
		fmt.Printf("%d. %s\n", i+1, account.Address.Hex())
		fmt.Printf("   File: %s\n", filepath.Base(account.URL.Path))
	}
}

// 生成随机私钥的辅助函数（用于测试）
func generatePrivateKey() (*ecdsa.PrivateKey, error) {
	return crypto.GenerateKey()
}

// 导出私钥的示例（仅用于演示，生产环境慎用）
func exportPrivateKey(ks *keystore.KeyStore, address string, password string) (string, error) {
	account, err := ks.Find(accounts.Account{Address: common.HexToAddress(address)})
	if err != nil {
		return "", err
	}

	keyJSON, err := os.ReadFile(account.URL.Path)
	if err != nil {
		return "", err
	}

	key, err := keystore.DecryptKey(keyJSON, password)
	if err != nil {
		return "", err
	}

	return fmt.Sprintf("%x", crypto.FromECDSA(key.PrivateKey)), nil
}